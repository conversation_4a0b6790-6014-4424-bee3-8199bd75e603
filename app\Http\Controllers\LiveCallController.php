<?php

namespace App\Http\Controllers;

use App\Models\LiveCall;
use App\Models\LiveCallAttendance;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class LiveCallController extends Controller
{
    /**
     * Display a listing of live calls.
     */
    public function index(Request $request)
    {
        $user = Auth::user();

        // Get filter parameters
        $filter = $request->get('filter', 'upcoming');

        // Base query
        $query = LiveCall::with(['mentor', 'attendances']);

        // Apply filters
        switch ($filter) {
            case 'live':
                $query->live();
                break;
            case 'completed':
                $query->completed();
                break;
            case 'my-registered':
                $query->whereHas('attendances', function ($q) use ($user) {
                    $q->where('user_id', $user->id);
                });
                break;
            case 'upcoming':
            default:
                $query->upcoming();
                break;
        }

        $liveCalls = $query->ordered()->paginate(12);

        // Get user's registrations
        $userRegistrations = [];
        if ($user) {
            $userRegistrations = LiveCallAttendance::where('user_id', $user->id)
                ->pluck('live_call_id')
                ->toArray();
        }

        return view('live-calls.index', compact('liveCalls', 'userRegistrations', 'filter'));
    }

    /**
     * Display the specified live call.
     */
    public function show(LiveCall $liveCall)
    {
        $liveCall->load(['mentor', 'attendances.user']);

        $user = Auth::user();
        $canAccess = $user ? $liveCall->canBeAccessedBy($user) : false;
        $isRegistered = $user ? $liveCall->isUserRegistered($user->id) : false;

        $userAttendance = null;
        if ($user && $isRegistered) {
            $userAttendance = $liveCall->attendances()
                ->where('user_id', $user->id)
                ->first();
        }

        return view('live-calls.show', compact(
            'liveCall',
            'canAccess',
            'isRegistered',
            'userAttendance'
        ));
    }

    /**
     * Register user for live call.
     */
    public function register(LiveCall $liveCall)
    {
        $user = Auth::user();

        // Check if user can access this call
        if (!$liveCall->canBeAccessedBy($user)) {
            return back()->withErrors(['access' => 'You need an active subscription to register for this call.']);
        }

        // Check if already registered
        if ($liveCall->isUserRegistered($user->id)) {
            return back()->withErrors(['registration' => 'You are already registered for this call.']);
        }

        // Check if call is full
        if ($liveCall->max_attendees && $liveCall->attendance_count >= $liveCall->max_attendees) {
            return back()->withErrors(['registration' => 'This call is full.']);
        }

        // Check if call is still upcoming
        if (!$liveCall->is_upcoming) {
            return back()->withErrors(['registration' => 'Registration is no longer available for this call.']);
        }

        // Register user
        LiveCallAttendance::create([
            'live_call_id' => $liveCall->id,
            'user_id' => $user->id,
            'registered_at' => now(),
            'status' => 'registered',
        ]);

        return back()->with('success', 'Successfully registered for the live call!');
    }

    /**
     * Unregister user from live call.
     */
    public function unregister(LiveCall $liveCall)
    {
        $user = Auth::user();

        $attendance = LiveCallAttendance::where('live_call_id', $liveCall->id)
            ->where('user_id', $user->id)
            ->first();

        if (!$attendance) {
            return back()->withErrors(['registration' => 'You are not registered for this call.']);
        }

        // Check if call hasn't started yet
        if (!$liveCall->is_upcoming) {
            return back()->withErrors(['registration' => 'Cannot unregister from a call that has already started.']);
        }

        $attendance->delete();

        return back()->with('success', 'Successfully unregistered from the live call.');
    }

    /**
     * Join live call.
     */
    public function join(LiveCall $liveCall)
    {
        $user = Auth::user();

        // Check if user is registered
        if (!$liveCall->isUserRegistered($user->id)) {
            return back()->withErrors(['access' => 'You must be registered to join this call.']);
        }

        // Check if call is live
        if (!$liveCall->is_live) {
            return back()->withErrors(['access' => 'This call is not currently live.']);
        }

        // Mark as attended
        $attendance = LiveCallAttendance::where('live_call_id', $liveCall->id)
            ->where('user_id', $user->id)
            ->first();

        if ($attendance && !$attendance->joined_at) {
            $attendance->markAsAttended();
        }

        // Redirect to meeting URL
        if ($liveCall->meeting_url) {
            return redirect($liveCall->meeting_url);
        }

        return back()->withErrors(['access' => 'Meeting link is not available.']);
    }

    /**
     * Get upcoming calls for dashboard.
     */
    public function upcoming()
    {
        $user = Auth::user();

        $upcomingCalls = LiveCall::upcoming()
            ->with('mentor')
            ->where(function ($query) use ($user) {
                if ($user && $user->activeSubscription) {
                    $planSlug = $user->activeSubscription->subscriptionPlan->slug;
                    $query->whereJsonContains('required_plans', $planSlug)
                          ->orWhereNull('required_plans')
                          ->orWhereJsonLength('required_plans', 0);
                }
            })
            ->take(5)
            ->get();

        return response()->json($upcomingCalls);
    }
}
