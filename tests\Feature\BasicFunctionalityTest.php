<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\User;
use App\Models\Course;
use App\Models\SubscriptionPlan;

class BasicFunctionalityTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test that the home page loads successfully.
     */
    public function test_home_page_loads()
    {
        $response = $this->get('/');
        $response->assertStatus(200);
        $response->assertSee('The Real World');
    }

    /**
     * Test that the courses page loads successfully.
     */
    public function test_courses_page_loads()
    {
        $response = $this->get('/courses');
        $response->assertStatus(200);
    }

    /**
     * Test that the login page loads successfully.
     */
    public function test_login_page_loads()
    {
        $response = $this->get('/login');
        $response->assertStatus(200);
        $response->assertSee('Login');
    }

    /**
     * Test that the register page loads successfully.
     */
    public function test_register_page_loads()
    {
        $response = $this->get('/register');
        $response->assertStatus(200);
        $response->assertSee('Register');
    }

    /**
     * Test user registration functionality.
     */
    public function test_user_can_register()
    {
        $response = $this->post('/register', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
        ]);

        $response->assertRedirect('/dashboard');
        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
        ]);
    }

    /**
     * Test user login functionality.
     */
    public function test_user_can_login()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password123'),
        ]);

        $response = $this->post('/login', [
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);

        $response->assertRedirect('/dashboard');
        $this->assertAuthenticatedAs($user);
    }

    /**
     * Test that authenticated users can access dashboard.
     */
    public function test_authenticated_user_can_access_dashboard()
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)->get('/dashboard');
        $response->assertStatus(200);
    }

    /**
     * Test that mentors can access mentor dashboard.
     */
    public function test_mentor_can_access_mentor_dashboard()
    {
        $mentor = User::factory()->create(['role' => 'mentor']);

        $response = $this->actingAs($mentor)->get('/mentor');
        $response->assertStatus(200);
    }

    /**
     * Test that non-mentors cannot access mentor dashboard.
     */
    public function test_non_mentor_cannot_access_mentor_dashboard()
    {
        $user = User::factory()->create(['role' => 'student']);

        $response = $this->actingAs($user)->get('/mentor');
        $response->assertStatus(403);
    }

    /**
     * Test course creation and relationships.
     */
    public function test_course_relationships_work()
    {
        $mentor = User::factory()->create(['role' => 'mentor']);
        $course = Course::factory()->create(['mentor_id' => $mentor->id]);

        $this->assertEquals($mentor->id, $course->mentor->id);
        $this->assertTrue($mentor->courses->contains($course));
    }

    /**
     * Test subscription plan functionality.
     */
    public function test_subscription_plans_exist()
    {
        SubscriptionPlan::factory()->create([
            'name' => 'Test Plan',
            'monthly_price' => 49.99,
            'is_active' => true,
        ]);

        $this->assertDatabaseHas('subscription_plans', [
            'name' => 'Test Plan',
            'is_active' => true,
        ]);
    }

    /**
     * Test that subscription page loads.
     */
    public function test_subscription_page_loads()
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)->get('/subscriptions');
        $response->assertStatus(200);
    }

    /**
     * Test that mentors page loads.
     */
    public function test_mentors_page_loads()
    {
        $response = $this->get('/mentors');
        $response->assertStatus(200);
    }

    /**
     * Test that mentor application page loads.
     */
    public function test_mentor_application_page_loads()
    {
        $response = $this->get('/mentors/apply');
        $response->assertStatus(200);
        $response->assertSee('Become a Mentor');
    }

    /**
     * Test that FAQ page loads.
     */
    public function test_faq_page_loads()
    {
        $response = $this->get('/faq');
        $response->assertStatus(200);
    }

    /**
     * Test that contact page loads.
     */
    public function test_contact_page_loads()
    {
        $response = $this->get('/contact');
        $response->assertStatus(200);
    }

    /**
     * Test that payment test page loads.
     */
    public function test_payment_test_page_loads()
    {
        $response = $this->get('/test-payment');
        $response->assertStatus(200);
        $response->assertSee('Payment System Test');
    }
}
