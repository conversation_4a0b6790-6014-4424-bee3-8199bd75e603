<?php

namespace Tests\Unit;

use App\Models\User;
use App\Models\Role;
use App\Models\Course;
use App\Models\Subscription;
use App\Models\SubscriptionPlan;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;

class UserTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create basic roles
        Role::create(['name' => 'user', 'display_name' => 'User']);
        Role::create(['name' => 'admin', 'display_name' => 'Admin']);
        Role::create(['name' => 'mentor', 'display_name' => 'Mentor']);
    }

    /** @test */
    public function it_can_create_a_user()
    {
        $user = User::factory()->create([
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
        ]);

        $this->assertDatabaseHas('users', [
            'first_name' => '<PERSON>',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
        ]);

        $this->assertEquals('<PERSON>', $user->name);
    }

    /** @test */
    public function it_hashes_password_when_creating_user()
    {
        $user = User::factory()->create([
            'password' => 'password123',
        ]);

        $this->assertTrue(Hash::check('password123', $user->password));
        $this->assertNotEquals('password123', $user->password);
    }

    /** @test */
    public function it_can_assign_roles_to_user()
    {
        $user = User::factory()->create();
        $adminRole = Role::where('name', 'admin')->first();
        
        $user->roles()->attach($adminRole);

        $this->assertTrue($user->hasRole('admin'));
        $this->assertFalse($user->hasRole('mentor'));
    }

    /** @test */
    public function it_can_check_multiple_roles()
    {
        $user = User::factory()->create();
        $adminRole = Role::where('name', 'admin')->first();
        $mentorRole = Role::where('name', 'mentor')->first();
        
        $user->roles()->attach([$adminRole->id, $mentorRole->id]);

        $this->assertTrue($user->hasAnyRole(['admin', 'mentor']));
        $this->assertTrue($user->hasAllRoles(['admin', 'mentor']));
        $this->assertFalse($user->hasAllRoles(['admin', 'mentor', 'user']));
    }

    /** @test */
    public function it_can_have_active_subscription()
    {
        $user = User::factory()->create();
        $plan = SubscriptionPlan::factory()->create([
            'name' => 'Premium',
            'price' => 49.99,
        ]);

        $subscription = Subscription::factory()->create([
            'user_id' => $user->id,
            'subscription_plan_id' => $plan->id,
            'status' => 'active',
            'starts_at' => now(),
            'ends_at' => now()->addMonth(),
        ]);

        $this->assertNotNull($user->activeSubscription);
        $this->assertEquals('Premium', $user->activeSubscription->subscriptionPlan->name);
        $this->assertTrue($user->hasActiveSubscription());
    }

    /** @test */
    public function it_returns_null_for_inactive_subscription()
    {
        $user = User::factory()->create();
        $plan = SubscriptionPlan::factory()->create();

        Subscription::factory()->create([
            'user_id' => $user->id,
            'subscription_plan_id' => $plan->id,
            'status' => 'cancelled',
            'starts_at' => now()->subMonth(),
            'ends_at' => now()->subDay(),
        ]);

        $this->assertNull($user->activeSubscription);
        $this->assertFalse($user->hasActiveSubscription());
    }

    /** @test */
    public function it_can_enroll_in_courses()
    {
        $user = User::factory()->create();
        $course = Course::factory()->create([
            'title' => 'Laravel Mastery',
            'is_published' => true,
        ]);

        $user->enrollInCourse($course);

        $this->assertTrue($user->isEnrolledIn($course));
        $this->assertDatabaseHas('user_progress', [
            'user_id' => $user->id,
            'course_id' => $course->id,
        ]);
    }

    /** @test */
    public function it_cannot_enroll_in_unpublished_course()
    {
        $user = User::factory()->create();
        $course = Course::factory()->create([
            'is_published' => false,
        ]);

        $result = $user->enrollInCourse($course);

        $this->assertFalse($result);
        $this->assertFalse($user->isEnrolledIn($course));
    }

    /** @test */
    public function it_can_calculate_course_progress()
    {
        $user = User::factory()->create();
        $course = Course::factory()->create();
        
        // Create lessons for the course
        $lessons = \App\Models\Lesson::factory()->count(4)->create([
            'course_id' => $course->id,
        ]);

        // Enroll user and complete 2 out of 4 lessons
        $user->enrollInCourse($course);
        
        foreach ($lessons->take(2) as $lesson) {
            \App\Models\UserProgress::create([
                'user_id' => $user->id,
                'course_id' => $course->id,
                'lesson_id' => $lesson->id,
                'is_completed' => true,
                'completed_at' => now(),
            ]);
        }

        $progress = $user->getCourseProgress($course);
        $this->assertEquals(50, $progress); // 2/4 = 50%
    }

    /** @test */
    public function it_can_get_completed_courses()
    {
        $user = User::factory()->create();
        $completedCourse = Course::factory()->create(['title' => 'Completed Course']);
        $incompleteCourse = Course::factory()->create(['title' => 'Incomplete Course']);

        // Mark one course as completed
        \App\Models\UserProgress::create([
            'user_id' => $user->id,
            'course_id' => $completedCourse->id,
            'is_completed' => true,
            'completed_at' => now(),
        ]);

        // Mark other course as incomplete
        \App\Models\UserProgress::create([
            'user_id' => $user->id,
            'course_id' => $incompleteCourse->id,
            'is_completed' => false,
        ]);

        $completedCourses = $user->completedCourses;
        
        $this->assertCount(1, $completedCourses);
        $this->assertEquals('Completed Course', $completedCourses->first()->title);
    }

    /** @test */
    public function it_can_check_subscription_access_to_course()
    {
        $user = User::factory()->create();
        $plan = SubscriptionPlan::factory()->create(['name' => 'Premium']);
        
        // Create active subscription
        Subscription::factory()->create([
            'user_id' => $user->id,
            'subscription_plan_id' => $plan->id,
            'status' => 'active',
            'starts_at' => now(),
            'ends_at' => now()->addMonth(),
        ]);

        $course = Course::factory()->create([
            'required_plans' => ['Premium'],
        ]);

        $this->assertTrue($user->hasAccessToCourse($course));
    }

    /** @test */
    public function it_denies_access_without_required_subscription()
    {
        $user = User::factory()->create();
        
        $course = Course::factory()->create([
            'required_plans' => ['Premium'],
        ]);

        $this->assertFalse($user->hasAccessToCourse($course));
    }

    /** @test */
    public function it_can_update_last_login()
    {
        $user = User::factory()->create([
            'last_login_at' => null,
        ]);

        $user->updateLastLogin();

        $this->assertNotNull($user->fresh()->last_login_at);
        $this->assertTrue($user->fresh()->last_login_at->isToday());
    }

    /** @test */
    public function it_can_be_activated_and_deactivated()
    {
        $user = User::factory()->create(['is_active' => true]);

        $user->deactivate();
        $this->assertFalse($user->fresh()->is_active);

        $user->activate();
        $this->assertTrue($user->fresh()->is_active);
    }

    /** @test */
    public function it_generates_full_name_correctly()
    {
        $user = User::factory()->create([
            'first_name' => 'John',
            'last_name' => 'Doe',
        ]);

        $this->assertEquals('John Doe', $user->getFullNameAttribute());
        $this->assertEquals('John Doe', $user->full_name);
    }

    /** @test */
    public function it_can_get_avatar_url()
    {
        $user = User::factory()->create([
            'avatar' => 'avatars/user123.jpg',
        ]);

        $this->assertStringContains('avatars/user123.jpg', $user->getAvatarUrlAttribute());
    }

    /** @test */
    public function it_returns_default_avatar_when_none_set()
    {
        $user = User::factory()->create(['avatar' => null]);

        $avatarUrl = $user->getAvatarUrlAttribute();
        $this->assertStringContains('ui-avatars.com', $avatarUrl);
        $this->assertStringContains(urlencode($user->name), $avatarUrl);
    }
}
