<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CourseResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'slug' => $this->slug,
            'description' => $this->description,
            'content' => $this->when($request->routeIs('api.courses.show'), $this->content),
            'thumbnail' => $this->thumbnail ? asset('storage/' . $this->thumbnail) : null,
            'difficulty_level' => $this->difficulty_level,
            'duration_hours' => $this->duration_hours,
            'is_free' => $this->is_free,
            'is_featured' => $this->is_featured,
            'category' => new CategoryResource($this->whenLoaded('category')),
            'mentor' => new UserResource($this->whenLoaded('mentor')),
            'lessons' => LessonResource::collection($this->whenLoaded('lessons')),
            'ratings' => [
                'average' => $this->courseRatings ? round($this->courseRatings->avg('rating'), 1) : 0,
                'count' => $this->courseRatings ? $this->courseRatings->count() : 0,
            ],
            'enrollment_count' => $this->when(
                $this->relationLoaded('userProgress'),
                $this->userProgress->groupBy('user_id')->count()
            ),
            'user_progress' => $this->when(
                isset($this->user_progress),
                $this->user_progress
            ),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
