<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create roles
        $adminRole = Role::firstOrCreate(['name' => 'admin']);
        $mentorRole = Role::firstOrCreate(['name' => 'mentor']);
        $userRole = Role::firstOrCreate(['name' => 'user']);

        // Create permissions
        $permissions = [
            // User management
            'manage users',
            'view users',
            'create users',
            'edit users',
            'delete users',

            // Course management
            'manage courses',
            'view courses',
            'create courses',
            'edit courses',
            'delete courses',
            'publish courses',

            // Lesson management
            'manage lessons',
            'view lessons',
            'create lessons',
            'edit lessons',
            'delete lessons',

            // Live calls
            'manage live calls',
            'view live calls',
            'create live calls',
            'edit live calls',
            'delete live calls',
            'attend live calls',

            // Community
            'manage community',
            'view community',
            'post in community',
            'moderate community',

            // Subscriptions
            'manage subscriptions',
            'view subscriptions',

            // Analytics
            'view analytics',

            // Settings
            'manage settings',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Assign permissions to roles
        $adminRole->givePermissionTo(Permission::all());

        $mentorRole->givePermissionTo([
            'view courses',
            'create courses',
            'edit courses',
            'publish courses',
            'manage lessons',
            'view lessons',
            'create lessons',
            'edit lessons',
            'manage live calls',
            'view live calls',
            'create live calls',
            'edit live calls',
            'attend live calls',
            'view community',
            'post in community',
            'moderate community',
        ]);

        $userRole->givePermissionTo([
            'view courses',
            'view lessons',
            'attend live calls',
            'view community',
            'post in community',
        ]);
    }
}
