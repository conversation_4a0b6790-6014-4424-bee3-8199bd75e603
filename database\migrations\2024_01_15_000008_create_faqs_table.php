<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('faqs', function (Blueprint $table) {
            $table->id();
            $table->string('question');
            $table->longText('answer');
            $table->foreignId('category_id')->constrained('faq_categories')->onDelete('cascade');
            $table->text('keywords')->nullable(); // For search functionality
            $table->string('icon')->nullable();
            $table->integer('order')->default(0);
            $table->boolean('is_published')->default(true);
            $table->integer('views')->default(0);
            $table->integer('helpful_votes')->default(0);
            $table->integer('unhelpful_votes')->default(0);
            $table->json('related_links')->nullable(); // Array of related links
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->index('category_id');
            $table->index('is_published');
            $table->index('views');
            $table->index('helpful_votes');
            $table->index('order');
            $table->fullText(['question', 'answer', 'keywords']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('faqs');
    }
};
