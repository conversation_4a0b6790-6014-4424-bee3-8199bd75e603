<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "Testing courses with categories:\n";

try {
    $courses = App\Models\Course::with(['categoryRelation', 'mentor'])->get();
    
    foreach ($courses as $course) {
        echo "Course: {$course->title}\n";
        echo "Category: " . ($course->category ? $course->category->name : 'No category') . "\n";
        echo "Mentor: " . ($course->mentor ? $course->mentor->name : 'No mentor') . "\n";
        echo "---\n";
    }
    
    echo "\nTesting categories for dropdown:\n";
    $categories = App\Models\Category::where('is_active', true)->get();
    
    foreach ($categories as $category) {
        echo "ID: {$category->id} | Name: {$category->name}\n";
    }
    
    echo "\nTest completed successfully!\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . ":" . $e->getLine() . "\n";
}
