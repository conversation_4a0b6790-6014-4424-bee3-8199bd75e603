<?php

namespace Database\Seeders;

use App\Models\Category;
use Illuminate\Database\Seeder;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'E-commerce',
                'slug' => 'ecommerce',
                'description' => 'Learn how to build and scale profitable e-commerce businesses',
                'icon' => 'shopping-cart',
                'color' => '#3B82F6',
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'name' => 'Copywriting',
                'slug' => 'copywriting',
                'description' => 'Master the art of persuasive writing and sales copy',
                'icon' => 'edit',
                'color' => '#10B981',
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'name' => 'Cryptocurrency',
                'slug' => 'cryptocurrency',
                'description' => 'Understand blockchain technology and crypto investments',
                'icon' => 'currency-bitcoin',
                'color' => '#F59E0B',
                'is_active' => true,
                'sort_order' => 3,
            ],
            [
                'name' => 'Stocks & Trading',
                'slug' => 'stocks-trading',
                'description' => 'Learn professional trading strategies and market analysis',
                'icon' => 'trending-up',
                'color' => '#EF4444',
                'is_active' => true,
                'sort_order' => 4,
            ],
            [
                'name' => 'Amazon FBA',
                'slug' => 'amazon-fba',
                'description' => 'Build a successful Amazon FBA business from scratch',
                'icon' => 'package',
                'color' => '#8B5CF6',
                'is_active' => true,
                'sort_order' => 5,
            ],
            [
                'name' => 'Freelancing',
                'slug' => 'freelancing',
                'description' => 'Monetize your skills through high-value freelancing',
                'icon' => 'briefcase',
                'color' => '#06B6D4',
                'is_active' => true,
                'sort_order' => 6,
            ],
            [
                'name' => 'Content Creation',
                'slug' => 'content-creation',
                'description' => 'Build audiences and monetize through content',
                'icon' => 'video-camera',
                'color' => '#F97316',
                'is_active' => true,
                'sort_order' => 7,
            ],
            [
                'name' => 'Business Management',
                'slug' => 'business-management',
                'description' => 'Learn to manage and scale successful businesses',
                'icon' => 'office-building',
                'color' => '#6366F1',
                'is_active' => true,
                'sort_order' => 8,
            ],
            [
                'name' => 'Real Estate',
                'slug' => 'real-estate',
                'description' => 'Invest and profit from real estate opportunities',
                'icon' => 'home',
                'color' => '#84CC16',
                'is_active' => true,
                'sort_order' => 9,
            ],
            [
                'name' => 'Fitness & Health',
                'slug' => 'fitness-health',
                'description' => 'Optimize your physical and mental performance',
                'icon' => 'heart',
                'color' => '#EC4899',
                'is_active' => true,
                'sort_order' => 10,
            ],
        ];

        foreach ($categories as $categoryData) {
            Category::firstOrCreate(
                ['slug' => $categoryData['slug']],
                $categoryData
            );
        }

        $this->command->info('Categories seeded successfully!');
    }
}
