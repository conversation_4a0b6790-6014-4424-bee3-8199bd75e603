<?php

namespace App\Http\Controllers;

use App\Models\SubscriptionPlan;
use App\Models\Course;
use App\Models\Testimonial;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    /**
     * Show the homepage.
     */
    public function index()
    {
        $subscriptionPlans = SubscriptionPlan::active()
            ->ordered()
            ->get();

        $featuredCourses = Course::published()
            ->featured()
            ->with('mentor')
            ->ordered()
            ->take(6)
            ->get();

        $testimonials = Testimonial::where('is_approved', true)
            ->where('is_featured', true)
            ->orderBy('sort_order')
            ->take(10)
            ->get();

        return view('home', compact('subscriptionPlans', 'featuredCourses', 'testimonials'));
    }

    /**
     * Show the pricing page.
     */
    public function pricing()
    {
        $subscriptionPlans = SubscriptionPlan::active()
            ->ordered()
            ->get();

        return view('pricing', compact('subscriptionPlans'));
    }

    /**
     * Show the about page.
     */
    public function about()
    {
        return view('about');
    }

    /**
     * Show the contact page.
     */
    public function contact()
    {
        return view('contact');
    }
}
