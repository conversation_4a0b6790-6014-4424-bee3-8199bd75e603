<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class LiveCall extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'description',
        'mentor_id',
        'course_id',
        'scheduled_at',
        'duration_minutes',
        'meeting_url',
        'meeting_id',
        'meeting_password',
        'max_attendees',
        'required_plans',
        'required_subscription_plans',
        'status',
        'recording_url',
        'is_featured',
        'is_recorded',
        'started_at',
        'ended_at',
        'cancelled_at',
    ];

    protected $casts = [
        'scheduled_at' => 'datetime',
        'started_at' => 'datetime',
        'ended_at' => 'datetime',
        'cancelled_at' => 'datetime',
        'required_plans' => 'array',
        'required_subscription_plans' => 'array',
        'is_featured' => 'boolean',
        'is_recorded' => 'boolean',
    ];

    /**
     * Get the mentor that hosts the live call.
     */
    public function mentor()
    {
        return $this->belongsTo(User::class, 'mentor_id');
    }

    /**
     * Get the course associated with the live call.
     */
    public function course()
    {
        return $this->belongsTo(Course::class, 'course_id');
    }

    /**
     * Get the attendances for the live call.
     */
    public function attendances()
    {
        return $this->hasMany(LiveCallAttendance::class);
    }

    /**
     * Get registered attendees.
     */
    public function registeredAttendees()
    {
        return $this->belongsToMany(User::class, 'live_call_attendances')
            ->withPivot(['registered_at', 'joined_at', 'left_at', 'duration_minutes', 'status'])
            ->withTimestamps();
    }

    /**
     * Check if user can access this live call.
     */
    public function canBeAccessedBy(User $user): bool
    {
        // Check if user has active subscription
        $subscription = $user->activeSubscription;
        if (!$subscription) {
            return false;
        }

        // If no specific plans required, any active subscription works
        if (empty($this->required_plans)) {
            return true;
        }

        // Check if user's plan is in required plans
        return in_array($subscription->subscriptionPlan->slug, $this->required_plans);
    }

    /**
     * Check if user is registered for this call.
     */
    public function isUserRegistered($userId): bool
    {
        return $this->attendances()->where('user_id', $userId)->exists();
    }

    /**
     * Get attendance count.
     */
    public function getAttendanceCountAttribute(): int
    {
        return $this->attendances()->count();
    }

    /**
     * Get participants count (alias for attendance count).
     */
    public function getParticipantsCountAttribute(): int
    {
        return $this->getAttendanceCountAttribute();
    }

    /**
     * Check if call is upcoming.
     */
    public function getIsUpcomingAttribute(): bool
    {
        return $this->scheduled_at->isFuture() && $this->status === 'scheduled';
    }

    /**
     * Check if call is live.
     */
    public function getIsLiveAttribute(): bool
    {
        $now = now();
        $endTime = $this->scheduled_at->addMinutes($this->duration_minutes);

        return $this->status === 'live' ||
               ($this->scheduled_at->isPast() && $endTime->isFuture() && $this->status === 'scheduled');
    }

    /**
     * Check if call is completed.
     */
    public function getIsCompletedAttribute(): bool
    {
        return $this->status === 'completed' ||
               $this->scheduled_at->addMinutes($this->duration_minutes)->isPast();
    }

    /**
     * Get formatted duration.
     */
    public function getFormattedDurationAttribute(): string
    {
        $hours = floor($this->duration_minutes / 60);
        $minutes = $this->duration_minutes % 60;

        if ($hours > 0) {
            return $hours . 'h ' . $minutes . 'm';
        }

        return $minutes . 'm';
    }

    /**
     * Scope to get upcoming calls.
     */
    public function scopeUpcoming($query)
    {
        return $query->where('scheduled_at', '>', now())
                    ->where('status', 'scheduled');
    }

    /**
     * Scope to get live calls.
     */
    public function scopeLive($query)
    {
        return $query->where('status', 'live')
                    ->orWhere(function ($q) {
                        $q->where('status', 'scheduled')
                          ->where('scheduled_at', '<=', now())
                          ->whereRaw('DATE_ADD(scheduled_at, INTERVAL duration_minutes MINUTE) > NOW()');
                    });
    }

    /**
     * Scope to get completed calls.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed')
                    ->orWhere(function ($q) {
                        $q->where('status', 'scheduled')
                          ->whereRaw('DATE_ADD(scheduled_at, INTERVAL duration_minutes MINUTE) < NOW()');
                    });
    }

    /**
     * Scope to get featured calls.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope to order by scheduled time.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('scheduled_at');
    }
}
