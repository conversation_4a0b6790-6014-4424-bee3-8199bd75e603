<?php

namespace App\Http\Controllers;

use App\Models\CommunityPost;
use App\Models\CommunityComment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CommunityController extends Controller
{
    /**
     * Display a listing of community posts.
     */
    public function index(Request $request)
    {
        $query = CommunityPost::with(['user', 'topLevelComments']);

        // Filter by category
        if ($request->filled('category')) {
            $query->category($request->category);
        }

        // Search posts
        if ($request->filled('search')) {
            $query->search($request->search);
        }

        // Sort posts
        $sort = $request->get('sort', 'activity');
        switch ($sort) {
            case 'popular':
                $query->byPopularity();
                break;
            case 'newest':
                $query->byNewest();
                break;
            case 'activity':
            default:
                $query->byActivity();
                break;
        }

        $posts = $query->paginate(15);

        // Get categories for filter
        $categories = CommunityPost::distinct()
            ->whereNotNull('category')
            ->pluck('category')
            ->sort();

        return view('community.index', compact('posts', 'categories'));
    }

    /**
     * Show the form for creating a new post.
     */
    public function create()
    {
        $categories = ['General', 'Questions', 'Success Stories', 'Resources', 'Networking'];
        return view('community.create', compact('categories'));
    }

    /**
     * Store a newly created post.
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'category' => 'nullable|string|max:50',
            'tags' => 'nullable|string',
        ]);

        $tags = $request->tags ?
            array_map('trim', explode(',', $request->tags)) :
            [];

        CommunityPost::create([
            'user_id' => Auth::id(),
            'title' => $request->title,
            'content' => $request->content,
            'category' => $request->category,
            'tags' => $tags,
        ]);

        return redirect()->route('community.index')
            ->with('success', 'Post created successfully!');
    }

    /**
     * Display the specified post.
     */
    public function show(CommunityPost $post)
    {
        $post->load([
            'user',
            'topLevelComments.user',
            'topLevelComments.replies.user'
        ]);

        // Increment views
        $post->incrementViews();

        return view('community.show', compact('post'));
    }

    /**
     * Show the form for editing the post.
     */
    public function edit(CommunityPost $post)
    {
        // Check if user owns the post
        if ($post->user_id !== Auth::id()) {
            abort(403);
        }

        $categories = ['General', 'Questions', 'Success Stories', 'Resources', 'Networking'];
        return view('community.edit', compact('post', 'categories'));
    }

    /**
     * Update the specified post.
     */
    public function update(Request $request, CommunityPost $post)
    {
        // Check if user owns the post
        if ($post->user_id !== Auth::id()) {
            abort(403);
        }

        $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'category' => 'nullable|string|max:50',
            'tags' => 'nullable|string',
        ]);

        $tags = $request->tags ?
            array_map('trim', explode(',', $request->tags)) :
            [];

        $post->update([
            'title' => $request->title,
            'content' => $request->content,
            'category' => $request->category,
            'tags' => $tags,
        ]);

        return redirect()->route('community.show', $post)
            ->with('success', 'Post updated successfully!');
    }

    /**
     * Remove the specified post.
     */
    public function destroy(CommunityPost $post)
    {
        // Check if user owns the post or is admin
        if ($post->user_id !== Auth::id() && !Auth::user()->hasRole('admin')) {
            abort(403);
        }

        $post->delete();

        return redirect()->route('community.index')
            ->with('success', 'Post deleted successfully!');
    }

    /**
     * Store a comment on a post.
     */
    public function storeComment(Request $request, CommunityPost $post)
    {
        $request->validate([
            'content' => 'required|string',
            'parent_id' => 'nullable|exists:community_comments,id',
        ]);

        CommunityComment::create([
            'post_id' => $post->id,
            'user_id' => Auth::id(),
            'parent_id' => $request->parent_id,
            'content' => $request->content,
        ]);

        return back()->with('success', 'Comment added successfully!');
    }

    /**
     * Delete a comment.
     */
    public function destroyComment(CommunityComment $comment)
    {
        // Check if user owns the comment or is admin
        if ($comment->user_id !== Auth::id() && !Auth::user()->hasRole('admin')) {
            abort(403);
        }

        $comment->delete();

        return back()->with('success', 'Comment deleted successfully!');
    }

    /**
     * Like/unlike a post.
     */
    public function toggleLike(CommunityPost $post)
    {
        $user = Auth::user();

        if ($post->isLikedBy($user->id)) {
            $post->likedBy()->detach($user->id);
            $post->decrement('likes_count');
            $liked = false;
        } else {
            $post->likedBy()->attach($user->id);
            $post->increment('likes_count');
            $liked = true;
        }

        return response()->json([
            'liked' => $liked,
            'likes_count' => $post->fresh()->likes_count,
        ]);
    }

    /**
     * Like/unlike a comment.
     */
    public function toggleCommentLike(CommunityComment $comment)
    {
        $user = Auth::user();

        if ($comment->isLikedBy($user->id)) {
            $comment->likedBy()->detach($user->id);
            $comment->decrement('likes_count');
            $liked = false;
        } else {
            $comment->likedBy()->attach($user->id);
            $comment->increment('likes_count');
            $liked = true;
        }

        return response()->json([
            'liked' => $liked,
            'likes_count' => $comment->fresh()->likes_count,
        ]);
    }
}
