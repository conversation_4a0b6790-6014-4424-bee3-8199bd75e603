<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Review extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'course_id',
        'mentor_id',
        'rating',
        'title',
        'comment',
        'is_verified_purchase',
        'is_approved',
        'is_featured',
        'helpful_votes',
        'unhelpful_votes',
        'admin_response',
        'responded_at',
    ];

    protected $casts = [
        'rating' => 'integer',
        'is_verified_purchase' => 'boolean',
        'is_approved' => 'boolean',
        'is_featured' => 'boolean',
        'helpful_votes' => 'integer',
        'unhelpful_votes' => 'integer',
        'responded_at' => 'datetime',
    ];

    protected $dates = [
        'responded_at',
        'deleted_at',
    ];

    // Rating constants
    const RATING_MIN = 1;
    const RATING_MAX = 5;

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function course()
    {
        return $this->belongsTo(Course::class);
    }

    public function mentor()
    {
        return $this->belongsTo(User::class, 'mentor_id');
    }

    public function votes()
    {
        return $this->hasMany(ReviewVote::class);
    }

    public function getStarsArrayAttribute()
    {
        return range(1, 5);
    }

    public function getHelpfulnessRatioAttribute()
    {
        $totalVotes = $this->helpful_votes + $this->unhelpful_votes;
        return $totalVotes > 0 ? ($this->helpful_votes / $totalVotes) * 100 : 0;
    }

    public function getRatingTextAttribute()
    {
        $ratings = [
            1 => 'Poor',
            2 => 'Fair',
            3 => 'Good',
            4 => 'Very Good',
            5 => 'Excellent',
        ];

        return $ratings[$this->rating] ?? 'Unknown';
    }

    public function getIsRecentAttribute()
    {
        return $this->created_at->isAfter(now()->subDays(30));
    }

    public function getUserVoteAttribute()
    {
        if (!auth()->check()) {
            return null;
        }

        $vote = $this->votes()->where('user_id', auth()->id())->first();
        return $vote ? ($vote->is_helpful ? 'helpful' : 'unhelpful') : null;
    }

    public function scopeApproved($query)
    {
        return $query->where('is_approved', true);
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    public function scopeVerifiedPurchase($query)
    {
        return $query->where('is_verified_purchase', true);
    }

    public function scopeByRating($query, $rating)
    {
        return $query->where('rating', $rating);
    }

    public function scopeForCourse($query, $courseId)
    {
        return $query->where('course_id', $courseId);
    }

    public function scopeForMentor($query, $mentorId)
    {
        return $query->where('mentor_id', $mentorId);
    }

    public function scopeRecent($query, $days = 30)
    {
        return $query->where('created_at', '>', now()->subDays($days));
    }

    public function scopeHighRated($query, $minRating = 4)
    {
        return $query->where('rating', '>=', $minRating);
    }

    public function scopePopular($query)
    {
        return $query->orderBy('helpful_votes', 'desc');
    }

    public function approve()
    {
        $this->update(['is_approved' => true]);
    }

    public function reject()
    {
        $this->update(['is_approved' => false]);
    }

    public function markAsFeatured()
    {
        $this->update(['is_featured' => true]);
    }

    public function unmarkAsFeatured()
    {
        $this->update(['is_featured' => false]);
    }

    public function addAdminResponse($response)
    {
        $this->update([
            'admin_response' => $response,
            'responded_at' => now(),
        ]);
    }

    public function voteHelpful($userId)
    {
        $existingVote = $this->votes()->where('user_id', $userId)->first();

        if ($existingVote) {
            if (!$existingVote->is_helpful) {
                $existingVote->update(['is_helpful' => true]);
                $this->decrement('unhelpful_votes');
                $this->increment('helpful_votes');
            }
        } else {
            $this->votes()->create([
                'user_id' => $userId,
                'is_helpful' => true,
            ]);
            $this->increment('helpful_votes');
        }
    }

    public function voteUnhelpful($userId)
    {
        $existingVote = $this->votes()->where('user_id', $userId)->first();

        if ($existingVote) {
            if ($existingVote->is_helpful) {
                $existingVote->update(['is_helpful' => false]);
                $this->decrement('helpful_votes');
                $this->increment('unhelpful_votes');
            }
        } else {
            $this->votes()->create([
                'user_id' => $userId,
                'is_helpful' => false,
            ]);
            $this->increment('unhelpful_votes');
        }
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($review) {
            // Auto-approve reviews from verified purchases
            if ($review->is_verified_purchase) {
                $review->is_approved = true;
            }

            // Set mentor_id from course if not provided
            if (!$review->mentor_id && $review->course_id) {
                $course = Course::find($review->course_id);
                if ($course) {
                    $review->mentor_id = $course->mentor_id;
                }
            }
        });

        static::created(function ($review) {
            // Update course average rating
            if ($review->course) {
                $avgRating = $review->course->reviews()->approved()->avg('rating');
                $review->course->update(['average_rating' => $avgRating]);
            }

            // Update mentor average rating
            if ($review->mentor) {
                $avgRating = Review::where('mentor_id', $review->mentor_id)
                    ->approved()
                    ->avg('rating');
                $review->mentor->update(['average_rating' => $avgRating]);
            }
        });

        static::updated(function ($review) {
            // Update averages when review is approved/rejected
            if ($review->wasChanged('is_approved')) {
                if ($review->course) {
                    $avgRating = $review->course->reviews()->approved()->avg('rating');
                    $review->course->update(['average_rating' => $avgRating]);
                }

                if ($review->mentor) {
                    $avgRating = Review::where('mentor_id', $review->mentor_id)
                        ->approved()
                        ->avg('rating');
                    $review->mentor->update(['average_rating' => $avgRating]);
                }
            }
        });
    }
}
