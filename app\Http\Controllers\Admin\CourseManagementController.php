<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Course;
use App\Models\Lesson;
use App\Models\User;
use App\Models\UserProgress;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class CourseManagementController extends Controller
{
    /**
     * Display a listing of courses.
     */
    public function index(Request $request)
    {
        $query = Course::with(['mentor', 'lessons', 'categoryRelation', 'userProgress']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('category', 'like', "%{$search}%");
            });
        }

        // Filter by category
        if ($request->filled('category')) {
            $query->where(function($q) use ($request) {
                $q->where('category_id', $request->category)
                  ->orWhere('category', $request->category);
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('is_published', $request->status === 'published');
        }

        // Filter by mentor
        if ($request->filled('mentor')) {
            $query->where('mentor_id', $request->mentor);
        }

        // Sort
        $sortBy = $request->get('sort', 'created_at');
        $sortDirection = $request->get('direction', 'desc');
        $query->orderBy($sortBy, $sortDirection);

        $courses = $query->paginate(20);

        // Get filter options
        $categories = \App\Models\Category::where('is_active', true)->get();
        $mentors = User::whereHas('roles', function ($q) {
            $q->where('name', 'mentor');
        })->get();

        return view('admin.courses.index', compact('courses', 'categories', 'mentors'));
    }

    /**
     * Show the form for creating a new course.
     */
    public function create()
    {
        $mentors = User::whereHas('roles', function ($q) {
            $q->where('name', 'mentor');
        })->get();

        $categories = \App\Models\Category::where('is_active', true)->get();
        $difficulties = ['beginner', 'intermediate', 'advanced'];

        return view('admin.courses.create', compact('mentors', 'categories', 'difficulties'));
    }

    /**
     * Store a newly created course.
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'mentor_id' => 'required|exists:users,id',
            'category' => 'required|string|max:100',
            'difficulty' => 'required|in:beginner,intermediate,advanced',
            'duration_hours' => 'required|numeric|min:0',
            'price' => 'required|numeric|min:0',
            'thumbnail' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'required_plans' => 'array',
            'is_published' => 'boolean',
            'is_featured' => 'boolean',
        ]);

        $courseData = $request->except(['thumbnail']);
        $courseData['required_plans'] = $request->required_plans ?? [];
        $courseData['is_published'] = $request->boolean('is_published');
        $courseData['is_featured'] = $request->boolean('is_featured');

        // Handle thumbnail upload
        if ($request->hasFile('thumbnail')) {
            $courseData['thumbnail'] = $request->file('thumbnail')->store('course-thumbnails', 'public');
        }

        $course = Course::create($courseData);

        return redirect()->route('admin.courses.show', $course)
            ->with('success', 'Course created successfully.');
    }

    /**
     * Display the specified course.
     */
    public function show(Course $course)
    {
        $course->load(['mentor', 'lessons.userProgress', 'userProgress']);

        // Course statistics
        $stats = [
            'total_lessons' => $course->lessons->count(),
            'published_lessons' => $course->publishedLessons->count(),
            'total_enrollments' => $course->userProgress()->distinct('user_id')->count(),
            'completed_enrollments' => $course->userProgress()
                ->where('is_completed', true)
                ->distinct('user_id')
                ->count(),
            'total_watch_time' => $course->userProgress()->sum('watch_time_seconds'),
            'average_completion_rate' => $this->calculateCompletionRate($course),
        ];

        // Recent enrollments
        $recentEnrollments = $course->userProgress()
            ->with('user')
            ->latest()
            ->take(10)
            ->get();

        // Lesson progress
        $lessonProgress = $course->lessons->map(function ($lesson) {
            return [
                'lesson' => $lesson,
                'completions' => $lesson->userProgress()->where('is_completed', true)->count(),
                'average_watch_time' => $lesson->userProgress()->avg('watch_time_seconds'),
            ];
        });

        return view('admin.courses.show', compact('course', 'stats', 'recentEnrollments', 'lessonProgress'));
    }

    /**
     * Show the form for editing the course.
     */
    public function edit(Course $course)
    {
        $mentors = User::whereHas('roles', function ($q) {
            $q->where('name', 'mentor');
        })->get();

        $categories = \App\Models\Category::where('is_active', true)->get();
        $difficulties = ['beginner', 'intermediate', 'advanced'];

        return view('admin.courses.edit', compact('course', 'mentors', 'categories', 'difficulties'));
    }

    /**
     * Update the specified course.
     */
    public function update(Request $request, Course $course)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'mentor_id' => 'required|exists:users,id',
            'category' => 'required|string|max:100',
            'difficulty' => 'required|in:beginner,intermediate,advanced',
            'duration_hours' => 'required|numeric|min:0',
            'price' => 'required|numeric|min:0',
            'thumbnail' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'required_plans' => 'array',
            'is_published' => 'boolean',
            'is_featured' => 'boolean',
        ]);

        $courseData = $request->except(['thumbnail']);
        $courseData['required_plans'] = $request->required_plans ?? [];
        $courseData['is_published'] = $request->boolean('is_published');
        $courseData['is_featured'] = $request->boolean('is_featured');

        // Handle thumbnail upload
        if ($request->hasFile('thumbnail')) {
            // Delete old thumbnail
            if ($course->thumbnail) {
                Storage::disk('public')->delete($course->thumbnail);
            }
            $courseData['thumbnail'] = $request->file('thumbnail')->store('course-thumbnails', 'public');
        }

        $course->update($courseData);

        return redirect()->route('admin.courses.show', $course)
            ->with('success', 'Course updated successfully.');
    }

    /**
     * Remove the specified course.
     */
    public function destroy(Course $course)
    {
        // Delete thumbnail
        if ($course->thumbnail) {
            Storage::disk('public')->delete($course->thumbnail);
        }

        $course->delete();

        return redirect()->route('admin.courses.index')
            ->with('success', 'Course deleted successfully.');
    }

    /**
     * Toggle course published status.
     */
    public function togglePublished(Course $course)
    {
        $course->update(['is_published' => !$course->is_published]);

        $status = $course->is_published ? 'published' : 'unpublished';
        return back()->with('success', "Course {$status} successfully.");
    }

    /**
     * Toggle course featured status.
     */
    public function toggleFeatured(Course $course)
    {
        $course->update(['is_featured' => !$course->is_featured]);

        $status = $course->is_featured ? 'featured' : 'unfeatured';
        return back()->with('success', "Course {$status} successfully.");
    }

    /**
     * Show course lessons management.
     */
    public function lessons(Course $course)
    {
        $lessons = $course->lessons()->orderBy('order')->get();
        return view('admin.courses.lessons', compact('course', 'lessons'));
    }

    /**
     * Show course analytics.
     */
    public function analytics(Course $course)
    {
        // Enrollment data over time
        $enrollmentData = $this->getEnrollmentData($course);

        // Completion data
        $completionData = $this->getCompletionData($course);

        // User engagement
        $engagementData = $this->getEngagementData($course);

        return view('admin.courses.analytics', compact('course', 'enrollmentData', 'completionData', 'engagementData'));
    }

    /**
     * Calculate course completion rate.
     */
    private function calculateCompletionRate(Course $course)
    {
        $totalEnrollments = $course->userProgress()->distinct('user_id')->count();
        if ($totalEnrollments === 0) return 0;

        $completedEnrollments = $course->userProgress()
            ->where('is_completed', true)
            ->distinct('user_id')
            ->count();

        return round(($completedEnrollments / $totalEnrollments) * 100, 2);
    }

    /**
     * Get enrollment data for analytics.
     */
    private function getEnrollmentData(Course $course)
    {
        $data = [];
        for ($i = 29; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $count = $course->userProgress()
                ->whereDate('created_at', $date)
                ->distinct('user_id')
                ->count();
            $data[] = [
                'date' => $date->format('M j'),
                'count' => $count
            ];
        }
        return $data;
    }

    /**
     * Get completion data for analytics.
     */
    private function getCompletionData(Course $course)
    {
        $data = [];
        for ($i = 29; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $count = $course->userProgress()
                ->whereDate('completed_at', $date)
                ->where('is_completed', true)
                ->distinct('user_id')
                ->count();
            $data[] = [
                'date' => $date->format('M j'),
                'count' => $count
            ];
        }
        return $data;
    }

    /**
     * Get engagement data for analytics.
     */
    private function getEngagementData(Course $course)
    {
        return [
            'average_session_time' => $course->userProgress()->avg('watch_time_seconds'),
            'total_sessions' => $course->userProgress()->count(),
            'unique_learners' => $course->userProgress()->distinct('user_id')->count(),
            'completion_rate' => $this->calculateCompletionRate($course),
        ];
    }
}
