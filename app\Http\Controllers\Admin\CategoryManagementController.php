<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class CategoryManagementController extends Controller
{
    /**
     * Display a listing of categories.
     */
    public function index()
    {
        $categories = Category::withCount(['courses'])
            ->orderBy('sort_order')
            ->get();

        $stats = [
            'total_categories' => Category::count(),
            'active_categories' => Category::where('is_active', true)->count(),
            'categories_with_courses' => Category::has('courses')->count(),
        ];

        return view('admin.categories.index', compact('categories', 'stats'));
    }

    /**
     * Show the form for creating a new category.
     */
    public function create()
    {
        $parentCategories = Category::where('parent_id', null)
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        return view('admin.categories.create', compact('parentCategories'));
    }

    /**
     * Store a newly created category.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:categories',
            'description' => 'nullable|string',
            'parent_id' => 'nullable|exists:categories,id',
            'icon' => 'nullable|string|max:100',
            'color' => 'nullable|string|max:7',
            'image' => 'nullable|image|max:2048',
            'sort_order' => 'required|integer|min:0',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
        ]);

        $categoryData = $request->except(['image']);
        $categoryData['slug'] = Str::slug($request->name);

        // Handle image upload
        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('categories', 'public');
            $categoryData['image'] = $imagePath;
        }

        Category::create($categoryData);

        return redirect()->route('admin.categories.index')
            ->with('success', 'Category created successfully.');
    }

    /**
     * Display the specified category.
     */
    public function show(Category $category)
    {
        $category->load(['courses', 'parent', 'children']);
        
        $stats = [
            'total_courses' => $category->courses()->count(),
            'published_courses' => $category->courses()->where('is_published', true)->count(),
            'total_students' => $category->courses()
                ->withCount('enrollments')
                ->get()
                ->sum('enrollments_count'),
        ];

        return view('admin.categories.show', compact('category', 'stats'));
    }

    /**
     * Show the form for editing the category.
     */
    public function edit(Category $category)
    {
        $parentCategories = Category::where('parent_id', null)
            ->where('id', '!=', $category->id)
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        return view('admin.categories.edit', compact('category', 'parentCategories'));
    }

    /**
     * Update the specified category.
     */
    public function update(Request $request, Category $category)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:categories,name,' . $category->id,
            'description' => 'nullable|string',
            'parent_id' => 'nullable|exists:categories,id',
            'icon' => 'nullable|string|max:100',
            'color' => 'nullable|string|max:7',
            'image' => 'nullable|image|max:2048',
            'sort_order' => 'required|integer|min:0',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
        ]);

        // Prevent setting parent to self or child
        if ($request->parent_id == $category->id) {
            return back()->withErrors(['parent_id' => 'Category cannot be its own parent.']);
        }

        if ($category->children()->where('id', $request->parent_id)->exists()) {
            return back()->withErrors(['parent_id' => 'Cannot set a child category as parent.']);
        }

        $categoryData = $request->except(['image']);
        $categoryData['slug'] = Str::slug($request->name);

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($category->image) {
                \Storage::disk('public')->delete($category->image);
            }
            
            $imagePath = $request->file('image')->store('categories', 'public');
            $categoryData['image'] = $imagePath;
        }

        $category->update($categoryData);

        return redirect()->route('admin.categories.show', $category)
            ->with('success', 'Category updated successfully.');
    }

    /**
     * Remove the specified category.
     */
    public function destroy(Category $category)
    {
        // Check if category has courses
        if ($category->courses()->exists()) {
            return back()->withErrors(['error' => 'Cannot delete category with associated courses.']);
        }

        // Check if category has children
        if ($category->children()->exists()) {
            return back()->withErrors(['error' => 'Cannot delete category with subcategories.']);
        }

        // Delete associated image
        if ($category->image) {
            \Storage::disk('public')->delete($category->image);
        }

        $category->delete();

        return redirect()->route('admin.categories.index')
            ->with('success', 'Category deleted successfully.');
    }

    /**
     * Toggle category active status.
     */
    public function toggleActive(Category $category)
    {
        $category->update(['is_active' => !$category->is_active]);
        
        $status = $category->is_active ? 'activated' : 'deactivated';
        return back()->with('success', "Category {$status} successfully.");
    }

    /**
     * Toggle category featured status.
     */
    public function toggleFeatured(Category $category)
    {
        $category->update(['is_featured' => !$category->is_featured]);
        
        $status = $category->is_featured ? 'featured' : 'unfeatured';
        return back()->with('success', "Category {$status} successfully.");
    }

    /**
     * Reorder categories.
     */
    public function reorder(Request $request)
    {
        $request->validate([
            'category_orders' => 'required|array',
            'category_orders.*.id' => 'required|exists:categories,id',
            'category_orders.*.order' => 'required|integer|min:0',
        ]);

        foreach ($request->category_orders as $categoryOrder) {
            Category::where('id', $categoryOrder['id'])
                   ->update(['sort_order' => $categoryOrder['order']]);
        }

        return response()->json(['success' => true, 'message' => 'Categories reordered successfully.']);
    }

    /**
     * Bulk actions for categories.
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:activate,deactivate,feature,unfeature,delete',
            'category_ids' => 'required|array',
            'category_ids.*' => 'exists:categories,id',
        ]);

        $categoryIds = $request->category_ids;
        $action = $request->action;

        switch ($action) {
            case 'activate':
                Category::whereIn('id', $categoryIds)->update(['is_active' => true]);
                $message = 'Categories activated successfully.';
                break;
            case 'deactivate':
                Category::whereIn('id', $categoryIds)->update(['is_active' => false]);
                $message = 'Categories deactivated successfully.';
                break;
            case 'feature':
                Category::whereIn('id', $categoryIds)->update(['is_featured' => true]);
                $message = 'Categories featured successfully.';
                break;
            case 'unfeature':
                Category::whereIn('id', $categoryIds)->update(['is_featured' => false]);
                $message = 'Categories unfeatured successfully.';
                break;
            case 'delete':
                $categories = Category::whereIn('id', $categoryIds)->get();
                foreach ($categories as $category) {
                    if ($category->courses()->exists() || $category->children()->exists()) {
                        return back()->withErrors(['error' => 'Cannot delete categories with courses or subcategories.']);
                    }
                }
                
                // Delete images
                foreach ($categories as $category) {
                    if ($category->image) {
                        \Storage::disk('public')->delete($category->image);
                    }
                }
                
                Category::whereIn('id', $categoryIds)->delete();
                $message = 'Categories deleted successfully.';
                break;
        }

        return back()->with('success', $message);
    }

    /**
     * Get category analytics.
     */
    public function analytics()
    {
        $categories = Category::withCount(['courses', 'courses as published_courses_count' => function($query) {
            $query->where('is_published', true);
        }])
        ->with(['courses' => function($query) {
            $query->withCount('enrollments');
        }])
        ->get();

        $categoryStats = $categories->map(function($category) {
            return [
                'name' => $category->name,
                'total_courses' => $category->courses_count,
                'published_courses' => $category->published_courses_count,
                'total_enrollments' => $category->courses->sum('enrollments_count'),
                'avg_enrollments_per_course' => $category->courses_count > 0 
                    ? round($category->courses->sum('enrollments_count') / $category->courses_count, 2) 
                    : 0,
            ];
        });

        return view('admin.categories.analytics', compact('categoryStats'));
    }
}
