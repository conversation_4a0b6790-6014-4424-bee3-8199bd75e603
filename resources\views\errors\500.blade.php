@extends('layouts.app')

@section('title', 'Server Error')

@section('content')
<div class="error-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 text-center">
                <div class="error-content">
                    <!-- Error Animation -->
                    <div class="error-animation">
                        <div class="error-number">
                            <span class="five">5</span>
                            <span class="zero">0</span>
                            <span class="zero">0</span>
                        </div>
                        <div class="error-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                    </div>
                    
                    <!-- Error Message -->
                    <div class="error-message">
                        <h1 class="error-title">Internal Server Error</h1>
                        <p class="error-description">
                            Something went wrong on our end. We're working to fix this issue as quickly as possible.
                        </p>
                    </div>
                    
                    <!-- Status Information -->
                    <div class="status-info">
                        <div class="status-card">
                            <div class="status-icon">
                                <i class="fas fa-tools"></i>
                            </div>
                            <div class="status-content">
                                <h3>We're On It!</h3>
                                <p>Our technical team has been automatically notified and is working to resolve this issue.</p>
                                <div class="status-details">
                                    <div class="detail-item">
                                        <i class="fas fa-clock"></i>
                                        <span>Incident reported: {{ now()->format('M d, Y H:i') }} UTC</span>
                                    </div>
                                    <div class="detail-item">
                                        <i class="fas fa-id-badge"></i>
                                        <span>Error ID: {{ Str::random(8) }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- What You Can Do -->
                    <div class="error-suggestions">
                        <h3>What can you do while we fix this?</h3>
                        <div class="suggestions-grid">
                            <div class="suggestion-item">
                                <div class="suggestion-icon">
                                    <i class="fas fa-redo"></i>
                                </div>
                                <h4>Try Again</h4>
                                <p>Sometimes a simple refresh can resolve temporary issues</p>
                                <button class="btn btn-primary" onclick="window.location.reload()">
                                    <i class="fas fa-redo"></i> Refresh Page
                                </button>
                            </div>
                            
                            <div class="suggestion-item">
                                <div class="suggestion-icon">
                                    <i class="fas fa-arrow-left"></i>
                                </div>
                                <h4>Go Back</h4>
                                <p>Return to the previous page you were viewing</p>
                                <button class="btn btn-primary" onclick="window.history.back()">
                                    <i class="fas fa-arrow-left"></i> Go Back
                                </button>
                            </div>
                            
                            <div class="suggestion-item">
                                <div class="suggestion-icon">
                                    <i class="fas fa-home"></i>
                                </div>
                                <h4>Homepage</h4>
                                <p>Start fresh from our homepage</p>
                                <a href="{{ route('home') }}" class="btn btn-primary">
                                    <i class="fas fa-home"></i> Homepage
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Alternative Actions -->
                    <div class="alternative-actions">
                        <h4>Or try these popular sections:</h4>
                        <div class="actions-grid">
                            <a href="{{ route('courses.index') }}" class="action-link">
                                <i class="fas fa-graduation-cap"></i>
                                <span>Browse Courses</span>
                            </a>
                            <a href="{{ route('dashboard') }}" class="action-link">
                                <i class="fas fa-tachometer-alt"></i>
                                <span>Dashboard</span>
                            </a>
                            <a href="{{ route('community.index') }}" class="action-link">
                                <i class="fas fa-users"></i>
                                <span>Community</span>
                            </a>
                            <a href="{{ route('live-calls.index') }}" class="action-link">
                                <i class="fas fa-video"></i>
                                <span>Live Calls</span>
                            </a>
                        </div>
                    </div>
                    
                    <!-- Contact Support -->
                    <div class="support-section">
                        <div class="support-card">
                            <div class="support-icon">
                                <i class="fas fa-headset"></i>
                            </div>
                            <div class="support-content">
                                <h4>Need Immediate Help?</h4>
                                <p>If this error persists or you need urgent assistance, please contact our support team.</p>
                                <div class="support-buttons">
                                    <a href="{{ route('contact') }}" class="btn btn-outline-primary">
                                        <i class="fas fa-envelope"></i> Contact Support
                                    </a>
                                    <button class="btn btn-outline-secondary" onclick="reportError()">
                                        <i class="fas fa-bug"></i> Report Issue
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Status Updates -->
                    <div class="status-updates">
                        <div class="updates-card">
                            <h4>Stay Updated</h4>
                            <p>Follow our status page for real-time updates on system performance and maintenance.</p>
                            <a href="#" class="btn btn-outline-light" onclick="checkStatus()">
                                <i class="fas fa-chart-line"></i> System Status
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Error Report Modal -->
<div class="modal fade" id="errorReportModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Report Error</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="errorReportForm">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="errorDescription">What were you trying to do when this error occurred?</label>
                        <textarea id="errorDescription" class="form-control" rows="3" placeholder="Describe what you were doing..." required></textarea>
                    </div>
                    <div class="form-group">
                        <label for="userEmail">Your email (optional)</label>
                        <input type="email" id="userEmail" class="form-control" placeholder="<EMAIL>">
                        <small class="form-text text-muted">We'll only use this to follow up on your report</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane"></i> Send Report
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.error-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #e74a3b 0%, #c0392b 100%);
    display: flex;
    align-items: center;
    padding: 2rem 0;
    color: white;
}

.error-content {
    padding: 2rem;
}

.error-animation {
    margin-bottom: 3rem;
    position: relative;
}

.error-number {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 8rem;
    font-weight: 900;
    line-height: 1;
    margin-bottom: 1rem;
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
}

.error-number span {
    display: inline-block;
    animation: shake 2s infinite;
}

.error-number .five {
    animation-delay: 0s;
    color: #ffd700;
}

.error-number .zero:first-of-type {
    animation-delay: 0.1s;
}

.error-number .zero:last-of-type {
    animation-delay: 0.2s;
}

.error-icon {
    font-size: 3rem;
    opacity: 0.8;
    animation: pulse 2s infinite;
    color: #ffd700;
}

@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-5px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(5px);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 0.8;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.1);
    }
}

.error-message {
    margin-bottom: 3rem;
}

.error-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.error-description {
    font-size: 1.25rem;
    opacity: 0.9;
    line-height: 1.6;
    max-width: 600px;
    margin: 0 auto;
}

.status-info {
    margin-bottom: 3rem;
}

.status-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    text-align: left;
}

.status-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 2rem;
    flex-shrink: 0;
    font-size: 2rem;
    color: #ffd700;
}

.status-content h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.status-content p {
    opacity: 0.9;
    margin-bottom: 1rem;
    line-height: 1.5;
}

.status-details {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.detail-item {
    display: flex;
    align-items: center;
    font-size: 0.875rem;
    opacity: 0.8;
}

.detail-item i {
    margin-right: 0.5rem;
    width: 16px;
}

.error-suggestions {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 3rem;
    backdrop-filter: blur(10px);
}

.error-suggestions h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 2rem;
    text-align: center;
}

.suggestions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.suggestion-item {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
}

.suggestion-item:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-5px);
}

.suggestion-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 1.5rem;
}

.suggestion-item h4 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.suggestion-item p {
    opacity: 0.9;
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.alternative-actions {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 3rem;
    backdrop-filter: blur(10px);
}

.alternative-actions h4 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    text-align: center;
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.action-link {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
}

.action-link:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    text-decoration: none;
    transform: translateY(-3px);
}

.action-link i {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.action-link span {
    font-size: 0.875rem;
    font-weight: 500;
}

.support-section {
    margin-bottom: 3rem;
}

.support-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    display: flex;
    align-items: center;
    backdrop-filter: blur(10px);
}

.support-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 2rem;
    flex-shrink: 0;
    font-size: 2rem;
}

.support-content h4 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.support-content p {
    opacity: 0.9;
    margin-bottom: 1rem;
    line-height: 1.5;
}

.support-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.status-updates {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    backdrop-filter: blur(10px);
    text-align: center;
}

.updates-card h4 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.updates-card p {
    opacity: 0.9;
    margin-bottom: 1rem;
    line-height: 1.5;
}

.btn {
    border-radius: 25px;
    padding: 0.5rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
}

.btn-primary:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    color: white;
    transform: translateY(-2px);
}

.btn-outline-primary,
.btn-outline-secondary,
.btn-outline-light {
    background: transparent;
    border: 2px solid rgba(255, 255, 255, 0.5);
    color: white;
}

.btn-outline-primary:hover,
.btn-outline-secondary:hover,
.btn-outline-light:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.7);
    color: white;
    transform: translateY(-2px);
}

.modal-content {
    background: #2d3748;
    color: white;
    border: none;
    border-radius: 15px;
}

.modal-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-footer {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.form-control {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
}

.form-control:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.5);
    color: white;
    box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
}

.form-control::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

@media (max-width: 768px) {
    .error-number {
        font-size: 5rem;
    }
    
    .error-title {
        font-size: 2rem;
    }
    
    .error-description {
        font-size: 1rem;
    }
    
    .suggestions-grid {
        grid-template-columns: 1fr;
    }
    
    .actions-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .status-card,
    .support-card {
        flex-direction: column;
        text-align: center;
    }
    
    .status-icon,
    .support-icon {
        margin-right: 0;
        margin-bottom: 1rem;
    }
    
    .support-buttons {
        justify-content: center;
    }
}
</style>
@endpush

@push('scripts')
<script>
function reportError() {
    $('#errorReportModal').modal('show');
}

function checkStatus() {
    // Simulate status check
    alert('System Status: All services are operational. This error is being investigated.');
}

document.getElementById('errorReportForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const description = document.getElementById('errorDescription').value;
    const email = document.getElementById('userEmail').value;
    
    // Simulate sending error report
    fetch('/api/error-report', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            description: description,
            email: email,
            error_type: '500',
            url: window.location.href,
            user_agent: navigator.userAgent
        })
    })
    .then(response => response.json())
    .then(data => {
        $('#errorReportModal').modal('hide');
        alert('Thank you for your report. We\'ll investigate this issue promptly.');
    })
    .catch(error => {
        alert('Unable to send report at this time. Please try contacting support directly.');
    });
});

// Auto-retry mechanism
let retryCount = 0;
const maxRetries = 3;

function autoRetry() {
    if (retryCount < maxRetries) {
        retryCount++;
        setTimeout(() => {
            window.location.reload();
        }, 5000 * retryCount); // Exponential backoff
    }
}

// Track 500 errors for analytics
if (typeof gtag !== 'undefined') {
    gtag('event', 'exception', {
        'description': '500 Internal Server Error',
        'fatal': false
    });
}
</script>
@endpush
