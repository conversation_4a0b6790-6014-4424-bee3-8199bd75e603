<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>@yield('title') - {{ config('app.name', 'The Real World') }}</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Print Styles -->
    <style>
        /* Reset and base styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Figtree', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 12pt;
            line-height: 1.6;
            color: #000;
            background: #fff;
        }

        /* Print-specific styles */
        @media print {
            body {
                font-size: 11pt;
            }
            
            .no-print {
                display: none !important;
            }
            
            .page-break {
                page-break-before: always;
            }
            
            .page-break-after {
                page-break-after: always;
            }
            
            .avoid-break {
                page-break-inside: avoid;
            }
            
            a {
                text-decoration: none;
                color: #000;
            }
            
            a[href]:after {
                content: " (" attr(href) ")";
                font-size: 10pt;
                color: #666;
            }
            
            .print-url:after {
                content: none;
            }
        }

        /* Header styles */
        .print-header {
            border-bottom: 2px solid #000;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }

        .print-header .logo {
            display: inline-block;
            width: 40px;
            height: 40px;
            background: #2563eb;
            color: #fff;
            text-align: center;
            line-height: 40px;
            font-weight: bold;
            font-size: 14pt;
            margin-right: 15px;
            vertical-align: middle;
        }

        .print-header .company-info {
            display: inline-block;
            vertical-align: middle;
        }

        .print-header .company-name {
            font-size: 18pt;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .print-header .company-tagline {
            font-size: 10pt;
            color: #666;
        }

        .print-header .document-info {
            float: right;
            text-align: right;
            font-size: 10pt;
            color: #666;
        }

        /* Content styles */
        .print-content {
            margin: 30px 0;
        }

        .print-content h1 {
            font-size: 18pt;
            font-weight: bold;
            margin-bottom: 20px;
            border-bottom: 1px solid #ccc;
            padding-bottom: 10px;
        }

        .print-content h2 {
            font-size: 14pt;
            font-weight: 600;
            margin: 25px 0 15px 0;
        }

        .print-content h3 {
            font-size: 12pt;
            font-weight: 600;
            margin: 20px 0 10px 0;
        }

        .print-content p {
            margin-bottom: 12px;
        }

        .print-content ul,
        .print-content ol {
            margin: 12px 0 12px 20px;
        }

        .print-content li {
            margin-bottom: 6px;
        }

        .print-content table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }

        .print-content table th,
        .print-content table td {
            border: 1px solid #ccc;
            padding: 8px;
            text-align: left;
        }

        .print-content table th {
            background-color: #f5f5f5;
            font-weight: bold;
        }

        .print-content .highlight {
            background-color: #ffffcc;
            padding: 2px 4px;
        }

        .print-content .important {
            font-weight: bold;
            color: #000;
        }

        .print-content .note {
            background-color: #f0f8ff;
            border-left: 4px solid #2563eb;
            padding: 10px;
            margin: 15px 0;
            font-size: 10pt;
        }

        /* Footer styles */
        .print-footer {
            border-top: 1px solid #ccc;
            padding-top: 20px;
            margin-top: 40px;
            font-size: 10pt;
            color: #666;
        }

        .print-footer .footer-left {
            float: left;
        }

        .print-footer .footer-right {
            float: right;
        }

        .print-footer .clearfix {
            clear: both;
        }

        /* Utility classes */
        .text-center {
            text-align: center;
        }

        .text-right {
            text-align: right;
        }

        .text-left {
            text-align: left;
        }

        .font-bold {
            font-weight: bold;
        }

        .font-normal {
            font-weight: normal;
        }

        .text-small {
            font-size: 10pt;
        }

        .text-large {
            font-size: 14pt;
        }

        .mb-small {
            margin-bottom: 10px;
        }

        .mb-medium {
            margin-bottom: 20px;
        }

        .mb-large {
            margin-bottom: 30px;
        }

        .mt-small {
            margin-top: 10px;
        }

        .mt-medium {
            margin-top: 20px;
        }

        .mt-large {
            margin-top: 30px;
        }

        /* Responsive adjustments */
        @media screen and (max-width: 768px) {
            .print-header .document-info {
                float: none;
                text-align: left;
                margin-top: 15px;
            }
        }
    </style>

    <!-- Additional Styles -->
    @stack('styles')
</head>
<body>
    <!-- Print Header -->
    <div class="print-header">
        <div class="logo">TRW</div>
        <div class="company-info">
            <div class="company-name">The Real World</div>
            <div class="company-tagline">Escape the Matrix</div>
        </div>
        <div class="document-info">
            <div>@yield('document-type', 'Document')</div>
            <div>Generated: {{ now()->format('M j, Y g:i A') }}</div>
            @hasSection('document-id')
                <div>ID: @yield('document-id')</div>
            @endif
        </div>
        <div style="clear: both;"></div>
    </div>

    <!-- Print Content -->
    <div class="print-content">
        @yield('content')
    </div>

    <!-- Print Footer -->
    <div class="print-footer">
        <div class="footer-left">
            <div>&copy; {{ date('Y') }} The Real World. All rights reserved.</div>
            @hasSection('footer-left')
                @yield('footer-left')
            @endif
        </div>
        <div class="footer-right">
            <div>{{ request()->url() }}</div>
            @hasSection('footer-right')
                @yield('footer-right')
            @endif
        </div>
        <div class="clearfix"></div>
    </div>

    <!-- Print Controls (hidden in print) -->
    <div class="no-print" style="position: fixed; top: 20px; right: 20px; z-index: 1000;">
        <button onclick="window.print()" style="background: #2563eb; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin-right: 10px;">
            Print
        </button>
        <button onclick="window.close()" style="background: #6b7280; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
            Close
        </button>
    </div>

    <!-- Additional Scripts -->
    @stack('scripts')
</body>
</html>
