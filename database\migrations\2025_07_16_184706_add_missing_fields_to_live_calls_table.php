<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('live_calls', function (Blueprint $table) {
            $table->foreignId('course_id')->nullable()->constrained()->onDelete('set null')->after('mentor_id');
            $table->json('required_subscription_plans')->nullable()->after('required_plans');
            $table->boolean('is_featured')->default(false)->after('is_recorded');
            $table->datetime('started_at')->nullable()->after('status');
            $table->datetime('ended_at')->nullable()->after('started_at');
            $table->datetime('cancelled_at')->nullable()->after('ended_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('live_calls', function (Blueprint $table) {
            $table->dropForeign(['course_id']);
            $table->dropColumn([
                'course_id',
                'required_subscription_plans',
                'is_featured',
                'started_at',
                'ended_at',
                'cancelled_at'
            ]);
        });
    }
};
