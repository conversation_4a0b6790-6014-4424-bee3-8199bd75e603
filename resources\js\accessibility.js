// Accessibility Enhancement Module for The Real World LMS

class AccessibilityManager {
    constructor() {
        this.init();
    }

    init() {
        this.setupKeyboardNavigation();
        this.setupFocusManagement();
        this.setupScreenReaderSupport();
        this.setupColorContrastToggle();
        this.setupMotionPreferences();
        this.setupFontSizeControls();
        this.setupSkipLinks();
        this.setupAriaLiveRegions();
    }

    // Keyboard Navigation Enhancement
    setupKeyboardNavigation() {
        // Enhanced tab navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Tab') {
                document.body.classList.add('keyboard-navigation');
            }
        });

        document.addEventListener('mousedown', () => {
            document.body.classList.remove('keyboard-navigation');
        });

        // Escape key handling for modals and dropdowns
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeAllModals();
                this.closeAllDropdowns();
            }
        });

        // Arrow key navigation for menus
        this.setupArrowKeyNavigation();
    }

    setupArrowKeyNavigation() {
        const menus = document.querySelectorAll('[role="menu"], [role="menubar"]');
        
        menus.forEach(menu => {
            menu.addEventListener('keydown', (e) => {
                const items = menu.querySelectorAll('[role="menuitem"]');
                const currentIndex = Array.from(items).indexOf(document.activeElement);

                switch (e.key) {
                    case 'ArrowDown':
                        e.preventDefault();
                        const nextIndex = (currentIndex + 1) % items.length;
                        items[nextIndex].focus();
                        break;
                    case 'ArrowUp':
                        e.preventDefault();
                        const prevIndex = currentIndex === 0 ? items.length - 1 : currentIndex - 1;
                        items[prevIndex].focus();
                        break;
                    case 'Home':
                        e.preventDefault();
                        items[0].focus();
                        break;
                    case 'End':
                        e.preventDefault();
                        items[items.length - 1].focus();
                        break;
                }
            });
        });
    }

    // Focus Management
    setupFocusManagement() {
        // Focus trap for modals
        this.setupFocusTrap();
        
        // Focus restoration
        this.setupFocusRestoration();
        
        // Focus indicators
        this.enhanceFocusIndicators();
    }

    setupFocusTrap() {
        const modals = document.querySelectorAll('.modal');
        
        modals.forEach(modal => {
            modal.addEventListener('keydown', (e) => {
                if (e.key === 'Tab') {
                    const focusableElements = modal.querySelectorAll(
                        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
                    );
                    
                    const firstElement = focusableElements[0];
                    const lastElement = focusableElements[focusableElements.length - 1];

                    if (e.shiftKey) {
                        if (document.activeElement === firstElement) {
                            e.preventDefault();
                            lastElement.focus();
                        }
                    } else {
                        if (document.activeElement === lastElement) {
                            e.preventDefault();
                            firstElement.focus();
                        }
                    }
                }
            });
        });
    }

    setupFocusRestoration() {
        let lastFocusedElement = null;

        document.addEventListener('focusin', (e) => {
            if (!e.target.closest('.modal')) {
                lastFocusedElement = e.target;
            }
        });

        // Restore focus when modal closes
        document.addEventListener('modal:closed', () => {
            if (lastFocusedElement) {
                lastFocusedElement.focus();
            }
        });
    }

    enhanceFocusIndicators() {
        const style = document.createElement('style');
        style.textContent = `
            .keyboard-navigation *:focus {
                outline: 2px solid #2563eb !important;
                outline-offset: 2px !important;
            }
            
            .keyboard-navigation button:focus,
            .keyboard-navigation a:focus,
            .keyboard-navigation input:focus,
            .keyboard-navigation select:focus,
            .keyboard-navigation textarea:focus {
                box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.3) !important;
            }
        `;
        document.head.appendChild(style);
    }

    // Screen Reader Support
    setupScreenReaderSupport() {
        // Dynamic content announcements
        this.setupLiveRegions();
        
        // Form validation announcements
        this.setupFormValidationAnnouncements();
        
        // Loading state announcements
        this.setupLoadingAnnouncements();
    }

    setupLiveRegions() {
        // Create live region for announcements
        const liveRegion = document.createElement('div');
        liveRegion.setAttribute('aria-live', 'polite');
        liveRegion.setAttribute('aria-atomic', 'true');
        liveRegion.className = 'sr-only';
        liveRegion.id = 'live-region';
        document.body.appendChild(liveRegion);

        // Create assertive live region for urgent announcements
        const assertiveLiveRegion = document.createElement('div');
        assertiveLiveRegion.setAttribute('aria-live', 'assertive');
        assertiveLiveRegion.setAttribute('aria-atomic', 'true');
        assertiveLiveRegion.className = 'sr-only';
        assertiveLiveRegion.id = 'assertive-live-region';
        document.body.appendChild(assertiveLiveRegion);
    }

    announce(message, priority = 'polite') {
        const liveRegion = document.getElementById(
            priority === 'assertive' ? 'assertive-live-region' : 'live-region'
        );
        
        if (liveRegion) {
            liveRegion.textContent = message;
            
            // Clear after announcement
            setTimeout(() => {
                liveRegion.textContent = '';
            }, 1000);
        }
    }

    setupFormValidationAnnouncements() {
        document.addEventListener('invalid', (e) => {
            const field = e.target;
            const label = document.querySelector(`label[for="${field.id}"]`);
            const fieldName = label ? label.textContent : field.name || 'Field';
            
            this.announce(`${fieldName} is invalid: ${field.validationMessage}`, 'assertive');
        });
    }

    setupLoadingAnnouncements() {
        // Announce loading states
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' && mutation.attributeName === 'aria-busy') {
                    const element = mutation.target;
                    if (element.getAttribute('aria-busy') === 'true') {
                        this.announce('Loading content, please wait...');
                    } else {
                        this.announce('Content loaded');
                    }
                }
            });
        });

        observer.observe(document.body, {
            attributes: true,
            subtree: true,
            attributeFilter: ['aria-busy']
        });
    }

    // Color Contrast Toggle
    setupColorContrastToggle() {
        const contrastToggle = document.createElement('button');
        contrastToggle.textContent = 'Toggle High Contrast';
        contrastToggle.className = 'fixed top-4 right-4 z-50 bg-black text-white p-2 rounded sr-only focus:not-sr-only';
        contrastToggle.setAttribute('aria-label', 'Toggle high contrast mode');
        
        contrastToggle.addEventListener('click', () => {
            document.body.classList.toggle('high-contrast');
            const isHighContrast = document.body.classList.contains('high-contrast');
            this.announce(`High contrast mode ${isHighContrast ? 'enabled' : 'disabled'}`);
            
            // Save preference
            localStorage.setItem('high-contrast', isHighContrast);
        });

        document.body.appendChild(contrastToggle);

        // Load saved preference
        if (localStorage.getItem('high-contrast') === 'true') {
            document.body.classList.add('high-contrast');
        }

        // Add high contrast styles
        const contrastStyles = document.createElement('style');
        contrastStyles.textContent = `
            .high-contrast {
                filter: contrast(150%) !important;
            }
            
            .high-contrast * {
                background-color: white !important;
                color: black !important;
                border-color: black !important;
            }
            
            .high-contrast a {
                color: blue !important;
                text-decoration: underline !important;
            }
            
            .high-contrast button {
                background-color: black !important;
                color: white !important;
                border: 2px solid black !important;
            }
        `;
        document.head.appendChild(contrastStyles);
    }

    // Motion Preferences
    setupMotionPreferences() {
        // Respect prefers-reduced-motion
        if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
            document.body.classList.add('reduce-motion');
        }

        // Motion toggle
        const motionToggle = document.createElement('button');
        motionToggle.textContent = 'Toggle Animations';
        motionToggle.className = 'fixed top-16 right-4 z-50 bg-black text-white p-2 rounded sr-only focus:not-sr-only';
        motionToggle.setAttribute('aria-label', 'Toggle animations and motion');
        
        motionToggle.addEventListener('click', () => {
            document.body.classList.toggle('reduce-motion');
            const isReduced = document.body.classList.contains('reduce-motion');
            this.announce(`Animations ${isReduced ? 'disabled' : 'enabled'}`);
            
            localStorage.setItem('reduce-motion', isReduced);
        });

        document.body.appendChild(motionToggle);

        // Load saved preference
        if (localStorage.getItem('reduce-motion') === 'true') {
            document.body.classList.add('reduce-motion');
        }
    }

    // Font Size Controls
    setupFontSizeControls() {
        const fontControls = document.createElement('div');
        fontControls.className = 'fixed top-28 right-4 z-50 sr-only focus-within:not-sr-only';
        fontControls.innerHTML = `
            <button id="decrease-font" class="bg-black text-white p-2 rounded mr-1" aria-label="Decrease font size">A-</button>
            <button id="increase-font" class="bg-black text-white p-2 rounded" aria-label="Increase font size">A+</button>
        `;

        document.body.appendChild(fontControls);

        let fontSize = parseInt(localStorage.getItem('font-size')) || 16;
        document.documentElement.style.fontSize = fontSize + 'px';

        document.getElementById('decrease-font').addEventListener('click', () => {
            if (fontSize > 12) {
                fontSize -= 2;
                document.documentElement.style.fontSize = fontSize + 'px';
                localStorage.setItem('font-size', fontSize);
                this.announce(`Font size decreased to ${fontSize} pixels`);
            }
        });

        document.getElementById('increase-font').addEventListener('click', () => {
            if (fontSize < 24) {
                fontSize += 2;
                document.documentElement.style.fontSize = fontSize + 'px';
                localStorage.setItem('font-size', fontSize);
                this.announce(`Font size increased to ${fontSize} pixels`);
            }
        });
    }

    // Skip Links
    setupSkipLinks() {
        const skipLink = document.createElement('a');
        skipLink.href = '#main-content';
        skipLink.textContent = 'Skip to main content';
        skipLink.className = 'sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:bg-blue-600 focus:text-white focus:p-2 focus:rounded';
        
        document.body.insertBefore(skipLink, document.body.firstChild);

        // Ensure main content has ID
        const main = document.querySelector('main') || document.querySelector('#app');
        if (main && !main.id) {
            main.id = 'main-content';
        }
    }

    // ARIA Live Regions
    setupAriaLiveRegions() {
        // Status region for form submissions, etc.
        const statusRegion = document.createElement('div');
        statusRegion.setAttribute('aria-live', 'polite');
        statusRegion.setAttribute('aria-label', 'Status messages');
        statusRegion.className = 'sr-only';
        statusRegion.id = 'status-region';
        document.body.appendChild(statusRegion);
    }

    // Utility Methods
    closeAllModals() {
        const modals = document.querySelectorAll('.modal.show');
        modals.forEach(modal => {
            modal.classList.remove('show');
            document.dispatchEvent(new CustomEvent('modal:closed'));
        });
    }

    closeAllDropdowns() {
        const dropdowns = document.querySelectorAll('.dropdown.open');
        dropdowns.forEach(dropdown => {
            dropdown.classList.remove('open');
        });
    }
}

// Initialize accessibility manager when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.accessibilityManager = new AccessibilityManager();
});

// Export for use in other modules
export default AccessibilityManager;
