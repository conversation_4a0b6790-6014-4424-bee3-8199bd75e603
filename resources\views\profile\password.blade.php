@extends('layouts.dashboard')

@section('title', 'Change Password')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('profile.index') }}">Profile</a></li>
                    <li class="breadcrumb-item active">Change Password</li>
                </ol>
            </nav>
            <h1 class="h3 mb-0 text-gray-800">Change Password</h1>
            <p class="mb-0 text-muted">Update your account password to keep your account secure</p>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-lock"></i> Password Security
                    </h6>
                </div>
                <div class="card-body">
                    @if (session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle"></i>
                            {{ session('success') }}
                            <button type="button" class="close" data-dismiss="alert">
                                <span>&times;</span>
                            </button>
                        </div>
                    @endif

                    @if (session('error'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle"></i>
                            {{ session('error') }}
                            <button type="button" class="close" data-dismiss="alert">
                                <span>&times;</span>
                            </button>
                        </div>
                    @endif

                    <!-- Password Security Info -->
                    <div class="security-info mb-4">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="security-item">
                                    <div class="security-icon">
                                        <i class="fas fa-calendar-alt text-info"></i>
                                    </div>
                                    <div class="security-content">
                                        <h6>Last Changed</h6>
                                        <p class="text-muted">{{ auth()->user()->password_changed_at ? auth()->user()->password_changed_at->diffForHumans() : 'Never' }}</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="security-item">
                                    <div class="security-icon">
                                        <i class="fas fa-shield-alt text-success"></i>
                                    </div>
                                    <div class="security-content">
                                        <h6>Security Status</h6>
                                        <p class="text-success">
                                            <i class="fas fa-check"></i> Account Secured
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <form method="POST" action="{{ route('profile.password.update') }}" id="passwordForm">
                        @csrf
                        @method('PUT')

                        <!-- Current Password -->
                        <div class="form-group">
                            <label for="current_password" class="form-label">
                                <i class="fas fa-key text-primary"></i>
                                Current Password <span class="text-danger">*</span>
                            </label>
                            <div class="password-input">
                                <input id="current_password" type="password" 
                                       class="form-control @error('current_password') is-invalid @enderror" 
                                       name="current_password" 
                                       required 
                                       autocomplete="current-password"
                                       placeholder="Enter your current password">
                                <button type="button" class="password-toggle" onclick="togglePassword('current_password')">
                                    <i class="fas fa-eye" id="current_password-eye"></i>
                                </button>
                            </div>
                            @error('current_password')
                                <div class="invalid-feedback">
                                    <i class="fas fa-exclamation-circle"></i>
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        <!-- New Password -->
                        <div class="form-group">
                            <label for="password" class="form-label">
                                <i class="fas fa-lock text-primary"></i>
                                New Password <span class="text-danger">*</span>
                            </label>
                            <div class="password-input">
                                <input id="password" type="password" 
                                       class="form-control @error('password') is-invalid @enderror" 
                                       name="password" 
                                       required 
                                       autocomplete="new-password"
                                       placeholder="Enter your new password"
                                       onkeyup="checkPasswordStrength(this.value)">
                                <button type="button" class="password-toggle" onclick="togglePassword('password')">
                                    <i class="fas fa-eye" id="password-eye"></i>
                                </button>
                            </div>
                            @error('password')
                                <div class="invalid-feedback">
                                    <i class="fas fa-exclamation-circle"></i>
                                    {{ $message }}
                                </div>
                            @enderror
                            
                            <!-- Password Strength Indicator -->
                            <div class="password-strength mt-2">
                                <div class="strength-bar">
                                    <div class="strength-fill" id="strengthFill"></div>
                                </div>
                                <div class="strength-text" id="strengthText">Password strength</div>
                            </div>
                        </div>

                        <!-- Confirm Password -->
                        <div class="form-group">
                            <label for="password_confirmation" class="form-label">
                                <i class="fas fa-lock text-primary"></i>
                                Confirm New Password <span class="text-danger">*</span>
                            </label>
                            <div class="password-input">
                                <input id="password_confirmation" type="password" 
                                       class="form-control" 
                                       name="password_confirmation" 
                                       required 
                                       autocomplete="new-password"
                                       placeholder="Confirm your new password"
                                       onkeyup="checkPasswordMatch()">
                                <button type="button" class="password-toggle" onclick="togglePassword('password_confirmation')">
                                    <i class="fas fa-eye" id="password_confirmation-eye"></i>
                                </button>
                            </div>
                            <div class="password-match mt-2" id="passwordMatch"></div>
                        </div>

                        <!-- Password Requirements -->
                        <div class="password-requirements mb-4">
                            <h6 class="font-weight-bold text-gray-800 mb-2">Password Requirements:</h6>
                            <div class="requirements-list">
                                <div class="requirement" id="req-length">
                                    <i class="fas fa-times text-danger"></i>
                                    <span>At least 8 characters long</span>
                                </div>
                                <div class="requirement" id="req-uppercase">
                                    <i class="fas fa-times text-danger"></i>
                                    <span>Contains uppercase letter (A-Z)</span>
                                </div>
                                <div class="requirement" id="req-lowercase">
                                    <i class="fas fa-times text-danger"></i>
                                    <span>Contains lowercase letter (a-z)</span>
                                </div>
                                <div class="requirement" id="req-number">
                                    <i class="fas fa-times text-danger"></i>
                                    <span>Contains number (0-9)</span>
                                </div>
                                <div class="requirement" id="req-special">
                                    <i class="fas fa-times text-danger"></i>
                                    <span>Contains special character (!@#$%^&*)</span>
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary" id="submitBtn" disabled>
                                <i class="fas fa-save"></i> Update Password
                            </button>
                            <a href="{{ route('profile.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Security Tips -->
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-lightbulb"></i> Security Tips
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="security-tip">
                                <div class="tip-icon">
                                    <i class="fas fa-shield-alt text-success"></i>
                                </div>
                                <div class="tip-content">
                                    <h6>Use Strong Passwords</h6>
                                    <p>Create passwords with a mix of letters, numbers, and special characters.</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="security-tip">
                                <div class="tip-icon">
                                    <i class="fas fa-sync-alt text-info"></i>
                                </div>
                                <div class="tip-content">
                                    <h6>Change Regularly</h6>
                                    <p>Update your password every 3-6 months for better security.</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="security-tip">
                                <div class="tip-icon">
                                    <i class="fas fa-eye-slash text-warning"></i>
                                </div>
                                <div class="tip-content">
                                    <h6>Keep It Private</h6>
                                    <p>Never share your password with anyone or write it down.</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="security-tip">
                                <div class="tip-icon">
                                    <i class="fas fa-ban text-danger"></i>
                                </div>
                                <div class="tip-content">
                                    <h6>Avoid Common Passwords</h6>
                                    <p>Don't use easily guessable passwords like "password123".</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.security-info {
    background: #f8f9fc;
    border-radius: 0.35rem;
    padding: 1.5rem;
    border: 1px solid #e3e6f0;
}

.security-item {
    display: flex;
    align-items: center;
}

.security-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.security-icon i {
    font-size: 1.5rem;
}

.security-content h6 {
    font-weight: 600;
    margin-bottom: 0.25rem;
    color: #5a5c69;
}

.security-content p {
    margin: 0;
    font-size: 0.875rem;
}

.password-input {
    position: relative;
}

.form-control {
    height: 50px;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    padding: 0 3rem 0 1rem;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 3px rgba(78, 115, 223, 0.1);
}

.password-toggle {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #858796;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.password-toggle:hover {
    color: #5a5c69;
}

.password-strength {
    margin-top: 0.5rem;
}

.strength-bar {
    height: 4px;
    background: #e3e6f0;
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.strength-fill {
    height: 100%;
    width: 0%;
    transition: all 0.3s ease;
    border-radius: 2px;
}

.strength-text {
    font-size: 0.875rem;
    font-weight: 500;
}

.password-requirements {
    background: #f8f9fc;
    border-radius: 0.35rem;
    padding: 1rem;
    border: 1px solid #e3e6f0;
}

.requirements-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 0.5rem;
}

.requirement {
    display: flex;
    align-items: center;
    font-size: 0.875rem;
}

.requirement i {
    margin-right: 0.5rem;
    width: 16px;
}

.requirement.valid i {
    color: #1cc88a !important;
}

.requirement.valid i:before {
    content: "\f00c";
}

.password-match {
    font-size: 0.875rem;
    font-weight: 500;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    padding-top: 1rem;
    border-top: 1px solid #e3e6f0;
}

.security-tip {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1.5rem;
}

.tip-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #f8f9fc;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    flex-shrink: 0;
}

.tip-icon i {
    font-size: 1.25rem;
}

.tip-content h6 {
    font-weight: 600;
    margin-bottom: 0.25rem;
    color: #5a5c69;
}

.tip-content p {
    font-size: 0.875rem;
    color: #858796;
    margin: 0;
    line-height: 1.4;
}

@media (max-width: 768px) {
    .form-actions {
        flex-direction: column;
    }
    
    .requirements-list {
        grid-template-columns: 1fr;
    }
    
    .security-info {
        padding: 1rem;
    }
}
</style>
@endpush

@push('scripts')
<script>
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const eye = document.getElementById(fieldId + '-eye');
    
    if (field.type === 'password') {
        field.type = 'text';
        eye.classList.remove('fa-eye');
        eye.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        eye.classList.remove('fa-eye-slash');
        eye.classList.add('fa-eye');
    }
}

function checkPasswordStrength(password) {
    const strengthFill = document.getElementById('strengthFill');
    const strengthText = document.getElementById('strengthText');
    
    let score = 0;
    let feedback = '';
    
    // Check requirements
    const requirements = {
        length: password.length >= 8,
        uppercase: /[A-Z]/.test(password),
        lowercase: /[a-z]/.test(password),
        number: /[0-9]/.test(password),
        special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
    };
    
    // Update requirement indicators
    Object.keys(requirements).forEach(req => {
        const element = document.getElementById(`req-${req}`);
        if (requirements[req]) {
            element.classList.add('valid');
            score++;
        } else {
            element.classList.remove('valid');
        }
    });
    
    // Calculate strength
    let strength = 0;
    let color = '';
    
    if (score === 0) {
        strength = 0;
        feedback = 'Password strength';
        color = '#e3e6f0';
    } else if (score <= 2) {
        strength = 25;
        feedback = 'Weak password';
        color = '#e74a3b';
    } else if (score <= 3) {
        strength = 50;
        feedback = 'Fair password';
        color = '#f6c23e';
    } else if (score <= 4) {
        strength = 75;
        feedback = 'Good password';
        color = '#36b9cc';
    } else {
        strength = 100;
        feedback = 'Strong password';
        color = '#1cc88a';
    }
    
    strengthFill.style.width = strength + '%';
    strengthFill.style.backgroundColor = color;
    strengthText.textContent = feedback;
    strengthText.style.color = color;
    
    checkFormValidity();
}

function checkPasswordMatch() {
    const password = document.getElementById('password').value;
    const confirmation = document.getElementById('password_confirmation').value;
    const matchElement = document.getElementById('passwordMatch');
    
    if (confirmation === '') {
        matchElement.textContent = '';
        matchElement.className = 'password-match';
    } else if (password === confirmation) {
        matchElement.textContent = '✓ Passwords match';
        matchElement.className = 'password-match text-success';
    } else {
        matchElement.textContent = '✗ Passwords do not match';
        matchElement.className = 'password-match text-danger';
    }
    
    checkFormValidity();
}

function checkFormValidity() {
    const currentPassword = document.getElementById('current_password').value;
    const password = document.getElementById('password').value;
    const confirmation = document.getElementById('password_confirmation').value;
    const submitBtn = document.getElementById('submitBtn');
    
    // Check if all requirements are met
    const requirements = {
        length: password.length >= 8,
        uppercase: /[A-Z]/.test(password),
        lowercase: /[a-z]/.test(password),
        number: /[0-9]/.test(password),
        special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
    };
    
    const allRequirementsMet = Object.values(requirements).every(req => req);
    const passwordsMatch = password === confirmation && confirmation !== '';
    const currentPasswordFilled = currentPassword !== '';
    
    if (allRequirementsMet && passwordsMatch && currentPasswordFilled) {
        submitBtn.disabled = false;
        submitBtn.classList.remove('btn-secondary');
        submitBtn.classList.add('btn-primary');
    } else {
        submitBtn.disabled = true;
        submitBtn.classList.remove('btn-primary');
        submitBtn.classList.add('btn-secondary');
    }
}

// Form submission
document.getElementById('passwordForm').addEventListener('submit', function(e) {
    const submitBtn = document.getElementById('submitBtn');
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Updating...';
});

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    checkFormValidity();
});
</script>
@endpush
