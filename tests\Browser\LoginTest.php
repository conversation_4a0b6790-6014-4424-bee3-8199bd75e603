<?php

namespace Tests\Browser;

use App\Models\User;
use App\Models\Role;
use Illuminate\Foundation\Testing\DatabaseMigrations;
use <PERSON><PERSON>\Dusk\Browser;
use Tests\DuskTestCase;

class LoginTest extends DuskTestCase
{
    use DatabaseMigrations;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create basic roles
        Role::create(['name' => 'user', 'display_name' => 'User']);
        Role::create(['name' => 'admin', 'display_name' => 'Admin']);
    }

    /** @test */
    public function user_can_login_successfully()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password123'),
            'is_active' => true,
        ]);

        $this->browse(function (Browser $browser) {
            $browser->visit('/login')
                    ->assertSee('Login')
                    ->type('email', '<EMAIL>')
                    ->type('password', 'password123')
                    ->press('Login')
                    ->assertPathIs('/dashboard')
                    ->assertSee('Dashboard');
        });
    }

    /** @test */
    public function user_sees_error_with_invalid_credentials()
    {
        $this->browse(function (Browser $browser) {
            $browser->visit('/login')
                    ->type('email', '<EMAIL>')
                    ->type('password', 'wrongpassword')
                    ->press('Login')
                    ->assertPathIs('/login')
                    ->assertSee('These credentials do not match our records');
        });
    }

    /** @test */
    public function login_form_validates_required_fields()
    {
        $this->browse(function (Browser $browser) {
            $browser->visit('/login')
                    ->press('Login')
                    ->assertSee('The email field is required')
                    ->assertSee('The password field is required');
        });
    }

    /** @test */
    public function user_can_toggle_password_visibility()
    {
        $this->browse(function (Browser $browser) {
            $browser->visit('/login')
                    ->type('password', 'testpassword')
                    ->assertAttribute('input[name="password"]', 'type', 'password')
                    ->click('.password-toggle')
                    ->assertAttribute('input[name="password"]', 'type', 'text')
                    ->click('.password-toggle')
                    ->assertAttribute('input[name="password"]', 'type', 'password');
        });
    }

    /** @test */
    public function remember_me_checkbox_works()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password123'),
        ]);

        $this->browse(function (Browser $browser) {
            $browser->visit('/login')
                    ->type('email', '<EMAIL>')
                    ->type('password', 'password123')
                    ->check('remember')
                    ->press('Login')
                    ->assertPathIs('/dashboard');
        });
    }

    /** @test */
    public function user_can_navigate_to_registration_from_login()
    {
        $this->browse(function (Browser $browser) {
            $browser->visit('/login')
                    ->clickLink('Register')
                    ->assertPathIs('/register')
                    ->assertSee('Create Account');
        });
    }

    /** @test */
    public function user_can_navigate_to_forgot_password()
    {
        $this->browse(function (Browser $browser) {
            $browser->visit('/login')
                    ->clickLink('Forgot Password?')
                    ->assertPathIs('/forgot-password')
                    ->assertSee('Reset Password');
        });
    }

    /** @test */
    public function login_form_shows_loading_state()
    {
        $this->browse(function (Browser $browser) {
            $browser->visit('/login')
                    ->type('email', '<EMAIL>')
                    ->type('password', 'password123')
                    ->press('Login')
                    ->assertSee('Logging in...')
                    ->assertAttribute('button[type="submit"]', 'disabled', 'true');
        });
    }

    /** @test */
    public function authenticated_user_is_redirected_from_login()
    {
        $user = User::factory()->create();

        $this->browse(function (Browser $browser) use ($user) {
            $browser->loginAs($user)
                    ->visit('/login')
                    ->assertPathIs('/dashboard');
        });
    }

    /** @test */
    public function login_form_is_accessible()
    {
        $this->browse(function (Browser $browser) {
            $browser->visit('/login')
                    // Check for proper labels
                    ->assertSee('Email Address')
                    ->assertSee('Password')
                    // Check for ARIA attributes
                    ->assertAttribute('input[name="email"]', 'aria-label', 'Email Address')
                    ->assertAttribute('input[name="password"]', 'aria-label', 'Password')
                    // Check for proper form structure
                    ->assertPresent('form[method="POST"]')
                    ->assertPresent('input[type="email"][required]')
                    ->assertPresent('input[type="password"][required]');
        });
    }

    /** @test */
    public function login_form_works_with_keyboard_navigation()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password123'),
        ]);

        $this->browse(function (Browser $browser) {
            $browser->visit('/login')
                    ->keys('input[name="email"]', '<EMAIL>')
                    ->keys('', ['{tab}']) // Tab to password field
                    ->keys('', 'password123')
                    ->keys('', ['{tab}']) // Tab to submit button
                    ->keys('', ['{enter}']) // Press enter to submit
                    ->assertPathIs('/dashboard');
        });
    }

    /** @test */
    public function login_form_handles_network_errors_gracefully()
    {
        $this->browse(function (Browser $browser) {
            // Simulate network error by visiting invalid endpoint
            $browser->visit('/login')
                    ->type('email', '<EMAIL>')
                    ->type('password', 'password123')
                    ->script('
                        document.querySelector("form").action = "/invalid-endpoint";
                    ')
                    ->press('Login')
                    ->waitFor('.error-message', 5)
                    ->assertSee('An error occurred. Please try again.');
        });
    }

    /** @test */
    public function login_form_shows_account_locked_message()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password123'),
            'is_active' => false,
        ]);

        $this->browse(function (Browser $browser) {
            $browser->visit('/login')
                    ->type('email', '<EMAIL>')
                    ->type('password', 'password123')
                    ->press('Login')
                    ->assertSee('Your account has been deactivated');
        });
    }

    /** @test */
    public function login_form_responsive_design()
    {
        $this->browse(function (Browser $browser) {
            // Test mobile view
            $browser->resize(375, 667) // iPhone 6/7/8 size
                    ->visit('/login')
                    ->assertVisible('.login-form')
                    ->assertVisible('input[name="email"]')
                    ->assertVisible('input[name="password"]')
                    ->assertVisible('button[type="submit"]');

            // Test tablet view
            $browser->resize(768, 1024) // iPad size
                    ->refresh()
                    ->assertVisible('.login-form')
                    ->assertVisible('input[name="email"]')
                    ->assertVisible('input[name="password"]')
                    ->assertVisible('button[type="submit"]');

            // Test desktop view
            $browser->resize(1920, 1080)
                    ->refresh()
                    ->assertVisible('.login-form')
                    ->assertVisible('input[name="email"]')
                    ->assertVisible('input[name="password"]')
                    ->assertVisible('button[type="submit"]');
        });
    }

    /** @test */
    public function login_form_has_proper_security_headers()
    {
        $this->browse(function (Browser $browser) {
            $browser->visit('/login');
            
            // Check for CSRF token
            $browser->assertPresent('input[name="_token"]');
            
            // Check for secure form submission
            $browser->assertAttribute('form', 'method', 'POST');
        });
    }

    /** @test */
    public function login_form_shows_two_factor_prompt_when_enabled()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password123'),
            'two_factor_secret' => 'test-secret',
        ]);

        $this->browse(function (Browser $browser) {
            $browser->visit('/login')
                    ->type('email', '<EMAIL>')
                    ->type('password', 'password123')
                    ->press('Login')
                    ->assertPathIs('/two-factor-challenge')
                    ->assertSee('Two Factor Authentication')
                    ->assertSee('Please enter your authentication code');
        });
    }

    /** @test */
    public function login_form_animations_work_properly()
    {
        $this->browse(function (Browser $browser) {
            $browser->visit('/login')
                    ->waitFor('.login-form', 2)
                    ->assertVisible('.login-form')
                    // Check for fade-in animation
                    ->assertScript('
                        return window.getComputedStyle(document.querySelector(".login-form")).opacity === "1";
                    ')
                    // Check for form field focus animations
                    ->click('input[name="email"]')
                    ->assertScript('
                        return document.querySelector("input[name=email]").matches(":focus");
                    ');
        });
    }
}
