<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\ContactMessage;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;

class ContactController extends Controller
{
    public function index()
    {
        return view('pages.contact');
    }

    public function store(Request $request)
    {
        $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'subject' => 'required|string|in:general,technical,billing,course,account,partnership,feedback,other',
            'priority' => 'required|string|in:low,medium,high,critical',
            'message' => 'required|string|max:1000',
            'attachment' => 'nullable|file|mimes:jpg,jpeg,png,pdf,doc,docx|max:10240', // 10MB max
            'copy_to_email' => 'nullable|boolean',
        ]);

        // Handle file upload
        $attachmentPath = null;
        if ($request->hasFile('attachment')) {
            $attachmentPath = $request->file('attachment')->store('contact-attachments', 'public');
        }

        // Generate unique ticket ID
        $ticketId = 'TRW-' . strtoupper(Str::random(8));

        // Create contact message
        $contactMessage = ContactMessage::create([
            'ticket_id' => $ticketId,
            'first_name' => $request->first_name,
            'last_name' => $request->last_name,
            'email' => $request->email,
            'phone' => $request->phone,
            'subject' => $request->subject,
            'priority' => $request->priority,
            'message' => $request->message,
            'attachment_path' => $attachmentPath,
            'user_id' => auth()->id(),
            'status' => 'open',
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
        ]);

        // Send notification email to admin
        try {
            // Mail::to(config('mail.admin_email', '<EMAIL>'))
            //     ->send(new \App\Mail\NewContactMessage($contactMessage));
        } catch (\Exception $e) {
            \Log::error('Failed to send contact notification email: ' . $e->getMessage());
        }

        // Send confirmation email to user
        if ($request->copy_to_email || !auth()->check()) {
            try {
                // Mail::to($request->email)
                //     ->send(new \App\Mail\ContactConfirmation($contactMessage));
            } catch (\Exception $e) {
                \Log::error('Failed to send contact confirmation email: ' . $e->getMessage());
            }
        }

        return response()->json([
            'success' => true,
            'ticket_id' => $ticketId,
            'message' => 'Your message has been sent successfully!'
        ]);
    }

    public function show($ticketId)
    {
        $contactMessage = ContactMessage::where('ticket_id', $ticketId)->firstOrFail();

        // Check if user can view this message
        if (!auth()->check() || (auth()->id() !== $contactMessage->user_id && !auth()->user()->isAdmin())) {
            abort(403);
        }

        return view('pages.contact-message', compact('contactMessage'));
    }

    public function ping()
    {
        return response()->json([
            'status' => 'ok',
            'timestamp' => now()->toISOString(),
        ]);
    }
}
