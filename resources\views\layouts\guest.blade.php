<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'The Real World') }} @yield('title')</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <!-- Additional Styles -->
    @stack('styles')
</head>
<body class="font-sans text-gray-900 antialiased">
    <div class="min-h-screen flex flex-col sm:justify-center items-center pt-6 sm:pt-0 bg-gradient-to-br from-blue-50 via-white to-purple-50">
        <!-- Logo -->
        <div class="mb-8">
            <a href="{{ route('home') }}" class="flex items-center">
                <div class="w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-700 rounded-xl flex items-center justify-center mr-4">
                    <span class="text-white font-bold text-lg">TRW</span>
                </div>
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">The Real World</h1>
                    <p class="text-sm text-gray-600">Escape the Matrix</p>
                </div>
            </a>
        </div>

        <!-- Main Content -->
        <div class="w-full sm:max-w-md mt-6 px-6 py-8 bg-white shadow-lg overflow-hidden sm:rounded-xl border border-gray-100">
            <!-- Flash Messages -->
            @if (session('status'))
                <div class="mb-4 font-medium text-sm text-green-600 bg-green-50 border border-green-200 rounded-lg p-3">
                    {{ session('status') }}
                </div>
            @endif

            @if (session('error'))
                <div class="mb-4 font-medium text-sm text-red-600 bg-red-50 border border-red-200 rounded-lg p-3">
                    {{ session('error') }}
                </div>
            @endif

            @if ($errors->any())
                <div class="mb-4 bg-red-50 border border-red-200 rounded-lg p-3">
                    <ul class="text-sm text-red-600 space-y-1">
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            @yield('content')
        </div>

        <!-- Footer Links -->
        <div class="mt-8 text-center">
            <div class="flex items-center justify-center space-x-6 text-sm text-gray-600">
                <a href="{{ route('home') }}" class="hover:text-gray-900 transition duration-300">
                    Home
                </a>
                <a href="{{ route('about') }}" class="hover:text-gray-900 transition duration-300">
                    About
                </a>
                <a href="{{ route('contact') }}" class="hover:text-gray-900 transition duration-300">
                    Contact
                </a>
                <a href="{{ route('privacy') }}" class="hover:text-gray-900 transition duration-300">
                    Privacy
                </a>
            </div>
            <p class="mt-4 text-xs text-gray-500">
                &copy; {{ date('Y') }} The Real World. All rights reserved.
            </p>
        </div>
    </div>

    <!-- Additional Scripts -->
    @stack('scripts')
</body>
</html>
