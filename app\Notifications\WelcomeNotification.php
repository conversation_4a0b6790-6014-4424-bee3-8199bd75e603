<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class WelcomeNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('Welcome to The Real World!')
            ->greeting('Welcome to The Real World, ' . $notifiable->first_name . '!')
            ->line('Thank you for joining The Real World community. You\'re now part of an exclusive group of individuals committed to building wealth and achieving financial freedom.')
            ->line('Here\'s what you can do next:')
            ->line('• Complete your profile setup')
            ->line('• Browse our extensive course library')
            ->line('• Join live calls with our mentors')
            ->line('• Connect with the community')
            ->action('Get Started', route('dashboard'))
            ->line('If you have any questions, our support team is here to help.')
            ->salutation('Welcome to the brotherhood,')
            ->salutation('The Real World Team');
    }

    /**
     * Get the database representation of the notification.
     */
    public function toDatabase(object $notifiable): array
    {
        return [
            'title' => 'Welcome to The Real World!',
            'message' => 'Thank you for joining our community. Start your journey to financial freedom today.',
            'action_url' => route('dashboard'),
            'action_text' => 'Get Started',
            'type' => 'welcome',
        ];
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return $this->toDatabase($notifiable);
    }
}
