<?php $__env->startSection('title', '- Live Call Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="p-6">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Live Call Management</h1>
                <p class="mt-1 text-sm text-gray-600">Schedule and manage live calls with mentors</p>
            </div>
            <div class="flex space-x-3">
                <a href="<?php echo e(route('admin.live-calls.create')); ?>" 
                   class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition duration-150">
                    Schedule New Call
                </a>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Calls</dt>
                            <dd class="text-lg font-medium text-gray-900"><?php echo e(number_format($stats['total_calls'])); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Upcoming</dt>
                            <dd class="text-lg font-medium text-gray-900"><?php echo e(number_format($stats['upcoming_calls'])); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Completed</dt>
                            <dd class="text-lg font-medium text-gray-900"><?php echo e(number_format($stats['completed_calls'])); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Attendees</dt>
                            <dd class="text-lg font-medium text-gray-900"><?php echo e(number_format($stats['total_attendees'])); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="px-6 py-4">
            <form method="GET" class="grid grid-cols-1 md:grid-cols-5 gap-4">
                <div>
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                    <input type="text" name="search" id="search" value="<?php echo e(request('search')); ?>" 
                           placeholder="Search calls..." 
                           class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                </div>

                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <select name="status" id="status" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <option value="">All Statuses</option>
                        <option value="scheduled" <?php echo e(request('status') === 'scheduled' ? 'selected' : ''); ?>>Scheduled</option>
                        <option value="live" <?php echo e(request('status') === 'live' ? 'selected' : ''); ?>>Live</option>
                        <option value="completed" <?php echo e(request('status') === 'completed' ? 'selected' : ''); ?>>Completed</option>
                        <option value="cancelled" <?php echo e(request('status') === 'cancelled' ? 'selected' : ''); ?>>Cancelled</option>
                    </select>
                </div>

                <div>
                    <label for="mentor" class="block text-sm font-medium text-gray-700 mb-1">Mentor</label>
                    <select name="mentor" id="mentor" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <option value="">All Mentors</option>
                        <?php $__currentLoopData = $mentors; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $mentor): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($mentor->id); ?>" <?php echo e(request('mentor') == $mentor->id ? 'selected' : ''); ?>>
                                <?php echo e($mentor->name); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>

                <div>
                    <label for="date_from" class="block text-sm font-medium text-gray-700 mb-1">From Date</label>
                    <input type="date" name="date_from" id="date_from" value="<?php echo e(request('date_from')); ?>" 
                           class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                </div>

                <div class="flex items-end">
                    <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition duration-150">
                        Filter
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Live Calls Table -->
    <div class="bg-white shadow overflow-hidden sm:rounded-md">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Live Calls</h3>
        </div>
        
        <?php if($liveCalls->count() > 0): ?>
            <ul class="divide-y divide-gray-200">
                <?php $__currentLoopData = $liveCalls; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $liveCall): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <li>
                        <div class="px-6 py-4">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <?php if($liveCall->status === 'live'): ?>
                                            <div class="h-3 w-3 bg-red-500 rounded-full animate-pulse"></div>
                                        <?php elseif($liveCall->status === 'scheduled'): ?>
                                            <div class="h-3 w-3 bg-blue-500 rounded-full"></div>
                                        <?php elseif($liveCall->status === 'completed'): ?>
                                            <div class="h-3 w-3 bg-green-500 rounded-full"></div>
                                        <?php else: ?>
                                            <div class="h-3 w-3 bg-gray-500 rounded-full"></div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">
                                            <?php echo e($liveCall->title); ?>

                                        </div>
                                        <div class="text-sm text-gray-500">
                                            by <?php echo e($liveCall->mentor->name); ?>

                                            <?php if($liveCall->course): ?>
                                                • <?php echo e($liveCall->course->title); ?>

                                            <?php endif; ?>
                                        </div>
                                        <div class="text-xs text-gray-400 mt-1">
                                            <?php echo e($liveCall->scheduled_at->format('M d, Y \a\t g:i A')); ?>

                                            • <?php echo e($liveCall->duration_minutes); ?>min
                                            <?php if($liveCall->attendances_count > 0): ?>
                                                • <?php echo e($liveCall->attendances_count); ?> attendees
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>

                                <div class="flex items-center space-x-4">
                                    <div class="flex items-center">
                                        <?php if($liveCall->status === 'scheduled'): ?>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                Scheduled
                                            </span>
                                        <?php elseif($liveCall->status === 'live'): ?>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                Live
                                            </span>
                                        <?php elseif($liveCall->status === 'completed'): ?>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                Completed
                                            </span>
                                        <?php else: ?>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                Cancelled
                                            </span>
                                        <?php endif; ?>
                                    </div>

                                    <div class="flex items-center space-x-2">
                                        <a href="<?php echo e(route('admin.live-calls.show', $liveCall)); ?>" 
                                           class="text-blue-600 hover:text-blue-900 text-sm font-medium">
                                            View
                                        </a>
                                        
                                        <?php if($liveCall->status === 'scheduled'): ?>
                                            <a href="<?php echo e(route('admin.live-calls.edit', $liveCall)); ?>" 
                                               class="text-indigo-600 hover:text-indigo-900 text-sm font-medium">
                                                Edit
                                            </a>
                                            
                                            <form method="POST" action="<?php echo e(route('admin.live-calls.start', $liveCall)); ?>" class="inline">
                                                <?php echo csrf_field(); ?>
                                                <button type="submit" 
                                                        class="text-green-600 hover:text-green-900 text-sm font-medium">
                                                    Start
                                                </button>
                                            </form>
                                            
                                            <form method="POST" action="<?php echo e(route('admin.live-calls.cancel', $liveCall)); ?>" class="inline">
                                                <?php echo csrf_field(); ?>
                                                <button type="submit" 
                                                        onclick="return confirm('Are you sure you want to cancel this live call?')"
                                                        class="text-red-600 hover:text-red-900 text-sm font-medium">
                                                    Cancel
                                                </button>
                                            </form>
                                        <?php elseif($liveCall->status === 'live'): ?>
                                            <form method="POST" action="<?php echo e(route('admin.live-calls.end', $liveCall)); ?>" class="inline">
                                                <?php echo csrf_field(); ?>
                                                <button type="submit" 
                                                        onclick="return confirm('Are you sure you want to end this live call?')"
                                                        class="text-red-600 hover:text-red-900 text-sm font-medium">
                                                    End Call
                                                </button>
                                            </form>
                                        <?php endif; ?>
                                        
                                        <a href="<?php echo e(route('admin.live-calls.attendees', $liveCall)); ?>" 
                                           class="text-purple-600 hover:text-purple-900 text-sm font-medium">
                                            Attendees
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </ul>

            <!-- Pagination -->
            <div class="px-6 py-4 border-t border-gray-200">
                <?php echo e($liveCalls->links()); ?>

            </div>
        <?php else: ?>
            <div class="px-6 py-12 text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No live calls found</h3>
                <p class="mt-1 text-sm text-gray-500">Get started by scheduling your first live call.</p>
                <div class="mt-6">
                    <a href="<?php echo e(route('admin.live-calls.create')); ?>" 
                       class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                        Schedule New Call
                    </a>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\LMS\resources\views/admin/live-calls/index.blade.php ENDPATH**/ ?>