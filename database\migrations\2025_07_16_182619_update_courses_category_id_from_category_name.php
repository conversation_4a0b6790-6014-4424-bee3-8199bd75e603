<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update courses to set category_id based on category name
        $courses = DB::table('courses')->whereNull('category_id')->get();

        foreach ($courses as $course) {
            if ($course->category) {
                // Try to find a matching category by name (case insensitive)
                $category = DB::table('categories')
                    ->whereRaw('LOWER(name) = ?', [strtolower($course->category)])
                    ->first();

                if ($category) {
                    DB::table('courses')
                        ->where('id', $course->id)
                        ->update(['category_id' => $category->id]);
                } else {
                    // Create a new category if it doesn't exist
                    $categoryId = DB::table('categories')->insertGetId([
                        'name' => $course->category,
                        'slug' => \Illuminate\Support\Str::slug($course->category),
                        'description' => 'Auto-created category for ' . $course->category,
                        'is_active' => true,
                        'sort_order' => 999,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);

                    DB::table('courses')
                        ->where('id', $course->id)
                        ->update(['category_id' => $categoryId]);
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Reset category_id to null for courses that were updated
        DB::table('courses')->update(['category_id' => null]);
    }
};
