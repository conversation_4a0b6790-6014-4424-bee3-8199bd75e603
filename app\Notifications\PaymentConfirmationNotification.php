<?php

namespace App\Notifications;

use App\Models\Payment;
use App\Models\UserSubscription;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class PaymentConfirmationNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $payment;
    protected $subscription;

    /**
     * Create a new notification instance.
     */
    public function __construct(Payment $payment, UserSubscription $subscription)
    {
        $this->payment = $payment;
        $this->subscription = $subscription;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('Payment Confirmation - The Real World')
            ->greeting('Hello ' . $notifiable->first_name . '!')
            ->line('Thank you for your payment! Your subscription has been successfully activated.')
            ->line('Payment Details:')
            ->line('• Plan: ' . $this->subscription->subscriptionPlan->name)
            ->line('• Amount: $' . number_format($this->payment->amount, 2) . ' ' . strtoupper($this->payment->currency))
            ->line('• Billing Cycle: ' . ucfirst($this->subscription->billing_cycle))
            ->line('• Transaction ID: ' . $this->payment->transaction_id)
            ->line('• Next Billing Date: ' . $this->subscription->current_period_end->format('M d, Y'))
            ->action('Access Your Dashboard', route('dashboard'))
            ->line('You now have full access to all courses and features included in your plan.')
            ->line('If you have any questions about your subscription, please contact our support team.')
            ->salutation('Thank you for choosing The Real World,')
            ->salutation('The Real World Team');
    }

    /**
     * Get the database representation of the notification.
     */
    public function toDatabase(object $notifiable): array
    {
        return [
            'title' => 'Payment Confirmed',
            'message' => 'Your payment of $' . number_format($this->payment->amount, 2) . ' has been processed successfully.',
            'action_url' => route('subscriptions.index'),
            'action_text' => 'View Subscription',
            'type' => 'payment_confirmation',
            'payment_id' => $this->payment->id,
        ];
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return $this->toDatabase($notifiable);
    }
}
