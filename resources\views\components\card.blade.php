@props([
    'hover' => true,
    'padding' => true,
    'shadow' => 'md',
    'rounded' => 'lg'
])

@php
$baseClasses = 'bg-white overflow-hidden transition-all duration-300';

$shadowClasses = [
    'none' => '',
    'sm' => 'shadow-sm',
    'md' => 'shadow-md',
    'lg' => 'shadow-lg',
    'xl' => 'shadow-xl',
];

$roundedClasses = [
    'none' => '',
    'sm' => 'rounded-sm',
    'md' => 'rounded-md',
    'lg' => 'rounded-lg',
    'xl' => 'rounded-xl',
];

$hoverClasses = $hover ? 'hover:shadow-lg hover:transform hover:-translate-y-1' : '';
$paddingClasses = $padding ? 'p-6' : '';

$classes = implode(' ', array_filter([
    $baseClasses,
    $shadowClasses[$shadow],
    $roundedClasses[$rounded],
    $hoverClasses,
    $paddingClasses
]));
@endphp

<div {{ $attributes->merge(['class' => $classes]) }}>
    @if(isset($header))
        <div class="px-6 py-4 border-b border-gray-200 bg-gray-50 -m-6 mb-6">
            {{ $header }}
        </div>
    @endif

    {{ $slot }}

    @if(isset($footer))
        <div class="px-6 py-4 border-t border-gray-200 bg-gray-50 -m-6 mt-6">
            {{ $footer }}
        </div>
    @endif
</div>
