<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

class Faq extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'question',
        'answer',
        'category_id',
        'keywords',
        'icon',
        'order',
        'is_published',
        'views',
        'helpful_votes',
        'unhelpful_votes',
        'related_links',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'is_published' => 'boolean',
        'related_links' => 'array',
        'views' => 'integer',
        'helpful_votes' => 'integer',
        'unhelpful_votes' => 'integer',
    ];

    protected $dates = [
        'deleted_at',
    ];

    public function category()
    {
        return $this->belongsTo(FaqCategory::class, 'category_id');
    }

    public function votes()
    {
        return $this->hasMany(FaqVote::class);
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function getSlugAttribute()
    {
        return Str::slug($this->question);
    }

    public function getExcerptAttribute()
    {
        return Str::limit(strip_tags($this->answer), 150);
    }

    public function getHelpfulnessRatioAttribute()
    {
        $totalVotes = $this->helpful_votes + $this->unhelpful_votes;
        return $totalVotes > 0 ? ($this->helpful_votes / $totalVotes) * 100 : 0;
    }

    public function scopePublished($query)
    {
        return $query->where('is_published', true);
    }

    public function scopePopular($query)
    {
        return $query->orderBy('views', 'desc')->orderBy('helpful_votes', 'desc');
    }

    public function scopeByCategory($query, $categorySlug)
    {
        return $query->whereHas('category', function($q) use ($categorySlug) {
            $q->where('slug', $categorySlug);
        });
    }

    public function scopeSearch($query, $searchTerm)
    {
        return $query->where(function($q) use ($searchTerm) {
            $q->where('question', 'LIKE', "%{$searchTerm}%")
              ->orWhere('answer', 'LIKE', "%{$searchTerm}%")
              ->orWhere('keywords', 'LIKE', "%{$searchTerm}%");
        });
    }

    public function getUserVoteAttribute()
    {
        if (!auth()->check()) {
            return null;
        }

        $vote = $this->votes()->where('user_id', auth()->id())->first();
        return $vote ? ($vote->is_helpful ? 'helpful' : 'unhelpful') : null;
    }

    public function getRouteKeyName()
    {
        return 'id';
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($faq) {
            if (auth()->check()) {
                $faq->created_by = auth()->id();
            }
        });

        static::updating(function ($faq) {
            if (auth()->check()) {
                $faq->updated_by = auth()->id();
            }
        });
    }
}
