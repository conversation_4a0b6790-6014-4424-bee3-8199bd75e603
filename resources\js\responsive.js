// Responsive Design Enhancement Module

class ResponsiveManager {
    constructor() {
        this.breakpoints = {
            xs: 475,
            sm: 640,
            md: 768,
            lg: 1024,
            xl: 1280,
            '2xl': 1536
        };
        
        this.currentBreakpoint = this.getCurrentBreakpoint();
        this.init();
    }

    init() {
        this.setupViewportHandler();
        this.setupTouchGestures();
        this.setupMobileNavigation();
        this.setupResponsiveTables();
        this.setupResponsiveImages();
        this.setupOrientationHandler();
        this.setupIntersectionObserver();
    }

    getCurrentBreakpoint() {
        const width = window.innerWidth;
        
        if (width >= this.breakpoints['2xl']) return '2xl';
        if (width >= this.breakpoints.xl) return 'xl';
        if (width >= this.breakpoints.lg) return 'lg';
        if (width >= this.breakpoints.md) return 'md';
        if (width >= this.breakpoints.sm) return 'sm';
        if (width >= this.breakpoints.xs) return 'xs';
        return 'mobile';
    }

    setupViewportHandler() {
        let resizeTimeout;
        
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
                const newBreakpoint = this.getCurrentBreakpoint();
                
                if (newBreakpoint !== this.currentBreakpoint) {
                    this.handleBreakpointChange(this.currentBreakpoint, newBreakpoint);
                    this.currentBreakpoint = newBreakpoint;
                }
                
                this.updateViewportClasses();
                this.adjustResponsiveElements();
            }, 100);
        });

        // Initial setup
        this.updateViewportClasses();
        this.adjustResponsiveElements();
    }

    handleBreakpointChange(oldBreakpoint, newBreakpoint) {
        document.body.classList.remove(`breakpoint-${oldBreakpoint}`);
        document.body.classList.add(`breakpoint-${newBreakpoint}`);
        
        // Dispatch custom event
        document.dispatchEvent(new CustomEvent('breakpointChange', {
            detail: { oldBreakpoint, newBreakpoint }
        }));
    }

    updateViewportClasses() {
        const width = window.innerWidth;
        const height = window.innerHeight;
        
        // Update viewport size classes
        document.body.classList.toggle('mobile', width < this.breakpoints.sm);
        document.body.classList.toggle('tablet', width >= this.breakpoints.sm && width < this.breakpoints.lg);
        document.body.classList.toggle('desktop', width >= this.breakpoints.lg);
        
        // Update orientation classes
        document.body.classList.toggle('landscape', width > height);
        document.body.classList.toggle('portrait', width <= height);
        
        // Update specific breakpoint class
        Object.keys(this.breakpoints).forEach(bp => {
            document.body.classList.remove(`breakpoint-${bp}`);
        });
        document.body.classList.add(`breakpoint-${this.currentBreakpoint}`);
    }

    setupTouchGestures() {
        if (!('ontouchstart' in window)) return;

        let startX, startY, startTime;
        
        document.addEventListener('touchstart', (e) => {
            const touch = e.touches[0];
            startX = touch.clientX;
            startY = touch.clientY;
            startTime = Date.now();
        }, { passive: true });

        document.addEventListener('touchend', (e) => {
            if (!startX || !startY) return;
            
            const touch = e.changedTouches[0];
            const endX = touch.clientX;
            const endY = touch.clientY;
            const endTime = Date.now();
            
            const deltaX = endX - startX;
            const deltaY = endY - startY;
            const deltaTime = endTime - startTime;
            
            // Swipe detection
            if (Math.abs(deltaX) > 50 && deltaTime < 300) {
                const direction = deltaX > 0 ? 'right' : 'left';
                this.handleSwipe(direction, e.target);
            }
            
            // Reset
            startX = startY = null;
        }, { passive: true });
    }

    handleSwipe(direction, target) {
        // Handle mobile navigation swipes
        const sidebar = document.querySelector('.mobile-sidebar');
        if (sidebar) {
            if (direction === 'right' && !sidebar.classList.contains('open')) {
                this.openMobileSidebar();
            } else if (direction === 'left' && sidebar.classList.contains('open')) {
                this.closeMobileSidebar();
            }
        }
        
        // Dispatch swipe event
        document.dispatchEvent(new CustomEvent('swipe', {
            detail: { direction, target }
        }));
    }

    setupMobileNavigation() {
        // Mobile menu toggle
        const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
        const mobileMenu = document.querySelector('.mobile-menu');
        
        if (mobileMenuToggle && mobileMenu) {
            mobileMenuToggle.addEventListener('click', () => {
                const isOpen = mobileMenu.classList.contains('open');
                
                if (isOpen) {
                    this.closeMobileMenu();
                } else {
                    this.openMobileMenu();
                }
            });
        }

        // Close mobile menu on outside click
        document.addEventListener('click', (e) => {
            if (mobileMenu && mobileMenu.classList.contains('open')) {
                if (!mobileMenu.contains(e.target) && !mobileMenuToggle.contains(e.target)) {
                    this.closeMobileMenu();
                }
            }
        });

        // Close mobile menu on escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && mobileMenu && mobileMenu.classList.contains('open')) {
                this.closeMobileMenu();
            }
        });
    }

    openMobileMenu() {
        const mobileMenu = document.querySelector('.mobile-menu');
        const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
        
        if (mobileMenu) {
            mobileMenu.classList.add('open');
            mobileMenu.setAttribute('aria-hidden', 'false');
            document.body.style.overflow = 'hidden';
            
            if (mobileMenuToggle) {
                mobileMenuToggle.setAttribute('aria-expanded', 'true');
            }
        }
    }

    closeMobileMenu() {
        const mobileMenu = document.querySelector('.mobile-menu');
        const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
        
        if (mobileMenu) {
            mobileMenu.classList.remove('open');
            mobileMenu.setAttribute('aria-hidden', 'true');
            document.body.style.overflow = '';
            
            if (mobileMenuToggle) {
                mobileMenuToggle.setAttribute('aria-expanded', 'false');
            }
        }
    }

    setupResponsiveTables() {
        const tables = document.querySelectorAll('table');
        
        tables.forEach(table => {
            // Wrap table in responsive container
            if (!table.parentElement.classList.contains('table-responsive')) {
                const wrapper = document.createElement('div');
                wrapper.className = 'table-responsive overflow-x-auto';
                table.parentNode.insertBefore(wrapper, table);
                wrapper.appendChild(table);
            }
            
            // Add mobile-friendly attributes
            this.enhanceTableForMobile(table);
        });
    }

    enhanceTableForMobile(table) {
        const headers = table.querySelectorAll('th');
        const rows = table.querySelectorAll('tbody tr');
        
        rows.forEach(row => {
            const cells = row.querySelectorAll('td');
            cells.forEach((cell, index) => {
                if (headers[index]) {
                    cell.setAttribute('data-label', headers[index].textContent);
                }
            });
        });
    }

    setupResponsiveImages() {
        // Lazy loading for images
        const images = document.querySelectorAll('img[data-src]');
        
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });

            images.forEach(img => imageObserver.observe(img));
        }

        // Responsive image sizing
        this.adjustImageSizes();
        window.addEventListener('resize', () => this.adjustImageSizes());
    }

    adjustImageSizes() {
        const images = document.querySelectorAll('img[data-sizes]');
        
        images.forEach(img => {
            const sizes = JSON.parse(img.dataset.sizes);
            const currentBreakpoint = this.currentBreakpoint;
            
            if (sizes[currentBreakpoint]) {
                img.style.width = sizes[currentBreakpoint].width;
                img.style.height = sizes[currentBreakpoint].height;
            }
        });
    }

    setupOrientationHandler() {
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                this.updateViewportClasses();
                this.adjustResponsiveElements();
                
                // Dispatch orientation change event
                document.dispatchEvent(new CustomEvent('orientationChange', {
                    detail: { orientation: window.orientation }
                }));
            }, 100);
        });
    }

    setupIntersectionObserver() {
        if (!('IntersectionObserver' in window)) return;

        // Animate elements on scroll
        const animateObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                    animateObserver.unobserve(entry.target);
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        });

        document.querySelectorAll('.animate-on-scroll').forEach(el => {
            animateObserver.observe(el);
        });
    }

    adjustResponsiveElements() {
        // Adjust card layouts
        this.adjustCardLayouts();
        
        // Adjust navigation
        this.adjustNavigation();
        
        // Adjust forms
        this.adjustForms();
    }

    adjustCardLayouts() {
        const cardContainers = document.querySelectorAll('.card-grid');
        
        cardContainers.forEach(container => {
            const cards = container.children;
            const containerWidth = container.offsetWidth;
            const cardMinWidth = 300;
            const gap = 20;
            
            const columns = Math.floor((containerWidth + gap) / (cardMinWidth + gap));
            container.style.gridTemplateColumns = `repeat(${Math.max(1, columns)}, 1fr)`;
        });
    }

    adjustNavigation() {
        const nav = document.querySelector('nav');
        if (!nav) return;
        
        if (this.currentBreakpoint === 'mobile' || this.currentBreakpoint === 'xs') {
            nav.classList.add('mobile-nav');
        } else {
            nav.classList.remove('mobile-nav');
        }
    }

    adjustForms() {
        const forms = document.querySelectorAll('form');
        
        forms.forEach(form => {
            if (this.currentBreakpoint === 'mobile' || this.currentBreakpoint === 'xs') {
                form.classList.add('mobile-form');
            } else {
                form.classList.remove('mobile-form');
            }
        });
    }

    // Utility methods
    isMobile() {
        return this.currentBreakpoint === 'mobile' || this.currentBreakpoint === 'xs';
    }

    isTablet() {
        return this.currentBreakpoint === 'sm' || this.currentBreakpoint === 'md';
    }

    isDesktop() {
        return ['lg', 'xl', '2xl'].includes(this.currentBreakpoint);
    }

    getViewportSize() {
        return {
            width: window.innerWidth,
            height: window.innerHeight,
            breakpoint: this.currentBreakpoint
        };
    }
}

// Initialize responsive manager
document.addEventListener('DOMContentLoaded', () => {
    window.responsiveManager = new ResponsiveManager();
});

// Export for use in other modules
export default ResponsiveManager;
