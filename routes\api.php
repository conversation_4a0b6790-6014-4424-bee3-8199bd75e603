<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->group(function () {
    // User routes
    Route::get('/user', function (Request $request) {
        return $request->user();
    });
    Route::put('/user', [App\Http\Controllers\Api\UserController::class, 'update']);
    Route::post('/user/avatar', [App\Http\Controllers\Api\UserController::class, 'updateAvatar']);
    Route::delete('/user/avatar', [App\Http\Controllers\Api\UserController::class, 'deleteAvatar']);

    // Course routes
    Route::get('/courses', [App\Http\Controllers\Api\CourseController::class, 'index']);
    Route::get('/courses/{course}', [App\Http\Controllers\Api\CourseController::class, 'show']);
    Route::post('/courses/{course}/enroll', [App\Http\Controllers\Api\CourseController::class, 'enroll']);
    Route::delete('/courses/{course}/unenroll', [App\Http\Controllers\Api\CourseController::class, 'unenroll']);
    Route::post('/courses/{course}/favorite', [App\Http\Controllers\Api\CourseController::class, 'toggleFavorite']);
    Route::post('/courses/{course}/rate', [App\Http\Controllers\Api\RatingController::class, 'store']);
    Route::get('/courses/my-courses', [App\Http\Controllers\Api\CourseController::class, 'myCourses']);
    Route::get('/courses/favorites', [App\Http\Controllers\Api\CourseController::class, 'favorites']);

    // Lesson routes
    Route::get('/lessons/{lesson}', [App\Http\Controllers\Api\LessonController::class, 'show']);
    Route::post('/lessons/{lesson}/progress', [App\Http\Controllers\Api\LessonController::class, 'updateProgress']);
    Route::post('/lessons/{lesson}/complete', [App\Http\Controllers\Api\LessonController::class, 'complete']);
    Route::post('/lessons/{lesson}/bookmark', [App\Http\Controllers\Api\LessonController::class, 'toggleBookmark']);
    Route::post('/lessons/{lesson}/note', [App\Http\Controllers\Api\LessonController::class, 'saveNote']);
    Route::get('/lessons/{lesson}/notes', [App\Http\Controllers\Api\LessonController::class, 'getNotes']);

    // Progress routes
    Route::get('/progress', [App\Http\Controllers\Api\ProgressController::class, 'index']);
    Route::get('/progress/course/{course}', [App\Http\Controllers\Api\ProgressController::class, 'courseProgress']);
    Route::get('/progress/stats', [App\Http\Controllers\Api\ProgressController::class, 'stats']);

    // Subscription routes
    Route::get('/subscription', [App\Http\Controllers\Api\SubscriptionController::class, 'current']);
    Route::post('/subscription/subscribe', [App\Http\Controllers\Api\SubscriptionController::class, 'subscribe']);
    Route::post('/subscription/cancel', [App\Http\Controllers\Api\SubscriptionController::class, 'cancel']);
    Route::post('/subscription/resume', [App\Http\Controllers\Api\SubscriptionController::class, 'resume']);
    Route::get('/subscription/invoices', [App\Http\Controllers\Api\SubscriptionController::class, 'invoices']);

    // Payment routes
    Route::get('/payments', [App\Http\Controllers\Api\PaymentController::class, 'index']);
    Route::get('/payments/{payment}', [App\Http\Controllers\Api\PaymentController::class, 'show']);
    Route::post('/payments/crypto/verify', [App\Http\Controllers\Api\PaymentController::class, 'verifyCrypto']);

    // Certificate routes
    Route::get('/certificates', [App\Http\Controllers\Api\CertificateController::class, 'index']);
    Route::get('/certificates/{certificate}', [App\Http\Controllers\Api\CertificateController::class, 'show']);
    Route::get('/certificates/{certificate}/download', [App\Http\Controllers\Api\CertificateController::class, 'download']);

    // Notification routes
    Route::get('/notifications', [App\Http\Controllers\Api\NotificationController::class, 'index']);
    Route::post('/notifications/{notification}/read', [App\Http\Controllers\Api\NotificationController::class, 'markAsRead']);
    Route::post('/notifications/read-all', [App\Http\Controllers\Api\NotificationController::class, 'markAllAsRead']);
    Route::delete('/notifications/{notification}', [App\Http\Controllers\Api\NotificationController::class, 'destroy']);
    Route::get('/notifications/unread-count', [App\Http\Controllers\Api\NotificationController::class, 'unreadCount']);

    // Live Call routes
    Route::get('/live-calls', [App\Http\Controllers\Api\LiveCallController::class, 'index']);
    Route::get('/live-calls/{liveCall}', [App\Http\Controllers\Api\LiveCallController::class, 'show']);
    Route::post('/live-calls/{liveCall}/register', [App\Http\Controllers\Api\LiveCallController::class, 'register']);
    Route::delete('/live-calls/{liveCall}/unregister', [App\Http\Controllers\Api\LiveCallController::class, 'unregister']);
    Route::post('/live-calls/{liveCall}/reminder', [App\Http\Controllers\Api\LiveCallController::class, 'setReminder']);

    // Community routes
    Route::get('/community/posts', [App\Http\Controllers\Api\CommunityController::class, 'index']);
    Route::post('/community/posts', [App\Http\Controllers\Api\CommunityController::class, 'store']);
    Route::get('/community/posts/{post}', [App\Http\Controllers\Api\CommunityController::class, 'show']);
    Route::put('/community/posts/{post}', [App\Http\Controllers\Api\CommunityController::class, 'update']);
    Route::delete('/community/posts/{post}', [App\Http\Controllers\Api\CommunityController::class, 'destroy']);
    Route::post('/community/posts/{post}/like', [App\Http\Controllers\Api\CommunityController::class, 'toggleLike']);
    Route::post('/community/posts/{post}/comments', [App\Http\Controllers\Api\CommunityController::class, 'storeComment']);
    Route::put('/community/comments/{comment}', [App\Http\Controllers\Api\CommunityController::class, 'updateComment']);
    Route::delete('/community/comments/{comment}', [App\Http\Controllers\Api\CommunityController::class, 'destroyComment']);
    Route::post('/community/comments/{comment}/like', [App\Http\Controllers\Api\CommunityController::class, 'toggleCommentLike']);

    // Search routes
    Route::get('/search', [App\Http\Controllers\Api\SearchController::class, 'search']);
    Route::get('/search/courses', [App\Http\Controllers\Api\SearchController::class, 'courses']);
    Route::get('/search/suggestions', [App\Http\Controllers\Api\SearchController::class, 'suggestions']);

    // Dashboard routes
    Route::get('/dashboard/stats', [App\Http\Controllers\Api\DashboardController::class, 'stats']);
    Route::get('/dashboard/recent-activity', [App\Http\Controllers\Api\DashboardController::class, 'recentActivity']);
    Route::get('/dashboard/recommendations', [App\Http\Controllers\Api\DashboardController::class, 'recommendations']);
});

// Public routes
Route::get('/subscription-plans', [App\Http\Controllers\Api\SubscriptionPlanController::class, 'index']);
Route::get('/courses/public', [App\Http\Controllers\Api\CourseController::class, 'publicCourses']);
Route::get('/courses/{course}/public', [App\Http\Controllers\Api\CourseController::class, 'publicShow']);
Route::get('/categories', [App\Http\Controllers\Api\CategoryController::class, 'index']);
Route::get('/mentors', [App\Http\Controllers\Api\MentorController::class, 'index']);
Route::get('/mentors/{mentor}', [App\Http\Controllers\Api\MentorController::class, 'show']);
Route::get('/live-calls/public', [App\Http\Controllers\Api\LiveCallController::class, 'publicCalls']);
Route::get('/testimonials', [App\Http\Controllers\Api\TestimonialController::class, 'index']);

// Authentication routes
Route::post('/auth/register', [App\Http\Controllers\Api\AuthController::class, 'register']);
Route::post('/auth/login', [App\Http\Controllers\Api\AuthController::class, 'login']);
Route::post('/auth/logout', [App\Http\Controllers\Api\AuthController::class, 'logout'])->middleware('auth:sanctum');
Route::post('/auth/refresh', [App\Http\Controllers\Api\AuthController::class, 'refresh'])->middleware('auth:sanctum');
Route::post('/auth/forgot-password', [App\Http\Controllers\Api\AuthController::class, 'forgotPassword']);
Route::post('/auth/reset-password', [App\Http\Controllers\Api\AuthController::class, 'resetPassword']);
Route::post('/auth/verify-email', [App\Http\Controllers\Api\AuthController::class, 'verifyEmail'])->middleware('auth:sanctum');
Route::post('/auth/resend-verification', [App\Http\Controllers\Api\AuthController::class, 'resendVerification'])->middleware('auth:sanctum');
