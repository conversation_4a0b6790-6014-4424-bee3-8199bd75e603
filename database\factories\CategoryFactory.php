<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Category>
 */
class CategoryFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $categories = [
            'E-commerce' => '#e74c3c',
            'Copywriting' => '#3498db',
            'Cryptocurrency' => '#f39c12',
            'Stocks & Trading' => '#27ae60',
            'Amazon FBA' => '#9b59b6',
            'Freelancing' => '#1abc9c',
            'Content Creation' => '#e67e22',
            'Business Management' => '#34495e',
            'Real Estate' => '#95a5a6',
            'Fitness & Health' => '#2ecc71',
        ];

        $name = $this->faker->randomElement(array_keys($categories));

        return [
            'name' => $name,
            'slug' => \Str::slug($name),
            'description' => $this->faker->sentence(10),
            'color' => $categories[$name],
            'icon' => 'fas fa-' . $this->faker->randomElement(['chart-line', 'pen', 'bitcoin', 'dollar-sign', 'shopping-cart', 'laptop', 'video', 'briefcase', 'home', 'dumbbell']),
            'is_active' => true,
            'sort_order' => $this->faker->numberBetween(0, 100),
        ];
    }
}
