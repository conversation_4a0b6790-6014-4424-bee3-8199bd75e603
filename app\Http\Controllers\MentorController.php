<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Course;
use App\Models\Category;
use App\Models\Review;
use App\Models\LiveCall;
use Illuminate\Support\Facades\Auth;

class MentorController extends Controller
{
    public function index(Request $request)
    {
        $query = User::where('role', 'mentor')
            ->where('is_active', true)
            ->with(['courses', 'reviews']);

        // Filter by category
        if ($request->has('category') && $request->category) {
            $query->whereHas('courses', function($q) use ($request) {
                $q->whereHas('category', function($cat) use ($request) {
                    $cat->where('slug', $request->category);
                });
            });
        }

        // Search functionality
        if ($request->has('search') && $request->search) {
            $searchTerm = $request->search;
            $query->where(function($q) use ($searchTerm) {
                $q->where('name', 'LIKE', "%{$searchTerm}%")
                  ->orWhere('title', 'LIKE', "%{$searchTerm}%")
                  ->orWhere('bio', 'LIKE', "%{$searchTerm}%")
                  ->orWhereJsonContains('specialties', $searchTerm);
            });
        }

        // Sort options
        $sortBy = $request->get('sort', 'featured');
        switch ($sortBy) {
            case 'rating':
                $query->orderByDesc('average_rating');
                break;
            case 'students':
                $query->withCount('enrollments')->orderByDesc('enrollments_count');
                break;
            case 'newest':
                $query->orderByDesc('created_at');
                break;
            default:
                $query->orderByDesc('is_featured')->orderByDesc('average_rating');
        }

        $mentors = $query->paginate(12);

        // Add calculated fields to each mentor
        $mentors->getCollection()->transform(function ($mentor) {
            $mentor->students_count = $mentor->courses->sum(function($course) {
                return $course->enrollments()->count();
            });
            
            $mentor->courses_count = $mentor->courses->count();
            
            $mentor->reviews_count = $mentor->reviews->count();
            
            $mentor->average_rating = $mentor->reviews->avg('rating') ?? 0;
            
            // Mock revenue data - replace with actual calculation
            $mentor->revenue = rand(1, 50);
            
            // Check if current user follows this mentor
            $mentor->is_followed = Auth::check() ? 
                Auth::user()->followedMentors()->where('mentor_id', $mentor->id)->exists() : false;
            
            // Parse specialties if stored as JSON
            if (is_string($mentor->specialties)) {
                $mentor->specialties = json_decode($mentor->specialties, true) ?? [];
            }
            
            // Parse social links if stored as JSON
            if (is_string($mentor->social_links)) {
                $mentor->social_links = json_decode($mentor->social_links, true) ?? [];
            }
            
            return $mentor;
        });

        // Get categories for filtering
        $categories = Category::whereHas('courses', function($query) {
            $query->whereHas('mentor', function($q) {
                $q->where('role', 'mentor')->where('is_active', true);
            });
        })
        ->withCount(['courses' => function($query) {
            $query->whereHas('mentor', function($q) {
                $q->where('role', 'mentor')->where('is_active', true);
            });
        }])
        ->get();

        // Statistics for hero section
        $stats = [
            'total_mentors' => User::where('role', 'mentor')->where('is_active', true)->count(),
            'combined_revenue' => 500, // Mock data - replace with actual calculation
            'total_students' => 50, // Mock data - replace with actual calculation
        ];

        return view('mentors.index', compact('mentors', 'categories', 'stats'));
    }

    public function show(User $mentor)
    {
        // Ensure the user is actually a mentor
        if ($mentor->role !== 'mentor' || !$mentor->is_active) {
            abort(404);
        }

        // Load mentor relationships
        $mentor->load([
            'courses' => function($query) {
                $query->with(['category', 'enrollments', 'reviews'])
                      ->withCount(['enrollments', 'reviews'])
                      ->orderBy('created_at', 'desc');
            },
            'reviews' => function($query) {
                $query->with(['user', 'course'])
                      ->orderBy('created_at', 'desc')
                      ->limit(10);
            },
            'achievements' => function($query) {
                $query->orderBy('date', 'desc');
            }
        ]);

        // Calculate mentor statistics
        $mentor->students_count = $mentor->courses->sum('enrollments_count');
        $mentor->courses_count = $mentor->courses->count();
        $mentor->reviews_count = $mentor->reviews->count();
        $mentor->average_rating = $mentor->reviews->avg('rating') ?? 0;
        $mentor->revenue = rand(1, 50); // Mock data

        // Check if current user follows this mentor
        $mentor->is_followed = Auth::check() ? 
            Auth::user()->followedMentors()->where('mentor_id', $mentor->id)->exists() : false;

        // Parse JSON fields
        if (is_string($mentor->specialties)) {
            $mentor->specialties = json_decode($mentor->specialties, true) ?? [];
        }
        
        if (is_string($mentor->social_links)) {
            $mentor->social_links = json_decode($mentor->social_links, true) ?? [];
        }

        // Get rating breakdown
        $ratingBreakdown = [];
        for ($i = 1; $i <= 5; $i++) {
            $count = $mentor->reviews->where('rating', $i)->count();
            $ratingBreakdown[$i] = $mentor->reviews_count > 0 ? 
                round(($count / $mentor->reviews_count) * 100) : 0;
        }
        $mentor->rating_breakdown = $ratingBreakdown;

        // Get upcoming live calls
        $mentor->upcomingLiveCalls = LiveCall::where('mentor_id', $mentor->id)
            ->where('scheduled_at', '>', now())
            ->orderBy('scheduled_at', 'asc')
            ->limit(5)
            ->get();

        // Get related posts from mentor
        $relatedPosts = collect(); // Placeholder - implement if you have a posts system

        // Get similar mentors
        $similarMentors = User::where('role', 'mentor')
            ->where('is_active', true)
            ->where('id', '!=', $mentor->id)
            ->whereHas('courses', function($query) use ($mentor) {
                $categoryIds = $mentor->courses->pluck('category_id')->unique();
                $query->whereIn('category_id', $categoryIds);
            })
            ->with(['courses', 'reviews'])
            ->limit(3)
            ->get()
            ->map(function($similarMentor) {
                $similarMentor->average_rating = $similarMentor->reviews->avg('rating') ?? 0;
                return $similarMentor;
            });

        return view('mentors.show', compact(
            'mentor', 
            'relatedPosts', 
            'similarMentors'
        ));
    }

    public function follow(Request $request, User $mentor)
    {
        if (!Auth::check()) {
            return response()->json(['success' => false, 'message' => 'Please login first'], 401);
        }

        if ($mentor->role !== 'mentor') {
            return response()->json(['success' => false, 'message' => 'Invalid mentor'], 400);
        }

        $user = Auth::user();
        $isFollowing = $user->followedMentors()->where('mentor_id', $mentor->id)->exists();

        if ($isFollowing) {
            $user->followedMentors()->detach($mentor->id);
            $following = false;
        } else {
            $user->followedMentors()->attach($mentor->id);
            $following = true;
        }

        return response()->json([
            'success' => true,
            'following' => $following,
            'message' => $following ? 'Now following mentor' : 'Unfollowed mentor'
        ]);
    }

    public function apply()
    {
        return view('mentors.apply');
    }

    public function submitApplication(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:mentor_applications,email',
            'phone' => 'required|string|max:20',
            'title' => 'required|string|max:255',
            'bio' => 'required|string|min:100',
            'experience' => 'required|string|min:50',
            'expertise' => 'required|array|min:1',
            'achievements' => 'required|string|min:50',
            'social_links' => 'nullable|array',
            'portfolio_url' => 'nullable|url',
            'resume' => 'nullable|file|mimes:pdf,doc,docx|max:5120',
            'video_introduction' => 'nullable|url',
        ]);

        // Handle file upload
        $resumePath = null;
        if ($request->hasFile('resume')) {
            $resumePath = $request->file('resume')->store('mentor-applications', 'public');
        }

        // Create mentor application
        $application = \App\Models\MentorApplication::create([
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'title' => $request->title,
            'bio' => $request->bio,
            'experience' => $request->experience,
            'expertise' => $request->expertise,
            'achievements' => $request->achievements,
            'social_links' => $request->social_links ?? [],
            'portfolio_url' => $request->portfolio_url,
            'resume_path' => $resumePath,
            'video_introduction' => $request->video_introduction,
            'status' => 'pending',
        ]);

        // Send notification email to admin
        // \Mail::to(config('mail.admin_email'))->send(new \App\Mail\NewMentorApplication($application));

        return redirect()->route('mentors.apply.success')
            ->with('success', 'Your mentor application has been submitted successfully!');
    }

    public function applicationSuccess()
    {
        return view('mentors.application-success');
    }
}
