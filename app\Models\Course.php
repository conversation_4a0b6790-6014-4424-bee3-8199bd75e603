<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Course extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'slug',
        'description',
        'syllabus',
        'thumbnail',
        'video_preview',
        'mentor_id',
        'category_id',
        'difficulty',
        'difficulty_level', // Alternative field name
        'duration_hours',
        'total_duration', // Alternative field name
        'price',
        'required_plans',
        'sort_order',
        'is_published',
        'is_featured',
        'is_free',
        'published_at',
    ];

    protected $casts = [
        'required_plans' => 'array',
        'price' => 'decimal:2',
        'is_published' => 'boolean',
        'is_featured' => 'boolean',
        'is_free' => 'boolean',
        'published_at' => 'datetime',
    ];

    /**
     * Override the category attribute to return the relationship instead of the string
     */
    public function getCategoryAttribute($value)
    {
        // If the category relationship is loaded, return it
        if ($this->relationLoaded('categoryRelation')) {
            return $this->getRelation('categoryRelation');
        }

        // Otherwise load and return the relationship
        return $this->categoryRelation;
    }

    /**
     * Get the total duration attribute (alias for duration_hours).
     */
    public function getTotalDurationAttribute()
    {
        return $this->duration_hours ?? $this->attributes['total_duration'] ?? 0;
    }

    /**
     * Get the difficulty level attribute (alias for difficulty).
     */
    public function getDifficultyLevelAttribute()
    {
        return $this->difficulty ?? $this->attributes['difficulty_level'] ?? 'beginner';
    }

    /**
     * Get the average rating for the course.
     */
    public function getAverageRatingAttribute()
    {
        return $this->reviews()->avg('rating') ?? 0;
    }

    /**
     * Check if the course is free.
     */
    public function getIsFreeAttribute()
    {
        return $this->price == 0 || $this->attributes['is_free'] ?? false;
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($course) {
            if (empty($course->slug)) {
                $course->slug = Str::slug($course->title);
            }
        });

        static::updating(function ($course) {
            if ($course->isDirty('title') && empty($course->slug)) {
                $course->slug = Str::slug($course->title);
            }
        });
    }

    /**
     * Get the mentor that owns the course.
     */
    public function mentor()
    {
        return $this->belongsTo(User::class, 'mentor_id');
    }

    /**
     * Get the category that the course belongs to.
     */
    public function categoryRelation()
    {
        return $this->belongsTo(Category::class, 'category_id');
    }

    /**
     * Get the enrollments for the course.
     */
    public function enrollments()
    {
        return $this->hasMany(Enrollment::class);
    }

    /**
     * Get the reviews for the course.
     */
    public function reviews()
    {
        return $this->hasMany(Review::class);
    }

    /**
     * Get the certificates issued for this course.
     */
    public function certificates()
    {
        return $this->hasMany(Certificate::class);
    }

    /**
     * Get the live calls for this course.
     */
    public function liveCalls()
    {
        return $this->hasMany(LiveCall::class);
    }

    /**
     * Get the lessons for the course.
     */
    public function lessons()
    {
        return $this->hasMany(Lesson::class)->orderBy('sort_order');
    }

    /**
     * Get published lessons for the course.
     */
    public function publishedLessons()
    {
        return $this->hasMany(Lesson::class)->where('is_published', true)->orderBy('sort_order');
    }

    /**
     * Get user progress for this course.
     */
    public function userProgress()
    {
        return $this->hasMany(UserProgress::class);
    }

    /**
     * Get progress for a specific user.
     */
    public function progressForUser($userId)
    {
        return $this->userProgress()->where('user_id', $userId);
    }

    /**
     * Check if user can access this course.
     */
    public function canBeAccessedBy(User $user): bool
    {
        // Free courses can be accessed by anyone
        if ($this->price == 0 && empty($this->required_plans)) {
            return true;
        }

        // Check if user has active subscription
        $subscription = $user->activeSubscription;
        if (!$subscription) {
            return false;
        }

        // If no specific plans required, any active subscription works
        if (empty($this->required_plans)) {
            return true;
        }

        // Check if user's plan is in required plans
        return in_array($subscription->subscriptionPlan->slug, $this->required_plans);
    }

    /**
     * Get completion percentage for a user.
     */
    public function getCompletionPercentageForUser($userId): float
    {
        $totalLessons = $this->publishedLessons()->count();
        if ($totalLessons === 0) {
            return 0;
        }

        $completedLessons = $this->progressForUser($userId)->where('is_completed', true)->count();
        return ($completedLessons / $totalLessons) * 100;
    }

    /**
     * Scope to get published courses.
     */
    public function scopePublished($query)
    {
        return $query->where('is_published', true);
    }

    /**
     * Scope to get featured courses.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope to filter by category.
     */
    public function scopeCategory($query, $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    /**
     * Scope to filter by difficulty.
     */
    public function scopeDifficulty($query, $difficulty)
    {
        return $query->where('difficulty', $difficulty);
    }

    /**
     * Scope to order by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('created_at', 'desc');
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName()
    {
        return 'slug';
    }
}
