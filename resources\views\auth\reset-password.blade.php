@extends('layouts.guest')

@section('title', 'Reset Password')

@section('content')
<div class="auth-container">
    <div class="auth-card">
        <div class="auth-header">
            <div class="logo">
                <img src="{{ asset('images/logo.png') }}" alt="The Real World" class="logo-img">
            </div>
            <h1 class="auth-title">Reset Password</h1>
            <p class="auth-subtitle">Enter your new password below to reset your account password.</p>
        </div>

        <form method="POST" action="{{ route('password.update') }}" class="auth-form">
            @csrf
            <input type="hidden" name="token" value="{{ $token }}">

            <div class="form-group">
                <label for="email" class="form-label">
                    <i class="fas fa-envelope"></i>
                    Email Address
                </label>
                <input id="email" type="email" 
                       class="form-control @error('email') is-invalid @enderror" 
                       name="email" 
                       value="{{ $email ?? old('email') }}" 
                       required 
                       autocomplete="email"
                       readonly>
                @error('email')
                    <div class="invalid-feedback">
                        <i class="fas fa-exclamation-circle"></i>
                        {{ $message }}
                    </div>
                @enderror
            </div>

            <div class="form-group">
                <label for="password" class="form-label">
                    <i class="fas fa-lock"></i>
                    New Password
                </label>
                <div class="password-input">
                    <input id="password" type="password" 
                           class="form-control @error('password') is-invalid @enderror" 
                           name="password" 
                           required 
                           autocomplete="new-password"
                           placeholder="Enter your new password">
                    <button type="button" class="password-toggle" onclick="togglePassword('password')">
                        <i class="fas fa-eye" id="password-eye"></i>
                    </button>
                </div>
                @error('password')
                    <div class="invalid-feedback">
                        <i class="fas fa-exclamation-circle"></i>
                        {{ $message }}
                    </div>
                @enderror
                <div class="password-requirements">
                    <small class="text-muted">
                        <i class="fas fa-info-circle"></i>
                        Password must be at least 8 characters long
                    </small>
                </div>
            </div>

            <div class="form-group">
                <label for="password_confirmation" class="form-label">
                    <i class="fas fa-lock"></i>
                    Confirm New Password
                </label>
                <div class="password-input">
                    <input id="password_confirmation" type="password" 
                           class="form-control" 
                           name="password_confirmation" 
                           required 
                           autocomplete="new-password"
                           placeholder="Confirm your new password">
                    <button type="button" class="password-toggle" onclick="togglePassword('password_confirmation')">
                        <i class="fas fa-eye" id="password_confirmation-eye"></i>
                    </button>
                </div>
            </div>

            <button type="submit" class="btn btn-primary btn-block btn-lg">
                <i class="fas fa-key"></i>
                Reset Password
            </button>
        </form>

        <div class="auth-footer">
            <div class="auth-links">
                <a href="{{ route('login') }}" class="auth-link">
                    <i class="fas fa-arrow-left"></i>
                    Back to Login
                </a>
            </div>
        </div>
    </div>

    <div class="auth-background">
        <div class="auth-bg-overlay"></div>
        <div class="auth-bg-content">
            <h2>Secure Reset</h2>
            <p>Create a strong new password to keep your account secure and continue your journey to financial freedom.</p>
            <div class="auth-features">
                <div class="feature">
                    <i class="fas fa-shield-alt"></i>
                    <span>Secure Encryption</span>
                </div>
                <div class="feature">
                    <i class="fas fa-lock"></i>
                    <span>Protected Account</span>
                </div>
                <div class="feature">
                    <i class="fas fa-check-circle"></i>
                    <span>Instant Access</span>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 2rem 1rem;
    position: relative;
}

.auth-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    padding: 3rem;
    width: 100%;
    max-width: 450px;
    position: relative;
    z-index: 2;
}

.auth-header {
    text-align: center;
    margin-bottom: 2rem;
}

.logo-img {
    height: 60px;
    margin-bottom: 1rem;
}

.auth-title {
    font-size: 2rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.auth-subtitle {
    color: #718096;
    font-size: 1rem;
    line-height: 1.5;
}

.auth-form {
    margin-bottom: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: flex;
    align-items: center;
    font-weight: 600;
    color: #4a5568;
    margin-bottom: 0.5rem;
}

.form-label i {
    margin-right: 0.5rem;
    color: #667eea;
}

.password-input {
    position: relative;
}

.form-control {
    height: 50px;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    padding: 0 3rem 0 1rem;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-control.is-invalid {
    border-color: #e53e3e;
}

.form-control[readonly] {
    background-color: #f7fafc;
    color: #718096;
}

.password-toggle {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #718096;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.password-toggle:hover {
    color: #4a5568;
}

.password-requirements {
    margin-top: 0.5rem;
}

.invalid-feedback {
    display: flex;
    align-items: center;
    color: #e53e3e;
    font-size: 0.875rem;
    margin-top: 0.5rem;
}

.invalid-feedback i {
    margin-right: 0.5rem;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    height: 50px;
    border-radius: 10px;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.auth-footer {
    text-align: center;
}

.auth-links {
    display: flex;
    align-items: center;
    justify-content: center;
}

.auth-link {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    display: flex;
    align-items: center;
    transition: color 0.3s ease;
}

.auth-link:hover {
    color: #764ba2;
    text-decoration: none;
}

.auth-link i {
    margin-right: 0.5rem;
}

.auth-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 2rem;
    z-index: 1;
}

.auth-bg-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
}

.auth-bg-content {
    max-width: 400px;
    color: white;
    text-align: center;
    position: relative;
    z-index: 2;
    margin-right: 2rem;
}

.auth-bg-content h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.auth-bg-content p {
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.auth-features {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.feature {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-weight: 500;
}

.feature i {
    font-size: 1.2rem;
    color: #ffd700;
}

@media (max-width: 768px) {
    .auth-container {
        padding: 1rem;
    }
    
    .auth-card {
        padding: 2rem;
    }
    
    .auth-background {
        display: none;
    }
    
    .auth-title {
        font-size: 1.75rem;
    }
    
    .auth-bg-content h2 {
        font-size: 2rem;
    }
}
</style>
@endpush

@push('scripts')
<script>
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const eye = document.getElementById(fieldId + '-eye');
    
    if (field.type === 'password') {
        field.type = 'text';
        eye.classList.remove('fa-eye');
        eye.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        eye.classList.remove('fa-eye-slash');
        eye.classList.add('fa-eye');
    }
}
</script>
@endpush
