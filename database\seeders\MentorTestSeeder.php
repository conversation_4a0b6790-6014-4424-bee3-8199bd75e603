<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Course;
use App\Models\Category;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class MentorTestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create test mentor user
        $mentor = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'mentor',
                'title' => 'Entrepreneur & Business Coach',
                'bio' => 'Former world champion kickboxer turned successful entrepreneur. I teach people how to escape the matrix and build wealth.',
                'specialties' => ['Business', 'Entrepreneurship', 'Mindset', 'Wealth Building'],
                'social_links' => [
                    'twitter' => 'https://twitter.com/cobratate',
                    'instagram' => 'https://instagram.com/cobratate',
                    'youtube' => 'https://youtube.com/@TateSpeech'
                ],
                'is_verified' => true,
                'is_featured' => true,
                'is_active' => true,
                'average_rating' => 4.8,
                'email_verified_at' => now(),
            ]
        );

        // Create test student user
        $student = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'John Student',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'student',
                'is_active' => true,
                'email_verified_at' => now(),
            ]
        );

        // Create test admin user
        $admin = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin User',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'admin',
                'is_active' => true,
                'email_verified_at' => now(),
            ]
        );

        // Create test category
        $category = Category::updateOrCreate(
            ['name' => 'Business & Entrepreneurship'],
            [
                'name' => 'Business & Entrepreneurship',
                'slug' => 'business-entrepreneurship',
                'description' => 'Learn how to start and grow successful businesses',
                'is_active' => true,
                'sort_order' => 1,
            ]
        );

        // Create test courses
        $course1 = Course::updateOrCreate(
            ['slug' => 'hustlers-university'],
            [
                'title' => 'Hustlers University',
                'slug' => 'hustlers-university',
                'description' => 'Learn the fundamentals of making money online and building wealth through various income streams.',
                'syllabus' => 'Module 1: Mindset\nModule 2: Online Business\nModule 3: Investments\nModule 4: Scaling',
                'mentor_id' => $mentor->id,
                'category_id' => $category->id,
                'category' => $category->name, // For backward compatibility
                'difficulty' => 'beginner',
                'difficulty_level' => 'beginner',
                'duration_hours' => 40,
                'total_duration' => 40,
                'price' => 49.99,
                'is_free' => false,
                'is_published' => true,
                'is_featured' => true,
                'average_rating' => 4.8,
                'published_at' => now(),
            ]
        );

        $course2 = Course::updateOrCreate(
            ['slug' => 'advanced-business-strategies'],
            [
                'title' => 'Advanced Business Strategies',
                'slug' => 'advanced-business-strategies',
                'description' => 'Advanced techniques for scaling your business and maximizing profits.',
                'syllabus' => 'Module 1: Market Analysis\nModule 2: Scaling Strategies\nModule 3: Team Building\nModule 4: Exit Strategies',
                'mentor_id' => $mentor->id,
                'category_id' => $category->id,
                'category' => $category->name,
                'difficulty' => 'advanced',
                'difficulty_level' => 'advanced',
                'duration_hours' => 60,
                'total_duration' => 60,
                'price' => 99.99,
                'is_free' => false,
                'is_published' => true,
                'is_featured' => false,
                'average_rating' => 4.7,
                'published_at' => now(),
            ]
        );

        // Create test enrollments if tables exist
        try {
            if (class_exists('App\Models\Enrollment')) {
                \App\Models\Enrollment::updateOrCreate(
                    ['user_id' => $student->id, 'course_id' => $course1->id],
                    [
                        'user_id' => $student->id,
                        'course_id' => $course1->id,
                        'enrolled_at' => now()->subDays(10),
                        'started_at' => now()->subDays(8),
                        'last_accessed_at' => now()->subHours(2),
                        'progress_percentage' => 35.5,
                        'is_active' => true,
                    ]
                );

                \App\Models\Enrollment::updateOrCreate(
                    ['user_id' => $student->id, 'course_id' => $course2->id],
                    [
                        'user_id' => $student->id,
                        'course_id' => $course2->id,
                        'enrolled_at' => now()->subDays(5),
                        'last_accessed_at' => now()->subDays(1),
                        'progress_percentage' => 0,
                        'is_active' => true,
                    ]
                );
            }
        } catch (\Exception $e) {
            // Tables don't exist yet, skip
        }

        // Create test reviews if tables exist
        try {
            if (class_exists('App\Models\Review')) {
                \App\Models\Review::updateOrCreate(
                    ['user_id' => $student->id, 'course_id' => $course1->id],
                    [
                        'user_id' => $student->id,
                        'course_id' => $course1->id,
                        'mentor_id' => $mentor->id,
                        'rating' => 5,
                        'title' => 'Excellent Course!',
                        'comment' => 'This course completely changed my perspective on business. Andrew\'s teaching style is direct and actionable. Highly recommended!',
                        'is_verified_purchase' => true,
                        'is_approved' => true,
                    ]
                );
            }
        } catch (\Exception $e) {
            // Tables don't exist yet, skip
        }

        $this->command->info('Test mentor data seeded successfully!');
        $this->command->info('Mentor login: <EMAIL> / password');
        $this->command->info('Student login: <EMAIL> / password');
        $this->command->info('Admin login: <EMAIL> / password');
    }
}
