<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class CommunityPost extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'title',
        'content',
        'category',
        'tags',
        'is_pinned',
        'is_locked',
        'views_count',
        'likes_count',
        'comments_count',
        'last_activity_at',
    ];

    protected $casts = [
        'tags' => 'array',
        'is_pinned' => 'boolean',
        'is_locked' => 'boolean',
        'last_activity_at' => 'datetime',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($post) {
            $post->last_activity_at = now();
        });
    }

    /**
     * Get the user that owns the post.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the comments for the post.
     */
    public function comments()
    {
        return $this->hasMany(CommunityComment::class, 'post_id');
    }

    /**
     * Get the top-level comments for the post.
     */
    public function topLevelComments()
    {
        return $this->hasMany(CommunityComment::class, 'post_id')
            ->whereNull('parent_id')
            ->orderBy('created_at');
    }

    /**
     * Get users who liked this post.
     */
    public function likedBy()
    {
        return $this->belongsToMany(User::class, 'community_post_likes')
            ->withTimestamps();
    }

    /**
     * Check if user has liked this post.
     */
    public function isLikedBy($userId): bool
    {
        return $this->likedBy()->where('user_id', $userId)->exists();
    }

    /**
     * Increment views count.
     */
    public function incrementViews()
    {
        $this->increment('views_count');
    }

    /**
     * Update last activity timestamp.
     */
    public function updateLastActivity()
    {
        $this->update(['last_activity_at' => now()]);
    }

    /**
     * Get excerpt of content.
     */
    public function getExcerptAttribute($length = 150): string
    {
        return Str::limit(strip_tags($this->content), $length);
    }

    /**
     * Get formatted tags.
     */
    public function getFormattedTagsAttribute(): array
    {
        return $this->tags ?? [];
    }

    /**
     * Scope to get pinned posts.
     */
    public function scopePinned($query)
    {
        return $query->where('is_pinned', true);
    }

    /**
     * Scope to get posts by category.
     */
    public function scopeCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Scope to search posts.
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('title', 'like', '%' . $search . '%')
              ->orWhere('content', 'like', '%' . $search . '%');
        });
    }

    /**
     * Scope to order by activity.
     */
    public function scopeByActivity($query)
    {
        return $query->orderBy('is_pinned', 'desc')
                    ->orderBy('last_activity_at', 'desc');
    }

    /**
     * Scope to order by popularity.
     */
    public function scopeByPopularity($query)
    {
        return $query->orderBy('is_pinned', 'desc')
                    ->orderBy('likes_count', 'desc')
                    ->orderBy('comments_count', 'desc');
    }

    /**
     * Scope to order by newest.
     */
    public function scopeByNewest($query)
    {
        return $query->orderBy('is_pinned', 'desc')
                    ->orderBy('created_at', 'desc');
    }
}
