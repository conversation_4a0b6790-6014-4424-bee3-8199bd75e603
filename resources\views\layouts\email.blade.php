<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>{{ config('app.name', 'The Real World') }}</title>
    
    <style>
        /* Reset styles */
        body, table, td, p, a, li, blockquote {
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }
        
        table, td {
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }
        
        img {
            -ms-interpolation-mode: bicubic;
            border: 0;
            height: auto;
            line-height: 100%;
            outline: none;
            text-decoration: none;
        }
        
        /* Base styles */
        body {
            margin: 0 !important;
            padding: 0 !important;
            background-color: #f8fafc;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 16px;
            line-height: 1.6;
            color: #374151;
        }
        
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
        }
        
        .email-header {
            background: linear-gradient(135deg, #2563eb 0%, #7c3aed 100%);
            padding: 40px 30px;
            text-align: center;
        }
        
        .email-logo {
            display: inline-block;
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            line-height: 60px;
            text-align: center;
            color: #ffffff;
            font-weight: bold;
            font-size: 20px;
            margin-bottom: 20px;
        }
        
        .email-title {
            color: #ffffff;
            font-size: 28px;
            font-weight: bold;
            margin: 0;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .email-subtitle {
            color: rgba(255, 255, 255, 0.9);
            font-size: 16px;
            margin: 10px 0 0 0;
        }
        
        .email-content {
            padding: 40px 30px;
        }
        
        .email-content h1 {
            color: #111827;
            font-size: 24px;
            font-weight: bold;
            margin: 0 0 20px 0;
            line-height: 1.3;
        }
        
        .email-content h2 {
            color: #374151;
            font-size: 20px;
            font-weight: 600;
            margin: 30px 0 15px 0;
            line-height: 1.3;
        }
        
        .email-content p {
            margin: 0 0 20px 0;
            line-height: 1.6;
        }
        
        .email-content ul {
            margin: 0 0 20px 0;
            padding-left: 20px;
        }
        
        .email-content li {
            margin-bottom: 8px;
        }
        
        .button {
            display: inline-block;
            padding: 16px 32px;
            background: linear-gradient(135deg, #2563eb 0%, #7c3aed 100%);
            color: #ffffff !important;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            text-align: center;
            margin: 20px 0;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
        }
        
        .button-secondary {
            background: #f3f4f6;
            color: #374151 !important;
            border: 1px solid #d1d5db;
        }
        
        .alert {
            padding: 16px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .alert-success {
            background-color: #ecfdf5;
            border: 1px solid #a7f3d0;
            color: #065f46;
        }
        
        .alert-warning {
            background-color: #fffbeb;
            border: 1px solid #fcd34d;
            color: #92400e;
        }
        
        .alert-error {
            background-color: #fef2f2;
            border: 1px solid #fca5a5;
            color: #991b1b;
        }
        
        .alert-info {
            background-color: #eff6ff;
            border: 1px solid #93c5fd;
            color: #1e40af;
        }
        
        .divider {
            height: 1px;
            background-color: #e5e7eb;
            margin: 30px 0;
        }
        
        .email-footer {
            background-color: #f9fafb;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #e5e7eb;
        }
        
        .email-footer p {
            margin: 0 0 10px 0;
            font-size: 14px;
            color: #6b7280;
        }
        
        .email-footer a {
            color: #2563eb;
            text-decoration: none;
        }
        
        .email-footer a:hover {
            text-decoration: underline;
        }
        
        .social-links {
            margin: 20px 0;
        }
        
        .social-links a {
            display: inline-block;
            margin: 0 10px;
            color: #6b7280;
            text-decoration: none;
        }
        
        .unsubscribe {
            font-size: 12px;
            color: #9ca3af;
            margin-top: 20px;
        }
        
        /* Responsive styles */
        @media only screen and (max-width: 600px) {
            .email-container {
                width: 100% !important;
            }
            
            .email-header,
            .email-content,
            .email-footer {
                padding: 20px !important;
            }
            
            .email-title {
                font-size: 24px !important;
            }
            
            .email-content h1 {
                font-size: 20px !important;
            }
            
            .button {
                display: block !important;
                width: 100% !important;
                box-sizing: border-box;
            }
        }
    </style>
</head>
<body>
    <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
        <tr>
            <td style="padding: 20px 0;">
                <div class="email-container">
                    <!-- Header -->
                    <div class="email-header">
                        <div class="email-logo">TRW</div>
                        <h1 class="email-title">The Real World</h1>
                        <p class="email-subtitle">Escape the Matrix</p>
                    </div>
                    
                    <!-- Content -->
                    <div class="email-content">
                        @yield('content')
                    </div>
                    
                    <!-- Footer -->
                    <div class="email-footer">
                        <p><strong>The Real World</strong></p>
                        <p>Join the ultimate community for entrepreneurs</p>
                        
                        <div class="social-links">
                            <a href="#">Twitter</a>
                            <a href="#">Instagram</a>
                            <a href="#">YouTube</a>
                        </div>
                        
                        <p>
                            <a href="{{ route('home') }}">Visit Website</a> |
                            <a href="{{ route('contact') }}">Contact Support</a> |
                            <a href="{{ route('privacy') }}">Privacy Policy</a>
                        </p>
                        
                        <div class="unsubscribe">
                            <p>
                                You're receiving this email because you're a member of The Real World.<br>
                                <a href="#">Unsubscribe</a> | <a href="#">Update Preferences</a>
                            </p>
                            <p>&copy; {{ date('Y') }} The Real World. All rights reserved.</p>
                        </div>
                    </div>
                </div>
            </td>
        </tr>
    </table>
</body>
</html>
