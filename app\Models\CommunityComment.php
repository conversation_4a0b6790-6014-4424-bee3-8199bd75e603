<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CommunityComment extends Model
{
    use HasFactory;

    protected $fillable = [
        'post_id',
        'user_id',
        'parent_id',
        'content',
        'status',
        'is_reported',
        'report_reason',
        'likes_count',
        'moderated_at',
        'moderated_by',
    ];

    protected $casts = [
        'is_reported' => 'boolean',
        'moderated_at' => 'datetime',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::created(function ($comment) {
            // Update post's comment count and last activity
            $comment->post->increment('comments_count');
            $comment->post->updateLastActivity();
        });

        static::deleted(function ($comment) {
            // Update post's comment count
            $comment->post->decrement('comments_count');
        });
    }

    /**
     * Get the post that owns the comment.
     */
    public function post()
    {
        return $this->belongsTo(CommunityPost::class, 'post_id');
    }

    /**
     * Get the user that owns the comment.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the parent comment.
     */
    public function parent()
    {
        return $this->belongsTo(CommunityComment::class, 'parent_id');
    }

    /**
     * Get the child comments (replies).
     */
    public function replies()
    {
        return $this->hasMany(CommunityComment::class, 'parent_id')
            ->orderBy('created_at');
    }

    /**
     * Get users who liked this comment.
     */
    public function likedBy()
    {
        return $this->belongsToMany(User::class, 'community_comment_likes')
            ->withTimestamps();
    }

    /**
     * Check if user has liked this comment.
     */
    public function isLikedBy($userId): bool
    {
        return $this->likedBy()->where('user_id', $userId)->exists();
    }

    /**
     * Check if this is a reply.
     */
    public function getIsReplyAttribute(): bool
    {
        return !is_null($this->parent_id);
    }

    /**
     * Scope to get top-level comments.
     */
    public function scopeTopLevel($query)
    {
        return $query->whereNull('parent_id');
    }

    /**
     * Scope to get replies.
     */
    public function scopeReplies($query)
    {
        return $query->whereNotNull('parent_id');
    }
}
