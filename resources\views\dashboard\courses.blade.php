@extends('layouts.dashboard')

@section('content')
<div class="py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">My Courses</h1>
                    <p class="text-gray-600 mt-1">Track your learning progress and continue your journey</p>
                </div>
                <a href="{{ route('courses.index') }}" 
                   class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition duration-300">
                    Browse More Courses
                </a>
            </div>
        </div>

        <!-- Progress Overview -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="bg-blue-100 rounded-full p-3 mr-4">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="text-2xl font-bold text-gray-900">{{ $coursesWithProgress->where('is_started', true)->count() }}</p>
                        <p class="text-gray-600 text-sm">Courses Started</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="bg-green-100 rounded-full p-3 mr-4">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="text-2xl font-bold text-gray-900">{{ $coursesWithProgress->where('progress_percentage', 100)->count() }}</p>
                        <p class="text-gray-600 text-sm">Courses Completed</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="bg-purple-100 rounded-full p-3 mr-4">
                        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="text-2xl font-bold text-gray-900">{{ number_format($coursesWithProgress->avg('progress_percentage'), 1) }}%</p>
                        <p class="text-gray-600 text-sm">Average Progress</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Course Filters -->
        <div class="bg-white rounded-lg shadow-md p-4 mb-6">
            <div class="flex flex-wrap items-center gap-4">
                <button onclick="filterCourses('all')" class="filter-btn active px-4 py-2 rounded-lg text-sm font-medium transition duration-300">
                    All Courses
                </button>
                <button onclick="filterCourses('in-progress')" class="filter-btn px-4 py-2 rounded-lg text-sm font-medium transition duration-300">
                    In Progress
                </button>
                <button onclick="filterCourses('completed')" class="filter-btn px-4 py-2 rounded-lg text-sm font-medium transition duration-300">
                    Completed
                </button>
                <button onclick="filterCourses('not-started')" class="filter-btn px-4 py-2 rounded-lg text-sm font-medium transition duration-300">
                    Not Started
                </button>
            </div>
        </div>

        <!-- Courses Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            @foreach($coursesWithProgress as $course)
            <div class="course-card bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition duration-300" 
                 data-status="{{ $course->progress_percentage == 100 ? 'completed' : ($course->is_started ? 'in-progress' : 'not-started') }}">
                
                <!-- Course Thumbnail -->
                <div class="relative">
                    @if($course->thumbnail)
                        <img src="{{ $course->thumbnail }}" alt="{{ $course->title }}" class="w-full h-48 object-cover">
                    @else
                        <div class="w-full h-48 bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                            <span class="text-white text-3xl font-bold">{{ substr($course->title, 0, 1) }}</span>
                        </div>
                    @endif
                    
                    <!-- Progress Overlay -->
                    @if($course->progress_percentage > 0)
                        <div class="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white p-2">
                            <div class="flex justify-between text-sm mb-1">
                                <span>{{ $course->completed_lessons }}/{{ $course->total_lessons }} lessons</span>
                                <span>{{ number_format($course->progress_percentage, 0) }}%</span>
                            </div>
                            <div class="w-full bg-gray-600 rounded-full h-1">
                                <div class="bg-green-400 h-1 rounded-full" style="width: {{ $course->progress_percentage }}%"></div>
                            </div>
                        </div>
                    @endif
                    
                    <!-- Status Badge -->
                    <div class="absolute top-2 right-2">
                        @if($course->progress_percentage == 100)
                            <span class="bg-green-500 text-white px-2 py-1 rounded text-xs font-medium">Completed</span>
                        @elseif($course->is_started)
                            <span class="bg-blue-500 text-white px-2 py-1 rounded text-xs font-medium">In Progress</span>
                        @else
                            <span class="bg-gray-500 text-white px-2 py-1 rounded text-xs font-medium">Not Started</span>
                        @endif
                    </div>
                </div>
                
                <!-- Course Info -->
                <div class="p-6">
                    <div class="flex items-center justify-between mb-2">
                        <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">{{ $course->category }}</span>
                        <span class="text-xs text-gray-500 capitalize">{{ $course->difficulty }}</span>
                    </div>
                    
                    <h3 class="font-semibold text-lg mb-2">{{ $course->title }}</h3>
                    <p class="text-gray-600 text-sm mb-4 line-clamp-2">{{ $course->description }}</p>
                    
                    <div class="flex items-center mb-4">
                        @if($course->mentor->avatar)
                            <img src="{{ $course->mentor->avatar }}" alt="{{ $course->mentor->name }}" class="w-6 h-6 rounded-full mr-2">
                        @else
                            <div class="w-6 h-6 bg-gray-300 rounded-full mr-2 flex items-center justify-center">
                                <span class="text-xs text-gray-600">{{ substr($course->mentor->name, 0, 1) }}</span>
                            </div>
                        @endif
                        <span class="text-sm text-gray-700">{{ $course->mentor->name }}</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-600">
                            <span class="flex items-center">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                {{ $course->duration_hours }}h
                            </span>
                        </div>
                        
                        <a href="{{ route('courses.show', $course) }}" 
                           class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm font-medium transition duration-300">
                            @if($course->progress_percentage == 100)
                                Review
                            @elseif($course->is_started)
                                Continue
                            @else
                                Start Course
                            @endif
                        </a>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
        
        @if($coursesWithProgress->isEmpty())
        <div class="text-center py-12">
            <div class="max-w-md mx-auto">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No courses available</h3>
                <p class="mt-1 text-sm text-gray-500">You need an active subscription to access courses.</p>
                <div class="mt-6">
                    <a href="{{ route('subscriptions.index') }}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition duration-300">
                        View Subscription Plans
                    </a>
                </div>
            </div>
        </div>
        @endif
    </div>
</div>

<script>
function filterCourses(status) {
    const cards = document.querySelectorAll('.course-card');
    const buttons = document.querySelectorAll('.filter-btn');
    
    // Update button states
    buttons.forEach(btn => btn.classList.remove('active', 'bg-blue-600', 'text-white'));
    event.target.classList.add('active', 'bg-blue-600', 'text-white');
    
    // Filter cards
    cards.forEach(card => {
        if (status === 'all' || card.dataset.status === status) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
}

// Initialize filter buttons
document.addEventListener('DOMContentLoaded', function() {
    const activeBtn = document.querySelector('.filter-btn.active');
    activeBtn.classList.add('bg-blue-600', 'text-white');
    
    const otherBtns = document.querySelectorAll('.filter-btn:not(.active)');
    otherBtns.forEach(btn => {
        btn.classList.add('bg-gray-100', 'text-gray-700', 'hover:bg-gray-200');
    });
});
</script>
@endsection
