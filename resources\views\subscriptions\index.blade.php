@extends('layouts.app')

@section('content')
<div class="py-12">
    <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <!-- Page Header -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
            <div class="p-6 bg-white border-b border-gray-200">
                <h1 class="text-2xl font-bold text-gray-900">Subscription Plans</h1>
                <p class="text-gray-600 mt-1">Choose the plan that's right for you</p>
            </div>
        </div>

        <!-- Current Subscription Status -->
        @if($userSubscription)
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
            <div class="p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Current Subscription</h2>
                
                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-lg font-medium text-green-800">{{ $userSubscription->subscriptionPlan->name }} Plan</h3>
                            <p class="text-green-700">
                                ${{ number_format($userSubscription->amount, 2) }} / {{ $userSubscription->billing_cycle }}
                            </p>
                            <p class="text-sm text-green-600 mt-1">
                                @if($userSubscription->status === 'cancelled')
                                    Cancelled - Access until {{ $userSubscription->current_period_end->format('M d, Y') }}
                                @else
                                    Next billing: {{ $userSubscription->current_period_end->format('M d, Y') }}
                                @endif
                            </p>
                        </div>
                        
                        <div class="flex space-x-2">
                            @if($userSubscription->status === 'cancelled')
                                <form method="POST" action="{{ route('subscriptions.resume') }}">
                                    @csrf
                                    <button type="submit" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded text-sm">
                                        Resume
                                    </button>
                                </form>
                            @else
                                <form method="POST" action="{{ route('subscriptions.cancel') }}" 
                                      onsubmit="return confirm('Are you sure you want to cancel your subscription?')">
                                    @csrf
                                    <button type="submit" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded text-sm">
                                        Cancel
                                    </button>
                                </form>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @endif

        <!-- Available Plans -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-6">Available Plans</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    @foreach($plans as $plan)
                    <div class="border rounded-lg p-6 {{ $plan->is_featured ? 'ring-2 ring-blue-500 transform scale-105' : 'border-gray-200' }}">
                        @if($plan->is_featured)
                            <div class="bg-blue-500 text-white text-center py-1 px-4 rounded-full text-sm font-medium mb-4">
                                Most Popular
                            </div>
                        @endif
                        
                        <h3 class="text-xl font-bold text-center mb-2">{{ $plan->name }}</h3>
                        
                        <!-- Monthly Price -->
                        <div class="text-center mb-4">
                            <span class="text-3xl font-bold">${{ number_format($plan->monthly_price, 0) }}</span>
                            <span class="text-gray-600">/month</span>
                        </div>
                        
                        <!-- Yearly Price -->
                        <div class="text-center mb-4">
                            <span class="text-lg font-semibold text-green-600">${{ number_format($plan->yearly_price, 0) }}</span>
                            <span class="text-sm text-green-600">/year</span>
                            <div class="text-xs text-green-600">
                                Save ${{ number_format(($plan->monthly_price * 12) - $plan->yearly_price, 0) }}
                            </div>
                        </div>
                        
                        <p class="text-gray-600 text-center mb-6">{{ $plan->description }}</p>
                        
                        <ul class="space-y-2 mb-6">
                            @foreach($plan->features as $feature)
                            <li class="flex items-center text-sm">
                                <svg class="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                {{ $feature }}
                            </li>
                            @endforeach
                        </ul>
                        
                        @if(!$userSubscription || $userSubscription->subscriptionPlan->id !== $plan->id)
                        <div class="space-y-2">
                            <a href="{{ route('subscriptions.checkout', ['plan' => $plan, 'billing' => 'monthly']) }}" 
                               class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded text-center block transition duration-300">
                                Choose Monthly
                            </a>
                            <a href="{{ route('subscriptions.checkout', ['plan' => $plan, 'billing' => 'yearly']) }}" 
                               class="w-full bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded text-center block transition duration-300">
                                Choose Yearly
                            </a>
                        </div>
                        @else
                        <div class="text-center">
                            <span class="text-green-600 font-medium">Current Plan</span>
                        </div>
                        @endif
                    </div>
                    @endforeach
                </div>
            </div>
        </div>

        <!-- Payment Method Management -->
        @if($userSubscription)
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mt-6">
            <div class="p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Payment Method</h2>
                
                @if(Auth::user()->hasDefaultPaymentMethod())
                    <div class="flex items-center justify-between bg-gray-50 p-4 rounded-lg">
                        <div>
                            <p class="font-medium">**** **** **** {{ Auth::user()->pm_last_four }}</p>
                            <p class="text-sm text-gray-600">{{ ucfirst(Auth::user()->pm_type) }}</p>
                        </div>
                        <button onclick="updatePaymentMethod()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm">
                            Update
                        </button>
                    </div>
                @else
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                        <p class="text-yellow-800">No payment method on file</p>
                        <button onclick="updatePaymentMethod()" class="mt-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm">
                            Add Payment Method
                        </button>
                    </div>
                @endif
            </div>
        </div>
        @endif
    </div>
</div>

<script>
function updatePaymentMethod() {
    // This would integrate with Stripe Elements for payment method updates
    alert('Payment method update functionality would be implemented here with Stripe Elements');
}
</script>
@endsection
