<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\SubscriptionPlan;

class SubscriptionPlanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $plans = [
            [
                'name' => 'Cadet',
                'slug' => 'cadet',
                'description' => 'Perfect for beginners starting their journey to financial freedom.',
                'monthly_price' => 49.99,
                'yearly_price' => 499.99,
                'features' => [
                    'Access to basic courses',
                    'Community chat access',
                    'Monthly progress reports',
                    'Email support'
                ],
                'max_courses' => 5,
                'live_calls_access' => false,
                'community_access' => true,
                'mentor_access' => false,
                'sort_order' => 1,
                'is_active' => true,
                'is_featured' => false,
            ],
            [
                'name' => 'Challenger',
                'slug' => 'challenger',
                'description' => 'For those ready to take their skills to the next level.',
                'monthly_price' => 99.99,
                'yearly_price' => 999.99,
                'features' => [
                    'Access to all courses',
                    'Community chat access',
                    'Monthly live calls',
                    'Weekly progress reports',
                    'Priority email support',
                    'Downloadable resources'
                ],
                'max_courses' => null, // unlimited
                'live_calls_access' => true,
                'community_access' => true,
                'mentor_access' => false,
                'sort_order' => 2,
                'is_active' => true,
                'is_featured' => true,
            ],
            [
                'name' => 'Hero',
                'slug' => 'hero',
                'description' => 'Advanced training for serious entrepreneurs.',
                'monthly_price' => 199.99,
                'yearly_price' => 1999.99,
                'features' => [
                    'Everything in Challenger',
                    'Weekly live calls',
                    'Direct mentor access',
                    'Private community channels',
                    '1-on-1 monthly mentoring session',
                    'Advanced course materials',
                    'Priority support'
                ],
                'max_courses' => null,
                'live_calls_access' => true,
                'community_access' => true,
                'mentor_access' => true,
                'sort_order' => 3,
                'is_active' => true,
                'is_featured' => false,
            ],
            [
                'name' => 'Champion',
                'slug' => 'champion',
                'description' => 'Elite tier for those who want everything and more.',
                'monthly_price' => 499.99,
                'yearly_price' => 4999.99,
                'features' => [
                    'Everything in Hero',
                    'Daily live calls',
                    'Unlimited mentor access',
                    'Exclusive champion community',
                    'Weekly 1-on-1 sessions',
                    'Custom business plan review',
                    'VIP support',
                    'Early access to new content'
                ],
                'max_courses' => null,
                'live_calls_access' => true,
                'community_access' => true,
                'mentor_access' => true,
                'sort_order' => 4,
                'is_active' => true,
                'is_featured' => false,
            ],
        ];

        foreach ($plans as $plan) {
            SubscriptionPlan::firstOrCreate(
                ['slug' => $plan['slug']],
                $plan
            );
        }
    }
}
