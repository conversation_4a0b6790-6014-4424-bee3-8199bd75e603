<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\UserSubscription;
use App\Models\SubscriptionPlan;
use Illuminate\Database\Seeder;

class SubscriptionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $plans = SubscriptionPlan::all();
        $users = User::whereHas('roles', function($q) {
            $q->where('name', 'user');
        })->get();

        // Create active subscriptions for 60% of users
        $activeUsers = $users->random(intval($users->count() * 0.6));

        foreach ($activeUsers as $user) {
            $plan = $plans->random();

            UserSubscription::create([
                'user_id' => $user->id,
                'subscription_plan_id' => $plan->id,
                'status' => 'active',
                'billing_cycle' => rand(0, 1) ? 'monthly' : 'yearly',
                'amount' => rand(0, 1) ? $plan->monthly_price : $plan->yearly_price,
                'currency' => 'USD',
                'current_period_start' => now()->subDays(rand(1, 30)),
                'current_period_end' => now()->addDays(rand(30, 365)),
                'stripe_subscription_id' => 'sub_' . \Str::random(24),
            ]);
        }

        // Create some expired subscriptions
        $expiredUsers = $users->diff($activeUsers)->random(intval($users->count() * 0.2));

        foreach ($expiredUsers as $user) {
            $plan = $plans->random();

            UserSubscription::create([
                'user_id' => $user->id,
                'subscription_plan_id' => $plan->id,
                'status' => 'expired',
                'billing_cycle' => rand(0, 1) ? 'monthly' : 'yearly',
                'amount' => rand(0, 1) ? $plan->monthly_price : $plan->yearly_price,
                'currency' => 'USD',
                'current_period_start' => now()->subDays(rand(60, 180)),
                'current_period_end' => now()->subDays(rand(1, 30)),
                'stripe_subscription_id' => 'sub_' . \Str::random(24),
                'ends_at' => now()->subDays(rand(1, 30)),
            ]);
        }

        // Create some cancelled subscriptions
        $cancelledUsers = $users->diff($activeUsers)->diff($expiredUsers)->random(intval($users->count() * 0.1));

        foreach ($cancelledUsers as $user) {
            $plan = $plans->random();

            UserSubscription::create([
                'user_id' => $user->id,
                'subscription_plan_id' => $plan->id,
                'status' => 'cancelled',
                'billing_cycle' => rand(0, 1) ? 'monthly' : 'yearly',
                'amount' => rand(0, 1) ? $plan->monthly_price : $plan->yearly_price,
                'currency' => 'USD',
                'current_period_start' => now()->subDays(rand(30, 90)),
                'current_period_end' => now()->addDays(rand(1, 30)), // Grace period
                'stripe_subscription_id' => 'sub_' . \Str::random(24),
                'cancelled_at' => now()->subDays(rand(1, 15)),
                'ends_at' => now()->addDays(rand(1, 30)),
            ]);
        }

        $this->command->info('Subscriptions seeded successfully!');
    }
}
