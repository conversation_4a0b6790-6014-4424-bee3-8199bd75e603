@extends('layouts.app')

@section('title', $lesson->title . ' - ' . $lesson->course->title)

@section('content')
<div class="lesson-layout">
    <!-- Lesson Sidebar -->
    <div class="lesson-sidebar">
        <div class="course-info">
            <div class="course-thumbnail">
                <img src="{{ $lesson->course->thumbnail ? asset('storage/' . $lesson->course->thumbnail) : asset('images/default-course-thumbnail.jpg') }}" 
                     alt="{{ $lesson->course->title }}">
            </div>
            <div class="course-details">
                <h3 class="course-title">{{ $lesson->course->title }}</h3>
                <p class="course-mentor">by {{ $lesson->course->mentor->name }}</p>
                <div class="course-progress">
                    <div class="progress">
                        <div class="progress-bar" role="progressbar" 
                             style="width: {{ $courseProgress }}%"
                             aria-valuenow="{{ $courseProgress }}" 
                             aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                    <small class="progress-text">{{ number_format($courseProgress, 1) }}% Complete</small>
                </div>
            </div>
        </div>
        
        <!-- Lesson Navigation -->
        <div class="lesson-navigation">
            <h4>Course Content</h4>
            <div class="lessons-list">
                @foreach($lesson->course->lessons as $courseLesson)
                <div class="lesson-item {{ $courseLesson->id === $lesson->id ? 'active' : '' }} {{ $courseLesson->is_completed ? 'completed' : '' }}">
                    <div class="lesson-number">{{ $loop->iteration }}</div>
                    <div class="lesson-info">
                        @if($courseLesson->id === $lesson->id)
                            <span class="lesson-title current">{{ $courseLesson->title }}</span>
                        @else
                            <a href="{{ route('lessons.show', $courseLesson) }}" class="lesson-title">
                                {{ $courseLesson->title }}
                            </a>
                        @endif
                        <div class="lesson-meta">
                            <span class="lesson-duration">
                                <i class="fas fa-clock"></i> {{ $courseLesson->duration }} min
                            </span>
                            @if($courseLesson->is_completed)
                            <span class="lesson-status completed">
                                <i class="fas fa-check"></i> Completed
                            </span>
                            @endif
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </div>

    <!-- Main Lesson Content -->
    <div class="lesson-main">
        <!-- Lesson Header -->
        <div class="lesson-header">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('courses.show', $lesson->course) }}">{{ $lesson->course->title }}</a></li>
                    <li class="breadcrumb-item active">{{ $lesson->title }}</li>
                </ol>
            </nav>
            
            <div class="lesson-title-section">
                <h1 class="lesson-title">{{ $lesson->title }}</h1>
                <div class="lesson-meta-info">
                    <span class="lesson-duration">
                        <i class="fas fa-clock"></i> {{ $lesson->duration }} minutes
                    </span>
                    <span class="lesson-difficulty">
                        <i class="fas fa-signal"></i> {{ ucfirst($lesson->difficulty_level) }}
                    </span>
                    @if($lesson->is_free)
                    <span class="lesson-badge free">
                        <i class="fas fa-unlock"></i> Free
                    </span>
                    @endif
                </div>
                <p class="lesson-description">{{ $lesson->description }}</p>
            </div>
        </div>

        <!-- Video Player -->
        @if($lesson->video_url)
        <div class="video-section">
            <div class="video-player-container">
                <video id="lessonVideo" class="video-player" controls preload="metadata" 
                       poster="{{ $lesson->thumbnail ? asset('storage/' . $lesson->thumbnail) : '' }}">
                    <source src="{{ asset('storage/' . $lesson->video_url) }}" type="video/mp4">
                    <track kind="captions" src="{{ $lesson->captions_url }}" srclang="en" label="English" default>
                    Your browser does not support the video tag.
                </video>
                
                <div class="video-controls-overlay">
                    <div class="playback-speed">
                        <select id="playbackSpeed" class="form-control form-control-sm">
                            <option value="0.5">0.5x</option>
                            <option value="0.75">0.75x</option>
                            <option value="1" selected>1x</option>
                            <option value="1.25">1.25x</option>
                            <option value="1.5">1.5x</option>
                            <option value="2">2x</option>
                        </select>
                    </div>
                    
                    <div class="video-quality">
                        <select id="videoQuality" class="form-control form-control-sm">
                            <option value="auto" selected>Auto</option>
                            <option value="720p">720p</option>
                            <option value="480p">480p</option>
                            <option value="360p">360p</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <!-- Video Progress -->
            <div class="video-progress-info">
                <div class="progress video-progress">
                    <div class="progress-bar" id="videoProgressBar" role="progressbar" 
                         style="width: {{ $lessonProgress->watch_percentage ?? 0 }}%"
                         aria-valuenow="{{ $lessonProgress->watch_percentage ?? 0 }}" 
                         aria-valuemin="0" aria-valuemax="100"></div>
                </div>
                <small class="text-muted">
                    Watched: <span id="watchedTime">{{ gmdate('H:i:s', $lessonProgress->watch_time ?? 0) }}</span> / 
                    <span id="totalTime">{{ gmdate('H:i:s', $lesson->duration * 60) }}</span>
                </small>
            </div>
        </div>
        @endif

        <!-- Lesson Content -->
        <div class="lesson-content-section">
            <div class="content-tabs">
                <ul class="nav nav-tabs" id="contentTabs" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link active" id="content-tab" data-toggle="tab" href="#content" role="tab">
                            <i class="fas fa-file-text"></i> Content
                        </a>
                    </li>
                    @if($lesson->resources->count() > 0)
                    <li class="nav-item">
                        <a class="nav-link" id="resources-tab" data-toggle="tab" href="#resources" role="tab">
                            <i class="fas fa-download"></i> Resources ({{ $lesson->resources->count() }})
                        </a>
                    </li>
                    @endif
                    <li class="nav-item">
                        <a class="nav-link" id="notes-tab" data-toggle="tab" href="#notes" role="tab">
                            <i class="fas fa-sticky-note"></i> My Notes
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="discussion-tab" data-toggle="tab" href="#discussion" role="tab">
                            <i class="fas fa-comments"></i> Discussion
                        </a>
                    </li>
                </ul>
                
                <div class="tab-content" id="contentTabsContent">
                    <!-- Content Tab -->
                    <div class="tab-pane fade show active" id="content" role="tabpanel">
                        <div class="lesson-content">
                            {!! $lesson->content !!}
                        </div>
                    </div>
                    
                    <!-- Resources Tab -->
                    @if($lesson->resources->count() > 0)
                    <div class="tab-pane fade" id="resources" role="tabpanel">
                        <div class="lesson-resources">
                            <h4>Downloadable Resources</h4>
                            <div class="resources-list">
                                @foreach($lesson->resources as $resource)
                                <div class="resource-item">
                                    <div class="resource-icon">
                                        <i class="fas fa-{{ $resource->getIconClass() }}"></i>
                                    </div>
                                    <div class="resource-info">
                                        <h5>{{ $resource->title }}</h5>
                                        <p>{{ $resource->description }}</p>
                                        <small class="text-muted">
                                            {{ $resource->file_type }} • {{ $resource->getFormattedSize() }}
                                        </small>
                                    </div>
                                    <div class="resource-actions">
                                        <a href="{{ route('lessons.resource', [$lesson, $resource]) }}" 
                                           class="btn btn-primary btn-sm" target="_blank">
                                            <i class="fas fa-download"></i> Download
                                        </a>
                                    </div>
                                </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                    @endif
                    
                    <!-- Notes Tab -->
                    <div class="tab-pane fade" id="notes" role="tabpanel">
                        <div class="lesson-notes">
                            <div class="notes-header">
                                <h4>My Notes</h4>
                                <button class="btn btn-primary btn-sm" onclick="addNote()">
                                    <i class="fas fa-plus"></i> Add Note
                                </button>
                            </div>
                            
                            <div class="notes-list" id="notesList">
                                @forelse($userNotes as $note)
                                <div class="note-item" data-note-id="{{ $note->id }}">
                                    <div class="note-content">
                                        <div class="note-text">{{ $note->content }}</div>
                                        <div class="note-meta">
                                            <small class="text-muted">
                                                @if($note->timestamp)
                                                At {{ gmdate('H:i:s', $note->timestamp) }} • 
                                                @endif
                                                {{ $note->created_at->diffForHumans() }}
                                            </small>
                                        </div>
                                    </div>
                                    <div class="note-actions">
                                        <button class="btn btn-sm btn-outline-secondary" onclick="editNote({{ $note->id }})">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger" onclick="deleteNote({{ $note->id }})">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                                @empty
                                <div class="empty-notes">
                                    <i class="fas fa-sticky-note fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">No notes yet. Add your first note to remember key points!</p>
                                </div>
                                @endforelse
                            </div>
                        </div>
                    </div>
                    
                    <!-- Discussion Tab -->
                    <div class="tab-pane fade" id="discussion" role="tabpanel">
                        <div class="lesson-discussion">
                            <h4>Discussion</h4>
                            <p class="text-muted">Ask questions and discuss this lesson with other students.</p>
                            
                            <!-- Add Comment Form -->
                            <form class="comment-form" onsubmit="addComment(event)">
                                <div class="form-group">
                                    <textarea class="form-control" rows="3" placeholder="Ask a question or share your thoughts..." required></textarea>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-comment"></i> Post Comment
                                </button>
                            </form>
                            
                            <!-- Comments List -->
                            <div class="comments-list" id="commentsList">
                                <!-- Comments will be loaded here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Lesson Actions -->
        <div class="lesson-actions">
            <div class="action-buttons">
                @if($previousLesson)
                <a href="{{ route('lessons.show', $previousLesson) }}" class="btn btn-outline-secondary">
                    <i class="fas fa-chevron-left"></i> Previous Lesson
                </a>
                @endif
                
                <div class="center-actions">
                    <button class="btn btn-outline-primary" onclick="toggleBookmark()" id="bookmarkBtn">
                        <i class="fas fa-bookmark {{ $isBookmarked ? 'text-warning' : '' }}"></i>
                        {{ $isBookmarked ? 'Bookmarked' : 'Bookmark' }}
                    </button>
                    
                    @if(!$lesson->is_completed)
                    <button class="btn btn-success" onclick="markComplete()" id="completeBtn">
                        <i class="fas fa-check"></i> Mark Complete
                    </button>
                    @else
                    <button class="btn btn-success" disabled>
                        <i class="fas fa-check"></i> Completed
                    </button>
                    @endif
                </div>
                
                @if($nextLesson)
                <a href="{{ route('lessons.show', $nextLesson) }}" class="btn btn-primary">
                    Next Lesson <i class="fas fa-chevron-right"></i>
                </a>
                @else
                <a href="{{ route('courses.show', $lesson->course) }}" class="btn btn-primary">
                    <i class="fas fa-trophy"></i> Course Complete
                </a>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Add Note Modal -->
<div class="modal fade" id="noteModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Note</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="noteForm">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="noteContent">Note Content</label>
                        <textarea id="noteContent" class="form-control" rows="4" placeholder="Enter your note..." required></textarea>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="noteTimestamp"> 
                            Link to current video time
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Note</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
