<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Certificate;
use App\Models\Course;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Barryvdh\DomPDF\Facade\Pdf;

class CertificateController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth')->except(['verify', 'publicShow']);
    }

    public function index()
    {
        $certificates = Certificate::where('user_id', Auth::id())
            ->with(['course', 'course.mentor', 'course.category'])
            ->orderBy('issued_at', 'desc')
            ->paginate(12);

        $stats = [
            'total_certificates' => $certificates->total(),
            'completed_this_month' => Certificate::where('user_id', Auth::id())
                ->whereMonth('issued_at', now()->month)
                ->whereYear('issued_at', now()->year)
                ->count(),
            'total_hours' => $certificates->sum(function($cert) {
                return $cert->course->total_duration ?? 0;
            }),
            'categories_completed' => $certificates->pluck('course.category.name')->unique()->count(),
        ];

        return view('dashboard.certificates', compact('certificates', 'stats'));
    }

    public function show(Certificate $certificate)
    {
        // Check if user owns this certificate or if it's public
        if ($certificate->user_id !== Auth::id() && !$certificate->is_public) {
            abort(403);
        }

        $certificate->load(['course', 'course.mentor', 'user']);

        // Get related certificates from the same user
        $relatedCertificates = Certificate::where('user_id', $certificate->user_id)
            ->where('id', '!=', $certificate->id)
            ->with(['course', 'course.category'])
            ->limit(6)
            ->get();

        return view('certificates.show', compact('certificate', 'relatedCertificates'));
    }

    public function download(Certificate $certificate)
    {
        // Check if user owns this certificate
        if ($certificate->user_id !== Auth::id()) {
            abort(403);
        }

        $certificate->load(['course', 'course.mentor', 'user']);

        // Generate PDF
        $pdf = Pdf::loadView('certificates.pdf', compact('certificate'))
            ->setPaper('a4', 'landscape')
            ->setOptions([
                'dpi' => 150,
                'defaultFont' => 'sans-serif',
                'isHtml5ParserEnabled' => true,
                'isRemoteEnabled' => true,
            ]);

        $filename = 'certificate-' . $certificate->certificate_id . '.pdf';

        return $pdf->download($filename);
    }

    public function verify($certificateId)
    {
        $certificate = Certificate::where('certificate_id', $certificateId)
            ->with(['course', 'course.mentor', 'user'])
            ->first();

        if (!$certificate) {
            return view('certificates.verify-failed', [
                'message' => 'Certificate not found or invalid certificate ID.'
            ]);
        }

        return view('certificates.verify', compact('certificate'));
    }

    public function publicShow($certificateId)
    {
        $certificate = Certificate::where('certificate_id', $certificateId)
            ->where('is_public', true)
            ->with(['course', 'course.mentor', 'user'])
            ->firstOrFail();

        return view('certificates.public', compact('certificate'));
    }

    public function generateForCourse(Course $course)
    {
        $user = Auth::user();

        // Check if user has completed the course
        $enrollment = $user->enrollments()
            ->where('course_id', $course->id)
            ->where('completed_at', '!=', null)
            ->first();

        if (!$enrollment) {
            return response()->json([
                'success' => false,
                'message' => 'You must complete the course to generate a certificate.'
            ], 400);
        }

        // Check if certificate already exists
        $existingCertificate = Certificate::where('user_id', $user->id)
            ->where('course_id', $course->id)
            ->first();

        if ($existingCertificate) {
            return response()->json([
                'success' => false,
                'message' => 'Certificate already exists for this course.',
                'certificate_url' => route('certificates.show', $existingCertificate)
            ], 400);
        }

        // Generate certificate
        $certificate = $this->createCertificate($user, $course, $enrollment);

        return response()->json([
            'success' => true,
            'message' => 'Certificate generated successfully!',
            'certificate_url' => route('certificates.show', $certificate)
        ]);
    }

    private function createCertificate($user, $course, $enrollment)
    {
        // Generate unique certificate ID
        $certificateId = 'TRW-' . strtoupper(uniqid());

        // Calculate grade based on quiz scores, etc.
        $grade = $this->calculateGrade($enrollment);

        // Calculate completion time
        $completionTime = $enrollment->created_at->diffInDays($enrollment->completed_at) . ' days';

        $certificate = Certificate::create([
            'certificate_id' => $certificateId,
            'user_id' => $user->id,
            'course_id' => $course->id,
            'issued_at' => now(),
            'grade' => $grade,
            'completion_time' => $completionTime,
            'is_public' => true,
            'qr_code_url' => $this->generateQRCode($certificateId),
        ]);

        // Send notification email
        try {
            // Mail::to($user->email)->send(new \App\Mail\CertificateIssued($certificate));
        } catch (\Exception $e) {
            \Log::error('Failed to send certificate email: ' . $e->getMessage());
        }

        return $certificate;
    }

    private function calculateGrade($enrollment)
    {
        // This would calculate based on quiz scores, assignments, etc.
        // For now, return a default grade
        return 'Pass';
    }

    private function generateQRCode($certificateId)
    {
        // Generate QR code for certificate verification
        $verificationUrl = route('certificates.verify', $certificateId);
        
        // You would use a QR code library here
        // For now, return a placeholder URL
        return "https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=" . urlencode($verificationUrl);
    }

    public function togglePublic(Certificate $certificate)
    {
        // Check if user owns this certificate
        if ($certificate->user_id !== Auth::id()) {
            abort(403);
        }

        $certificate->update([
            'is_public' => !$certificate->is_public
        ]);

        return response()->json([
            'success' => true,
            'is_public' => $certificate->is_public,
            'message' => $certificate->is_public ? 
                'Certificate is now public' : 
                'Certificate is now private'
        ]);
    }

    public function share(Request $request, Certificate $certificate)
    {
        // Check if user owns this certificate
        if ($certificate->user_id !== Auth::id()) {
            abort(403);
        }

        $platform = $request->get('platform');
        $certificateUrl = route('certificates.show', $certificate);
        
        $shareUrls = [
            'linkedin' => 'https://www.linkedin.com/sharing/share-offsite/?url=' . urlencode($certificateUrl),
            'twitter' => 'https://twitter.com/intent/tweet?url=' . urlencode($certificateUrl) . '&text=' . urlencode('I just earned a certificate from The Real World!'),
            'facebook' => 'https://www.facebook.com/sharer/sharer.php?u=' . urlencode($certificateUrl),
        ];

        if (!isset($shareUrls[$platform])) {
            return response()->json(['success' => false, 'message' => 'Invalid platform'], 400);
        }

        return response()->json([
            'success' => true,
            'share_url' => $shareUrls[$platform]
        ]);
    }
}
