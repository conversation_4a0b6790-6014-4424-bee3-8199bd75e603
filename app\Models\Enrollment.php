<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Enrollment extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'course_id',
        'enrolled_at',
        'started_at',
        'completed_at',
        'last_accessed_at',
        'progress_percentage',
        'completion_certificate_id',
        'payment_id',
        'enrollment_source',
        'is_active',
    ];

    protected $casts = [
        'enrolled_at' => 'datetime',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'last_accessed_at' => 'datetime',
        'progress_percentage' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    protected $dates = [
        'enrolled_at',
        'started_at',
        'completed_at',
        'last_accessed_at',
        'deleted_at',
    ];

    // Enrollment source constants
    const SOURCE_DIRECT = 'direct';
    const SOURCE_SUBSCRIPTION = 'subscription';
    const SOURCE_GIFT = 'gift';
    const SOURCE_PROMOTION = 'promotion';
    const SOURCE_ADMIN = 'admin';

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function course()
    {
        return $this->belongsTo(Course::class);
    }

    public function certificate()
    {
        return $this->belongsTo(Certificate::class, 'completion_certificate_id');
    }

    public function payment()
    {
        return $this->belongsTo(Payment::class);
    }

    public function progress()
    {
        return $this->hasMany(UserProgress::class, 'user_id', 'user_id')
                    ->where('course_id', $this->course_id);
    }

    public function getIsCompletedAttribute()
    {
        return !is_null($this->completed_at);
    }

    public function getIsStartedAttribute()
    {
        return !is_null($this->started_at);
    }

    public function getStatusAttribute()
    {
        if ($this->completed_at) {
            return 'completed';
        } elseif ($this->started_at) {
            return 'in_progress';
        } else {
            return 'enrolled';
        }
    }

    public function getStatusLabelAttribute()
    {
        $labels = [
            'enrolled' => 'Enrolled',
            'in_progress' => 'In Progress',
            'completed' => 'Completed',
        ];

        return $labels[$this->status] ?? 'Unknown';
    }

    public function getStatusColorAttribute()
    {
        $colors = [
            'enrolled' => 'secondary',
            'in_progress' => 'primary',
            'completed' => 'success',
        ];

        return $colors[$this->status] ?? 'secondary';
    }

    public function getDurationAttribute()
    {
        if (!$this->started_at) {
            return null;
        }

        $endDate = $this->completed_at ?? now();
        return $this->started_at->diffInDays($endDate);
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeCompleted($query)
    {
        return $query->whereNotNull('completed_at');
    }

    public function scopeInProgress($query)
    {
        return $query->whereNotNull('started_at')->whereNull('completed_at');
    }

    public function scopeNotStarted($query)
    {
        return $query->whereNull('started_at');
    }

    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeForCourse($query, $courseId)
    {
        return $query->where('course_id', $courseId);
    }

    public function scopeRecentlyActive($query, $days = 30)
    {
        return $query->where('last_accessed_at', '>', now()->subDays($days));
    }

    public function markAsStarted()
    {
        if (!$this->started_at) {
            $this->update([
                'started_at' => now(),
                'last_accessed_at' => now(),
            ]);
        }
    }

    public function markAsCompleted()
    {
        if (!$this->completed_at) {
            $this->update([
                'completed_at' => now(),
                'progress_percentage' => 100,
                'last_accessed_at' => now(),
            ]);

            // Generate certificate if not exists
            $this->generateCertificate();
        }
    }

    public function updateProgress($percentage)
    {
        $this->update([
            'progress_percentage' => min(100, max(0, $percentage)),
            'last_accessed_at' => now(),
        ]);

        // Mark as started if not already
        if (!$this->started_at) {
            $this->markAsStarted();
        }

        // Mark as completed if 100%
        if ($percentage >= 100 && !$this->completed_at) {
            $this->markAsCompleted();
        }
    }

    public function updateLastAccessed()
    {
        $this->update(['last_accessed_at' => now()]);
    }

    private function generateCertificate()
    {
        // Check if certificate already exists
        if ($this->certificate) {
            return $this->certificate;
        }

        // Generate certificate
        $certificate = Certificate::create([
            'certificate_id' => 'TRW-' . strtoupper(uniqid()),
            'user_id' => $this->user_id,
            'course_id' => $this->course_id,
            'issued_at' => now(),
            'is_valid' => true,
            'is_public' => true,
        ]);

        // Update enrollment with certificate
        $this->update(['completion_certificate_id' => $certificate->id]);

        return $certificate;
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($enrollment) {
            if (!$enrollment->enrolled_at) {
                $enrollment->enrolled_at = now();
            }
            
            if (!isset($enrollment->is_active)) {
                $enrollment->is_active = true;
            }
        });
    }
}
