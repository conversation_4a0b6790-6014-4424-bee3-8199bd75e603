<?php $__env->startSection('title', '- Community Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="p-6">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Community Management</h1>
                <p class="mt-1 text-sm text-gray-600">Moderate posts, comments, and community interactions</p>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Posts</dt>
                            <dd class="text-lg font-medium text-gray-900"><?php echo e(number_format($stats['total_posts'])); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Comments</dt>
                            <dd class="text-lg font-medium text-gray-900"><?php echo e(number_format($stats['total_comments'])); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Pending Review</dt>
                            <dd class="text-lg font-medium text-gray-900"><?php echo e(number_format($stats['pending_review'])); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Reported</dt>
                            <dd class="text-lg font-medium text-gray-900"><?php echo e(number_format($stats['reported_content'])); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Content Tabs -->
    <div class="bg-white shadow rounded-lg">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8 px-6" aria-label="Tabs">
                <button onclick="showContentTab('posts')" 
                        class="content-tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm active">
                    Posts
                </button>
                <button onclick="showContentTab('comments')" 
                        class="content-tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                    Comments
                </button>
                <button onclick="showContentTab('reported')" 
                        class="content-tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                    Reported Content
                </button>
            </nav>
        </div>

        <!-- Posts Tab -->
        <div id="posts-tab" class="content-tab-content p-6">
            <!-- Filters -->
            <div class="mb-6">
                <form method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <label for="search_posts" class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                        <input type="text" name="search" id="search_posts" value="<?php echo e(request('search')); ?>" 
                               placeholder="Search posts..." 
                               class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    </div>

                    <div>
                        <label for="status_posts" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                        <select name="status" id="status_posts" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            <option value="">All Statuses</option>
                            <option value="approved" <?php echo e(request('status') === 'approved' ? 'selected' : ''); ?>>Approved</option>
                            <option value="pending" <?php echo e(request('status') === 'pending' ? 'selected' : ''); ?>>Pending</option>
                            <option value="rejected" <?php echo e(request('status') === 'rejected' ? 'selected' : ''); ?>>Rejected</option>
                        </select>
                    </div>

                    <div>
                        <label for="author" class="block text-sm font-medium text-gray-700 mb-1">Author</label>
                        <input type="text" name="author" id="author" value="<?php echo e(request('author')); ?>" 
                               placeholder="Author name..." 
                               class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    </div>

                    <div class="flex items-end">
                        <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition duration-150">
                            Filter
                        </button>
                    </div>
                </form>
            </div>

            <!-- Posts List -->
            <?php if($posts->count() > 0): ?>
                <div class="space-y-4">
                    <?php $__currentLoopData = $posts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="border border-gray-200 rounded-lg p-4">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center mb-2">
                                        <div class="flex-shrink-0 h-8 w-8">
                                            <div class="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center">
                                                <span class="text-xs font-medium text-gray-700">
                                                    <?php echo e(substr($post->user->name, 0, 2)); ?>

                                                </span>
                                            </div>
                                        </div>
                                        <div class="ml-3">
                                            <p class="text-sm font-medium text-gray-900"><?php echo e($post->user->name); ?></p>
                                            <p class="text-xs text-gray-500"><?php echo e($post->created_at->diffForHumans()); ?></p>
                                        </div>
                                    </div>
                                    
                                    <h4 class="text-lg font-medium text-gray-900 mb-2"><?php echo e($post->title); ?></h4>
                                    <p class="text-gray-600 mb-3"><?php echo e(Str::limit($post->content, 200)); ?></p>
                                    
                                    <div class="flex items-center space-x-4 text-sm text-gray-500">
                                        <span><?php echo e($post->likes_count); ?> likes</span>
                                        <span><?php echo e($post->comments_count); ?> comments</span>
                                        <?php if($post->image): ?>
                                            <span class="flex items-center">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                                </svg>
                                                Image
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <div class="flex items-center space-x-2 ml-4">
                                    <?php if($post->status === 'approved'): ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            Approved
                                        </span>
                                    <?php elseif($post->status === 'pending'): ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                            Pending
                                        </span>
                                    <?php else: ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            Rejected
                                        </span>
                                    <?php endif; ?>

                                    <div class="flex space-x-1">
                                        <?php if($post->status !== 'approved'): ?>
                                            <form method="POST" action="<?php echo e(route('admin.community.approve-post', $post)); ?>" class="inline">
                                                <?php echo csrf_field(); ?>
                                                <button type="submit" class="text-green-600 hover:text-green-900 text-sm font-medium">
                                                    Approve
                                                </button>
                                            </form>
                                        <?php endif; ?>
                                        
                                        <?php if($post->status !== 'rejected'): ?>
                                            <form method="POST" action="<?php echo e(route('admin.community.reject-post', $post)); ?>" class="inline">
                                                <?php echo csrf_field(); ?>
                                                <button type="submit" class="text-red-600 hover:text-red-900 text-sm font-medium">
                                                    Reject
                                                </button>
                                            </form>
                                        <?php endif; ?>
                                        
                                        <form method="POST" action="<?php echo e(route('admin.community.delete-post', $post)); ?>" class="inline">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('DELETE'); ?>
                                            <button type="submit" 
                                                    onclick="return confirm('Are you sure you want to delete this post?')"
                                                    class="text-red-600 hover:text-red-900 text-sm font-medium">
                                                Delete
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                <!-- Pagination -->
                <div class="mt-6">
                    <?php echo e($posts->links()); ?>

                </div>
            <?php else: ?>
                <div class="text-center py-12">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No posts found</h3>
                    <p class="mt-1 text-sm text-gray-500">No posts match your current filters.</p>
                </div>
            <?php endif; ?>
        </div>

        <!-- Comments Tab -->
        <div id="comments-tab" class="content-tab-content p-6 hidden">
            <?php if($comments->count() > 0): ?>
                <div class="space-y-4">
                    <?php $__currentLoopData = $comments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $comment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="border border-gray-200 rounded-lg p-4">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center mb-2">
                                        <div class="flex-shrink-0 h-8 w-8">
                                            <div class="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center">
                                                <span class="text-xs font-medium text-gray-700">
                                                    <?php echo e(substr($comment->user->name, 0, 2)); ?>

                                                </span>
                                            </div>
                                        </div>
                                        <div class="ml-3">
                                            <p class="text-sm font-medium text-gray-900"><?php echo e($comment->user->name); ?></p>
                                            <p class="text-xs text-gray-500"><?php echo e($comment->created_at->diffForHumans()); ?></p>
                                        </div>
                                    </div>
                                    
                                    <p class="text-gray-600 mb-2"><?php echo e($comment->content); ?></p>
                                    <p class="text-xs text-gray-500">
                                        On post: <a href="#" class="text-blue-600 hover:text-blue-900"><?php echo e(Str::limit($comment->post->title, 50)); ?></a>
                                    </p>
                                </div>

                                <div class="flex items-center space-x-2 ml-4">
                                    <?php if($comment->status === 'approved'): ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            Approved
                                        </span>
                                    <?php elseif($comment->status === 'pending'): ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                            Pending
                                        </span>
                                    <?php else: ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            Rejected
                                        </span>
                                    <?php endif; ?>

                                    <div class="flex space-x-1">
                                        <?php if($comment->status !== 'approved'): ?>
                                            <form method="POST" action="<?php echo e(route('admin.community.approve-comment', $comment)); ?>" class="inline">
                                                <?php echo csrf_field(); ?>
                                                <button type="submit" class="text-green-600 hover:text-green-900 text-sm font-medium">
                                                    Approve
                                                </button>
                                            </form>
                                        <?php endif; ?>
                                        
                                        <?php if($comment->status !== 'rejected'): ?>
                                            <form method="POST" action="<?php echo e(route('admin.community.reject-comment', $comment)); ?>" class="inline">
                                                <?php echo csrf_field(); ?>
                                                <button type="submit" class="text-red-600 hover:text-red-900 text-sm font-medium">
                                                    Reject
                                                </button>
                                            </form>
                                        <?php endif; ?>
                                        
                                        <form method="POST" action="<?php echo e(route('admin.community.delete-comment', $comment)); ?>" class="inline">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('DELETE'); ?>
                                            <button type="submit" 
                                                    onclick="return confirm('Are you sure you want to delete this comment?')"
                                                    class="text-red-600 hover:text-red-900 text-sm font-medium">
                                                Delete
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                <!-- Pagination -->
                <div class="mt-6">
                    <?php echo e($comments->links()); ?>

                </div>
            <?php else: ?>
                <div class="text-center py-12">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No comments found</h3>
                    <p class="mt-1 text-sm text-gray-500">No comments match your current filters.</p>
                </div>
            <?php endif; ?>
        </div>

        <!-- Reported Content Tab -->
        <div id="reported-tab" class="content-tab-content p-6 hidden">
            <?php if($reportedContent->count() > 0): ?>
                <div class="space-y-4">
                    <?php $__currentLoopData = $reportedContent; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $report): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="border border-red-200 rounded-lg p-4 bg-red-50">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center mb-2">
                                        <svg class="h-5 w-5 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                        </svg>
                                        <span class="text-sm font-medium text-red-900">Reported <?php echo e(ucfirst($report['type'])); ?></span>
                                        <span class="ml-2 text-xs text-red-700"><?php echo e($report['reported_at']); ?></span>
                                    </div>
                                    
                                    <p class="text-sm text-red-800 mb-2"><strong>Reason:</strong> <?php echo e($report['reason']); ?></p>
                                    <p class="text-sm text-gray-700 mb-2"><strong>Content:</strong> <?php echo e(Str::limit($report['content'], 150)); ?></p>
                                    <p class="text-xs text-gray-600">
                                        <strong>Author:</strong> <?php echo e($report['author']); ?> | 
                                        <strong>Reported by:</strong> <?php echo e($report['reporter']); ?>

                                    </p>
                                </div>

                                <div class="flex space-x-2 ml-4">
                                    <button onclick="dismissReport(<?php echo e($report['id']); ?>)" 
                                            class="text-green-600 hover:text-green-900 text-sm font-medium">
                                        Dismiss
                                    </button>
                                    <button onclick="takeAction(<?php echo e($report['id']); ?>)" 
                                            class="text-red-600 hover:text-red-900 text-sm font-medium">
                                        Take Action
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            <?php else: ?>
                <div class="text-center py-12">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No reported content</h3>
                    <p class="mt-1 text-sm text-gray-500">All content is clean and no reports pending.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Bulk Actions -->
    <div class="mt-8 bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Bulk Actions</h3>
        </div>
        <div class="p-6">
            <form method="POST" action="<?php echo e(route('admin.community.bulk-action')); ?>">
                <?php echo csrf_field(); ?>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label for="bulk_type" class="block text-sm font-medium text-gray-700 mb-2">Content Type</label>
                        <select name="type" id="bulk_type" 
                                class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            <option value="posts">Posts</option>
                            <option value="comments">Comments</option>
                        </select>
                    </div>
                    
                    <div>
                        <label for="bulk_action" class="block text-sm font-medium text-gray-700 mb-2">Action</label>
                        <select name="action" id="bulk_action" 
                                class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            <option value="approve">Approve All Pending</option>
                            <option value="reject">Reject All Pending</option>
                            <option value="delete_rejected">Delete All Rejected</option>
                        </select>
                    </div>
                    
                    <div class="flex items-end">
                        <button type="submit" 
                                onclick="return confirm('Are you sure you want to perform this bulk action?')"
                                class="w-full bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md transition duration-150">
                            Execute Action
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function showContentTab(tabName) {
    // Hide all tab contents
    document.querySelectorAll('.content-tab-content').forEach(tab => {
        tab.classList.add('hidden');
    });
    
    // Remove active class from all buttons
    document.querySelectorAll('.content-tab-button').forEach(button => {
        button.classList.remove('active', 'border-blue-500', 'text-blue-600');
        button.classList.add('border-transparent', 'text-gray-500');
    });
    
    // Show selected tab
    document.getElementById(tabName + '-tab').classList.remove('hidden');
    
    // Add active class to clicked button
    event.target.classList.add('active', 'border-blue-500', 'text-blue-600');
    event.target.classList.remove('border-transparent', 'text-gray-500');
}

function dismissReport(reportId) {
    if (confirm('Are you sure you want to dismiss this report?')) {
        fetch(`/admin/community/reports/${reportId}/dismiss`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            }
        });
    }
}

function takeAction(reportId) {
    if (confirm('Are you sure you want to take action on this reported content?')) {
        fetch(`/admin/community/reports/${reportId}/action`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            }
        });
    }
}

// Initialize first tab as active
document.addEventListener('DOMContentLoaded', function() {
    const firstButton = document.querySelector('.content-tab-button');
    firstButton.classList.add('active', 'border-blue-500', 'text-blue-600');
    firstButton.classList.remove('border-transparent', 'text-gray-500');
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\LMS\resources\views/admin/community/index.blade.php ENDPATH**/ ?>