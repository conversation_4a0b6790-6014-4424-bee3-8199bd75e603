@extends('layouts.app')

@section('title', $category->name . ' Courses')

@section('content')
<div class="category-hero">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ route('home') }}">Home</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('courses.index') }}">Courses</a></li>
                        <li class="breadcrumb-item active">{{ $category->name }}</li>
                    </ol>
                </nav>
                
                <div class="category-header">
                    <div class="category-icon">
                        <i class="fas fa-{{ $category->icon }}"></i>
                    </div>
                    <div class="category-info">
                        <h1 class="category-title">{{ $category->name }}</h1>
                        <p class="category-description">{{ $category->description }}</p>
                        <div class="category-stats">
                            <span class="stat-item">
                                <i class="fas fa-book"></i>
                                {{ $courses->total() }} {{ Str::plural('course', $courses->total()) }}
                            </span>
                            <span class="stat-item">
                                <i class="fas fa-users"></i>
                                {{ $category->total_students }} students enrolled
                            </span>
                            <span class="stat-item">
                                <i class="fas fa-star"></i>
                                {{ number_format($category->average_rating, 1) }} average rating
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 text-right">
                <div class="category-actions">
                    <button class="btn btn-outline-light" onclick="toggleCategoryFollow()">
                        <i class="fas fa-bell"></i> Follow Category
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="category-content">
    <div class="container">
        <!-- Featured Course -->
        @if($featuredCourse)
        <div class="featured-course-section mb-5">
            <h2 class="section-title">Featured Course</h2>
            <div class="featured-course-card">
                <div class="row align-items-center">
                    <div class="col-lg-6">
                        <div class="featured-course-image">
                            <img src="{{ $featuredCourse->thumbnail ? asset('storage/' . $featuredCourse->thumbnail) : asset('images/default-course-thumbnail.jpg') }}" 
                                 alt="{{ $featuredCourse->title }}">
                            <div class="featured-badge">
                                <i class="fas fa-star"></i> Featured
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="featured-course-content">
                            <div class="course-meta">
                                <span class="course-level">{{ ucfirst($featuredCourse->difficulty_level) }}</span>
                                <span class="course-duration">{{ $featuredCourse->total_duration }} hours</span>
                            </div>
                            <h3 class="featured-course-title">{{ $featuredCourse->title }}</h3>
                            <p class="featured-course-description">{{ $featuredCourse->description }}</p>
                            
                            <div class="featured-course-mentor">
                                <img src="{{ $featuredCourse->mentor->avatar ? asset('storage/' . $featuredCourse->mentor->avatar) : asset('images/default-avatar.png') }}" 
                                     alt="{{ $featuredCourse->mentor->name }}" class="mentor-avatar">
                                <div class="mentor-info">
                                    <span class="mentor-name">{{ $featuredCourse->mentor->name }}</span>
                                    <span class="mentor-title">{{ $featuredCourse->mentor->title }}</span>
                                </div>
                            </div>
                            
                            <div class="featured-course-stats">
                                <div class="stat">
                                    <i class="fas fa-star text-warning"></i>
                                    <span>{{ number_format($featuredCourse->average_rating, 1) }}</span>
                                    <small>({{ $featuredCourse->ratings_count }} reviews)</small>
                                </div>
                                <div class="stat">
                                    <i class="fas fa-users"></i>
                                    <span>{{ $featuredCourse->enrollments_count }} students</span>
                                </div>
                            </div>
                            
                            <div class="featured-course-actions">
                                @auth
                                    @if(auth()->user()->isEnrolledIn($featuredCourse))
                                        <a href="{{ route('courses.show', $featuredCourse) }}" class="btn btn-success btn-lg">
                                            <i class="fas fa-play"></i> Continue Learning
                                        </a>
                                    @else
                                        <a href="{{ route('courses.show', $featuredCourse) }}" class="btn btn-primary btn-lg">
                                            <i class="fas fa-plus"></i> Enroll Now
                                        </a>
                                    @endif
                                @else
                                    <a href="{{ route('login') }}" class="btn btn-primary btn-lg">
                                        <i class="fas fa-sign-in-alt"></i> Login to Enroll
                                    </a>
                                @endauth
                                <a href="{{ route('courses.show', $featuredCourse) }}" class="btn btn-outline-primary btn-lg">
                                    <i class="fas fa-eye"></i> Preview
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @endif

        <!-- Filters and Sort -->
        <div class="courses-controls">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="filter-buttons">
                        <button class="filter-btn {{ request('level') === null ? 'active' : '' }}" 
                                onclick="filterByLevel('')">All Levels</button>
                        <button class="filter-btn {{ request('level') === 'beginner' ? 'active' : '' }}" 
                                onclick="filterByLevel('beginner')">Beginner</button>
                        <button class="filter-btn {{ request('level') === 'intermediate' ? 'active' : '' }}" 
                                onclick="filterByLevel('intermediate')">Intermediate</button>
                        <button class="filter-btn {{ request('level') === 'advanced' ? 'active' : '' }}" 
                                onclick="filterByLevel('advanced')">Advanced</button>
                    </div>
                </div>
                <div class="col-md-6 text-right">
                    <div class="sort-controls">
                        <label for="sortSelect">Sort by:</label>
                        <select id="sortSelect" class="form-control form-control-sm d-inline-block w-auto ml-2" 
                                onchange="updateSort(this.value)">
                            <option value="popular" {{ request('sort', 'popular') === 'popular' ? 'selected' : '' }}>Most Popular</option>
                            <option value="newest" {{ request('sort') === 'newest' ? 'selected' : '' }}>Newest</option>
                            <option value="rating" {{ request('sort') === 'rating' ? 'selected' : '' }}>Highest Rated</option>
                            <option value="title" {{ request('sort') === 'title' ? 'selected' : '' }}>A-Z</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- Courses Grid -->
        @if($courses->count() > 0)
        <div class="courses-grid">
            @foreach($courses as $course)
            <div class="course-card">
                <div class="course-image">
                    <img src="{{ $course->thumbnail ? asset('storage/' . $course->thumbnail) : asset('images/default-course-thumbnail.jpg') }}" 
                         alt="{{ $course->title }}">
                    <div class="course-overlay">
                        <a href="{{ route('courses.show', $course) }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-eye"></i> View Details
                        </a>
                    </div>
                    @if($course->is_free)
                    <div class="course-badge free">Free</div>
                    @endif
                    @if($course->is_new)
                    <div class="course-badge new">New</div>
                    @endif
                </div>
                
                <div class="course-content">
                    <div class="course-meta">
                        <span class="course-level level-{{ $course->difficulty_level }}">
                            {{ ucfirst($course->difficulty_level) }}
                        </span>
                        <span class="course-duration">
                            <i class="fas fa-clock"></i> {{ $course->total_duration }}h
                        </span>
                    </div>
                    
                    <h3 class="course-title">
                        <a href="{{ route('courses.show', $course) }}">{{ $course->title }}</a>
                    </h3>
                    
                    <p class="course-description">{{ Str::limit($course->description, 120) }}</p>
                    
                    <div class="course-mentor">
                        <img src="{{ $course->mentor->avatar ? asset('storage/' . $course->mentor->avatar) : asset('images/default-avatar.png') }}" 
                             alt="{{ $course->mentor->name }}" class="mentor-avatar">
                        <span class="mentor-name">{{ $course->mentor->name }}</span>
                    </div>
                    
                    <div class="course-stats">
                        <div class="stat-item">
                            <div class="rating">
                                @for($i = 1; $i <= 5; $i++)
                                    <i class="fas fa-star {{ $i <= $course->average_rating ? 'text-warning' : 'text-muted' }}"></i>
                                @endfor
                                <span class="rating-text">{{ number_format($course->average_rating, 1) }}</span>
                                <small>({{ $course->ratings_count }})</small>
                            </div>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-users"></i>
                            <span>{{ $course->enrollments_count }} students</span>
                        </div>
                    </div>
                    
                    <div class="course-actions">
                        @auth
                            @if(auth()->user()->isEnrolledIn($course))
                                <a href="{{ route('courses.show', $course) }}" class="btn btn-success btn-sm btn-block">
                                    <i class="fas fa-play"></i> Continue
                                </a>
                            @else
                                <a href="{{ route('courses.show', $course) }}" class="btn btn-primary btn-sm btn-block">
                                    <i class="fas fa-plus"></i> Enroll Now
                                </a>
                            @endif
                        @else
                            <a href="{{ route('login') }}" class="btn btn-primary btn-sm btn-block">
                                <i class="fas fa-sign-in-alt"></i> Login to Enroll
                            </a>
                        @endauth
                        
                        <div class="course-actions-secondary">
                            <button class="btn btn-outline-secondary btn-sm" onclick="toggleWishlist({{ $course->id }})" 
                                    title="Add to Wishlist">
                                <i class="fas fa-heart"></i>
                            </button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="shareCourse({{ $course->id }})" 
                                    title="Share Course">
                                <i class="fas fa-share-alt"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            @endforeach
        </div>

        <!-- Pagination -->
        <div class="pagination-wrapper">
            {{ $courses->appends(request()->query())->links() }}
        </div>

        @else
        <!-- No Courses -->
        <div class="no-courses">
            <div class="no-courses-icon">
                <i class="fas fa-book-open"></i>
            </div>
            <h3>No courses available</h3>
            <p>There are currently no courses in this category. Check back later for new content!</p>
            <a href="{{ route('courses.index') }}" class="btn btn-primary">
                <i class="fas fa-arrow-left"></i> Browse All Courses
            </a>
        </div>
        @endif

        <!-- Related Categories -->
        @if($relatedCategories->count() > 0)
        <div class="related-categories">
            <h3>Related Categories</h3>
            <div class="categories-grid">
                @foreach($relatedCategories as $relatedCategory)
                <a href="{{ route('courses.category', $relatedCategory) }}" class="category-card">
                    <div class="category-icon">
                        <i class="fas fa-{{ $relatedCategory->icon }}"></i>
                    </div>
                    <div class="category-info">
                        <h4>{{ $relatedCategory->name }}</h4>
                        <p>{{ $relatedCategory->courses_count }} courses</p>
                    </div>
                </a>
                @endforeach
            </div>
        </div>
        @endif
    </div>
</div>
@endsection

@push('styles')
<style>
.category-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem 0;
}

.breadcrumb {
    background: transparent;
    padding: 0;
    margin-bottom: 1rem;
}

.breadcrumb-item a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
}

.breadcrumb-item.active {
    color: white;
}

.category-header {
    display: flex;
    align-items: center;
}

.category-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 2rem;
    flex-shrink: 0;
}

.category-icon i {
    font-size: 2rem;
}

.category-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.category-description {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 1rem;
    line-height: 1.6;
}

.category-stats {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.stat-item {
    display: flex;
    align-items: center;
    font-size: 0.9rem;
    opacity: 0.9;
}

.stat-item i {
    margin-right: 0.5rem;
}

.category-content {
    padding: 3rem 0;
    background: #f8f9fc;
}

.section-title {
    font-size: 1.75rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 2rem;
}

.featured-course-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin-bottom: 3rem;
}

.featured-course-image {
    position: relative;
    height: 300px;
    overflow: hidden;
}

.featured-course-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.featured-badge {
    position: absolute;
    top: 1rem;
    left: 1rem;
    background: linear-gradient(135deg, #f6c23e 0%, #f4b942 100%);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.875rem;
}

.featured-course-content {
    padding: 2rem;
}

.course-meta {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.course-level {
    background: #e2e8f0;
    color: #4a5568;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.course-duration {
    color: #718096;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
}

.featured-course-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 1rem;
    line-height: 1.3;
}

.featured-course-description {
    color: #718096;
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.featured-course-mentor {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
}

.mentor-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 1rem;
}

.mentor-info {
    display: flex;
    flex-direction: column;
}

.mentor-name {
    font-weight: 600;
    color: #2d3748;
    font-size: 1rem;
}

.mentor-title {
    color: #718096;
    font-size: 0.875rem;
}

.featured-course-stats {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
}

.stat {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #4a5568;
    font-size: 0.875rem;
}

.featured-course-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.courses-controls {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.filter-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.filter-btn {
    background: #f7fafc;
    border: 1px solid #e2e8f0;
    color: #4a5568;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-btn:hover,
.filter-btn.active {
    background: #667eea;
    border-color: #667eea;
    color: white;
}

.sort-controls {
    display: flex;
    align-items: center;
    font-size: 0.875rem;
    color: #4a5568;
}

.courses-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.course-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.course-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.course-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.course-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.course-card:hover .course-image img {
    transform: scale(1.05);
}

.course-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.course-card:hover .course-overlay {
    opacity: 1;
}

.course-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.course-badge.free {
    background: #48bb78;
    color: white;
}

.course-badge.new {
    background: #ed8936;
    color: white;
}

.course-content {
    padding: 1.5rem;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.course-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    line-height: 1.4;
}

.course-title a {
    color: #2d3748;
    text-decoration: none;
}

.course-title a:hover {
    color: #667eea;
    text-decoration: none;
}

.course-description {
    color: #718096;
    font-size: 0.875rem;
    line-height: 1.5;
    margin-bottom: 1rem;
    flex: 1;
}

.course-mentor {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.course-mentor .mentor-avatar {
    width: 30px;
    height: 30px;
    margin-right: 0.5rem;
}

.course-mentor .mentor-name {
    color: #4a5568;
    font-size: 0.875rem;
    font-weight: 500;
}

.course-stats {
    margin-bottom: 1rem;
}

.rating {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    margin-bottom: 0.5rem;
}

.rating-text {
    font-weight: 600;
    color: #2d3748;
    margin-left: 0.25rem;
}

.course-actions {
    margin-top: auto;
}

.course-actions-secondary {
    display: flex;
    justify-content: space-between;
    margin-top: 0.5rem;
}

.level-beginner {
    background: #c6f6d5;
    color: #276749;
}

.level-intermediate {
    background: #fed7d7;
    color: #c53030;
}

.level-advanced {
    background: #e9d8fd;
    color: #553c9a;
}

.no-courses {
    text-align: center;
    padding: 4rem 2rem;
}

.no-courses-icon {
    font-size: 4rem;
    color: #cbd5e0;
    margin-bottom: 2rem;
}

.no-courses h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 1rem;
}

.no-courses p {
    color: #718096;
    font-size: 1.1rem;
    margin-bottom: 2rem;
}

.related-categories {
    margin-top: 4rem;
}

.related-categories h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 2rem;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1.5rem;
}

.category-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    text-decoration: none;
    color: inherit;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
}

.category-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    text-decoration: none;
    color: inherit;
}

.category-card .category-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    margin-right: 1rem;
}

.category-card .category-icon i {
    font-size: 1.25rem;
}

.category-card h4 {
    font-size: 1rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.25rem;
}

.category-card p {
    color: #718096;
    font-size: 0.875rem;
    margin: 0;
}

@media (max-width: 768px) {
    .category-header {
        flex-direction: column;
        text-align: center;
    }
    
    .category-icon {
        margin-right: 0;
        margin-bottom: 1rem;
    }
    
    .category-title {
        font-size: 2rem;
    }
    
    .category-stats {
        justify-content: center;
    }
    
    .courses-controls {
        text-align: center;
    }
    
    .courses-controls .row {
        flex-direction: column;
        gap: 1rem;
    }
    
    .sort-controls {
        justify-content: center;
    }
    
    .courses-grid {
        grid-template-columns: 1fr;
    }
    
    .featured-course-actions {
        flex-direction: column;
    }
}
</style>
@endpush

@push('scripts')
<script>
function filterByLevel(level) {
    const url = new URL(window.location);
    if (level) {
        url.searchParams.set('level', level);
    } else {
        url.searchParams.delete('level');
    }
    window.location.href = url.toString();
}

function updateSort(value) {
    const url = new URL(window.location);
    url.searchParams.set('sort', value);
    window.location.href = url.toString();
}

function toggleCategoryFollow() {
    // Category follow functionality
    fetch(`/categories/{{ $category->id }}/follow`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const button = event.target.closest('button');
            const icon = button.querySelector('i');
            const text = button.querySelector('span') || button;
            
            if (data.following) {
                icon.className = 'fas fa-bell-slash';
                text.textContent = ' Unfollow Category';
            } else {
                icon.className = 'fas fa-bell';
                text.textContent = ' Follow Category';
            }
        }
    });
}

function toggleWishlist(courseId) {
    fetch(`/courses/${courseId}/favorite`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const button = event.target.closest('button');
            const icon = button.querySelector('i');
            if (data.favorited) {
                icon.style.color = '#e53e3e';
            } else {
                icon.style.color = '';
            }
        }
    });
}

function shareCourse(courseId) {
    const courseUrl = `{{ url('/courses') }}/${courseId}`;
    if (navigator.share) {
        navigator.share({
            title: 'Check out this course',
            url: courseUrl
        });
    } else {
        navigator.clipboard.writeText(courseUrl).then(() => {
            alert('Course link copied to clipboard!');
        });
    }
}
</script>
@endpush
