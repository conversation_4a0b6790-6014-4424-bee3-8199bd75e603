<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class NotificationTemplate extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'subject',
        'content',
        'type',
        'variables',
        'is_active',
    ];

    protected $casts = [
        'variables' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Get the usage count for this template.
     * This is a placeholder - in a real implementation, you'd track usage.
     */
    public function usages()
    {
        // This would be a relationship to a notifications_sent table or similar
        // For now, return a mock relationship
        return $this->hasMany(self::class, 'id', 'non_existent_field');
    }

    /**
     * Scope for active templates.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for templates by type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }
}
