<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Faq;
use App\Models\FaqCategory;
use Illuminate\Support\Facades\Auth;

class FaqController extends Controller
{
    public function index(Request $request)
    {
        // Get all FAQs with categories
        $faqs = Faq::with(['category'])
            ->where('is_published', true)
            ->orderBy('order', 'asc')
            ->orderBy('created_at', 'desc')
            ->get();

        // Get FAQ categories with counts
        $categories = FaqCategory::withCount(['faqs' => function($query) {
            $query->where('is_published', true);
        }])
        ->orderBy('order', 'asc')
        ->get()
        ->map(function($category) {
            return [
                'name' => $category->name,
                'slug' => $category->slug,
                'icon' => $category->icon,
                'count' => $category->faqs_count,
                'faqs' => $category->faqs()->where('is_published', true)->orderBy('order', 'asc')->get()
            ];
        });

        // Get popular FAQs (most viewed)
        $popularFaqs = Faq::where('is_published', true)
            ->orderBy('views', 'desc')
            ->orderBy('helpful_votes', 'desc')
            ->limit(6)
            ->get();

        return view('pages.faq', compact('faqs', 'categories', 'popularFaqs'));
    }

    public function show(Faq $faq)
    {
        if (!$faq->is_published) {
            abort(404);
        }

        // Increment view count
        $faq->increment('views');

        // Get related FAQs
        $relatedFaqs = Faq::where('category_id', $faq->category_id)
            ->where('id', '!=', $faq->id)
            ->where('is_published', true)
            ->limit(5)
            ->get();

        return view('pages.faq-single', compact('faq', 'relatedFaqs'));
    }

    public function markHelpful(Request $request, Faq $faq)
    {
        $request->validate([
            'helpful' => 'required|boolean'
        ]);

        $isHelpful = $request->helpful;
        $userId = Auth::id();

        // Check if user already voted
        $existingVote = \App\Models\FaqVote::where('faq_id', $faq->id)
            ->where('user_id', $userId)
            ->first();

        if ($existingVote) {
            // Update existing vote
            $existingVote->update(['is_helpful' => $isHelpful]);
        } else {
            // Create new vote
            \App\Models\FaqVote::create([
                'faq_id' => $faq->id,
                'user_id' => $userId,
                'is_helpful' => $isHelpful,
            ]);
        }

        // Recalculate vote counts
        $helpfulCount = \App\Models\FaqVote::where('faq_id', $faq->id)
            ->where('is_helpful', true)
            ->count();

        $unhelpfulCount = \App\Models\FaqVote::where('faq_id', $faq->id)
            ->where('is_helpful', false)
            ->count();

        // Update FAQ vote counts
        $faq->update([
            'helpful_votes' => $helpfulCount,
            'unhelpful_votes' => $unhelpfulCount,
        ]);

        return response()->json([
            'success' => true,
            'helpful_count' => $helpfulCount,
            'unhelpful_count' => $unhelpfulCount,
        ]);
    }

    public function trackView(Faq $faq)
    {
        if (!$faq->is_published) {
            return response()->json(['success' => false], 404);
        }

        // Increment view count (with some basic rate limiting)
        $sessionKey = 'faq_viewed_' . $faq->id;
        if (!session()->has($sessionKey)) {
            $faq->increment('views');
            session()->put($sessionKey, true);
        }

        return response()->json(['success' => true]);
    }

    public function search(Request $request)
    {
        $query = $request->get('q', '');
        
        if (strlen($query) < 2) {
            return response()->json([]);
        }

        $results = Faq::where('is_published', true)
            ->where(function($q) use ($query) {
                $q->where('question', 'LIKE', "%{$query}%")
                  ->orWhere('answer', 'LIKE', "%{$query}%")
                  ->orWhere('keywords', 'LIKE', "%{$query}%");
            })
            ->with('category')
            ->orderBy('views', 'desc')
            ->limit(10)
            ->get()
            ->map(function($faq) {
                return [
                    'id' => $faq->id,
                    'question' => $faq->question,
                    'category' => $faq->category->name,
                    'url' => route('faq.show', $faq),
                ];
            });

        return response()->json($results);
    }
}
