<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Lesson;
use App\Models\UserProgress;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class LessonController extends Controller
{
    /**
     * Display the specified lesson.
     */
    public function show(Lesson $lesson)
    {
        // Check if user has access to this lesson
        if (!$lesson->is_published || !$lesson->course->is_published) {
            return response()->json([
                'success' => false,
                'message' => 'Lesson not found',
            ], 404);
        }

        // Check subscription access
        if (!$lesson->is_free && !Auth::user()->hasActiveSubscription()) {
            return response()->json([
                'success' => false,
                'message' => 'Subscription required to access this lesson',
            ], 403);
        }

        $lesson->load(['course']);

        // Get user progress
        $progress = Auth::user()->userProgress()
            ->where('lesson_id', $lesson->id)
            ->first();

        $lesson->user_progress = $progress;

        return response()->json([
            'success' => true,
            'data' => $lesson,
        ]);
    }

    /**
     * Update lesson progress.
     */
    public function updateProgress(Request $request, Lesson $lesson)
    {
        $request->validate([
            'progress' => 'required|integer|min:0|max:100',
            'watch_time' => 'nullable|integer|min:0',
        ]);

        $progress = UserProgress::updateOrCreate(
            [
                'user_id' => Auth::id(),
                'course_id' => $lesson->course_id,
                'lesson_id' => $lesson->id,
            ],
            [
                'completion_percentage' => $request->progress,
                'watch_time_seconds' => $request->watch_time ?? 0,
                'last_accessed_at' => now(),
            ]
        );

        return response()->json([
            'success' => true,
            'data' => $progress,
        ]);
    }

    /**
     * Mark lesson as complete.
     */
    public function complete(Lesson $lesson)
    {
        $progress = UserProgress::updateOrCreate(
            [
                'user_id' => Auth::id(),
                'course_id' => $lesson->course_id,
                'lesson_id' => $lesson->id,
            ],
            [
                'is_completed' => true,
                'completion_percentage' => 100,
                'completed_at' => now(),
                'last_accessed_at' => now(),
            ]
        );

        // Check if course is completed
        $totalLessons = $lesson->course->lessons()->where('is_published', true)->count();
        $completedLessons = Auth::user()->userProgress()
            ->where('course_id', $lesson->course_id)
            ->where('is_completed', true)
            ->count();

        $courseCompleted = $totalLessons > 0 && $completedLessons >= $totalLessons;

        return response()->json([
            'success' => true,
            'data' => [
                'lesson_progress' => $progress,
                'course_completed' => $courseCompleted,
                'course_progress' => $totalLessons > 0 ? ($completedLessons / $totalLessons) * 100 : 0,
            ],
        ]);
    }
}
