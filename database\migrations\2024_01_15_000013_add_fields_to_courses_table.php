<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('courses', function (Blueprint $table) {
            // Add category_id field if it doesn't exist
            if (!Schema::hasColumn('courses', 'category_id')) {
                $table->foreignId('category_id')->nullable()->after('mentor_id')->constrained('categories')->onDelete('set null');
            }
            
            // Add additional fields that might be missing
            if (!Schema::hasColumn('courses', 'difficulty_level')) {
                $table->enum('difficulty_level', ['beginner', 'intermediate', 'advanced'])->default('beginner')->after('category_id');
            }
            
            if (!Schema::hasColumn('courses', 'total_duration')) {
                $table->integer('total_duration')->default(0)->after('difficulty_level'); // Duration in hours
            }
            
            if (!Schema::hasColumn('courses', 'is_free')) {
                $table->boolean('is_free')->default(false)->after('price');
            }
            
            if (!Schema::hasColumn('courses', 'average_rating')) {
                $table->decimal('average_rating', 3, 1)->default(0)->after('is_free');
            }
        });

        // Add indexes for the new fields
        Schema::table('courses', function (Blueprint $table) {
            $table->index('category_id');
            $table->index('difficulty_level');
            $table->index('is_free');
            $table->index('average_rating');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('courses', function (Blueprint $table) {
            $table->dropIndex(['category_id']);
            $table->dropIndex(['difficulty_level']);
            $table->dropIndex(['is_free']);
            $table->dropIndex(['average_rating']);
            
            $table->dropForeign(['category_id']);
            $table->dropColumn([
                'category_id',
                'difficulty_level',
                'total_duration',
                'is_free',
                'average_rating'
            ]);
        });
    }
};
