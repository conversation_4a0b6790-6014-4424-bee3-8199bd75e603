@extends('layouts.app')

@section('title', 'Frequently Asked Questions')

@section('content')
<div class="faq-container">
    <!-- FAQ Hero -->
    <div class="faq-hero">
        <div class="container">
            <div class="row justify-content-center text-center">
                <div class="col-lg-8">
                    <h1 class="hero-title">Frequently Asked Questions</h1>
                    <p class="hero-subtitle">
                        Find answers to common questions about The Real World membership, courses, and community
                    </p>
                    
                    <!-- Search FAQ -->
                    <div class="faq-search">
                        <div class="search-input-group">
                            <input type="text" id="faqSearch" class="form-control" 
                                   placeholder="Search for answers..." autocomplete="off">
                            <div class="search-icon">
                                <i class="fas fa-search"></i>
                            </div>
                        </div>
                        <div class="search-suggestions" id="searchSuggestions"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="faq-content">
        <div class="container">
            <div class="row">
                <!-- FAQ Categories -->
                <div class="col-lg-3">
                    <div class="faq-sidebar">
                        <h4>Categories</h4>
                        <div class="category-list">
                            <button class="category-item active" data-category="all">
                                <i class="fas fa-list"></i>
                                All Questions
                                <span class="count">{{ $faqs->count() }}</span>
                            </button>
                            
                            @foreach($categories as $category)
                            <button class="category-item" data-category="{{ $category['slug'] }}">
                                <i class="fas fa-{{ $category['icon'] }}"></i>
                                {{ $category['name'] }}
                                <span class="count">{{ $category['count'] }}</span>
                            </button>
                            @endforeach
                        </div>
                        
                        <!-- Quick Links -->
                        <div class="quick-links">
                            <h5>Quick Links</h5>
                            <a href="{{ route('contact') }}" class="quick-link">
                                <i class="fas fa-envelope"></i>
                                Contact Support
                            </a>
                            <a href="{{ route('community.index') }}" class="quick-link">
                                <i class="fas fa-users"></i>
                                Community Forum
                            </a>
                            <a href="{{ route('subscriptions.plans') }}" class="quick-link">
                                <i class="fas fa-crown"></i>
                                View Plans
                            </a>
                        </div>
                    </div>
                </div>

                <!-- FAQ Content -->
                <div class="col-lg-9">
                    <!-- Popular Questions -->
                    <div class="popular-questions">
                        <h2 class="section-title">
                            <i class="fas fa-fire"></i>
                            Most Popular Questions
                        </h2>
                        <div class="popular-grid">
                            @foreach($popularFaqs as $faq)
                            <div class="popular-card" onclick="scrollToFaq('{{ $faq->id }}')">
                                <div class="popular-icon">
                                    <i class="fas fa-{{ $faq->icon }}"></i>
                                </div>
                                <h4>{{ $faq->question }}</h4>
                                <p>{{ Str::limit(strip_tags($faq->answer), 80) }}</p>
                                <div class="popular-meta">
                                    <span class="views">{{ $faq->views }} views</span>
                                    <span class="helpful">{{ $faq->helpful_votes }} helpful</span>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>

                    <!-- FAQ Accordion -->
                    <div class="faq-accordion">
                        @foreach($categories as $category)
                        <div class="category-section" data-category="{{ $category['slug'] }}">
                            <h2 class="category-title">
                                <i class="fas fa-{{ $category['icon'] }}"></i>
                                {{ $category['name'] }}
                            </h2>
                            
                            <div class="accordion" id="accordion{{ $category['slug'] }}">
                                @foreach($category['faqs'] as $faq)
                                <div class="faq-item" id="faq-{{ $faq->id }}" data-category="{{ $category['slug'] }}">
                                    <div class="faq-header" data-toggle="collapse" 
                                         data-target="#collapse{{ $faq->id }}" 
                                         aria-expanded="false">
                                        <h3 class="faq-question">
                                            {{ $faq->question }}
                                        </h3>
                                        <div class="faq-toggle">
                                            <i class="fas fa-plus"></i>
                                        </div>
                                    </div>
                                    
                                    <div id="collapse{{ $faq->id }}" class="collapse" 
                                         data-parent="#accordion{{ $category['slug'] }}">
                                        <div class="faq-body">
                                            <div class="faq-answer">
                                                {!! $faq->answer !!}
                                            </div>
                                            
                                            @if($faq->related_links)
                                            <div class="related-links">
                                                <h5>Related Links:</h5>
                                                <ul>
                                                    @foreach($faq->related_links as $link)
                                                    <li><a href="{{ $link['url'] }}" target="_blank">{{ $link['title'] }}</a></li>
                                                    @endforeach
                                                </ul>
                                            </div>
                                            @endif
                                            
                                            <div class="faq-actions">
                                                <div class="helpful-section">
                                                    <span class="helpful-text">Was this helpful?</span>
                                                    <button class="helpful-btn" onclick="markHelpful({{ $faq->id }}, true)">
                                                        <i class="fas fa-thumbs-up"></i>
                                                        Yes ({{ $faq->helpful_votes }})
                                                    </button>
                                                    <button class="helpful-btn" onclick="markHelpful({{ $faq->id }}, false)">
                                                        <i class="fas fa-thumbs-down"></i>
                                                        No ({{ $faq->unhelpful_votes }})
                                                    </button>
                                                </div>
                                                
                                                <div class="share-section">
                                                    <button class="share-btn" onclick="shareFaq({{ $faq->id }})">
                                                        <i class="fas fa-share-alt"></i>
                                                        Share
                                                    </button>
                                                </div>
                                            </div>
                                            
                                            <div class="faq-meta">
                                                <small class="text-muted">
                                                    Last updated: {{ $faq->updated_at->format('M d, Y') }} • 
                                                    {{ $faq->views }} views
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                @endforeach
                            </div>
                        </div>
                        @endforeach
                    </div>

                    <!-- Still Need Help -->
                    <div class="help-section">
                        <div class="help-card">
                            <div class="help-icon">
                                <i class="fas fa-question-circle"></i>
                            </div>
                            <div class="help-content">
                                <h3>Still need help?</h3>
                                <p>Can't find what you're looking for? Our support team is here to help you succeed.</p>
                                <div class="help-options">
                                    <a href="{{ route('contact') }}" class="btn btn-primary">
                                        <i class="fas fa-envelope"></i>
                                        Contact Support
                                    </a>
                                    <a href="{{ route('community.index') }}" class="btn btn-outline-primary">
                                        <i class="fas fa-users"></i>
                                        Ask Community
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.faq-container {
    background: #f8f9fc;
    min-height: 100vh;
}

.faq-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4rem 0 3rem;
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.hero-subtitle {
    font-size: 1.25rem;
    opacity: 0.9;
    margin-bottom: 3rem;
    line-height: 1.6;
}

.faq-search {
    max-width: 500px;
    margin: 0 auto;
    position: relative;
}

.search-input-group {
    position: relative;
}

.search-input-group .form-control {
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50px;
    padding: 1rem 3rem 1rem 1.5rem;
    font-size: 1.1rem;
    color: #2d3748;
}

.search-input-group .form-control::placeholder {
    color: #718096;
}

.search-icon {
    position: absolute;
    right: 1.5rem;
    top: 50%;
    transform: translateY(-50%);
    color: #718096;
    font-size: 1.25rem;
}

.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    display: none;
    margin-top: 0.5rem;
}

.suggestion-item {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e2e8f0;
    cursor: pointer;
    color: #2d3748;
}

.suggestion-item:hover {
    background: #f7fafc;
}

.suggestion-item:last-child {
    border-bottom: none;
}

.faq-content {
    padding: 3rem 0;
}

.faq-sidebar {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    position: sticky;
    top: 2rem;
}

.faq-sidebar h4 {
    color: #2d3748;
    margin-bottom: 1.5rem;
    font-weight: 600;
}

.category-list {
    margin-bottom: 2rem;
}

.category-item {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 1rem;
    background: transparent;
    border: none;
    border-radius: 10px;
    color: #4a5568;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 0.5rem;
}

.category-item:hover,
.category-item.active {
    background: #667eea;
    color: white;
}

.category-item i {
    margin-right: 0.75rem;
    width: 20px;
}

.category-item .count {
    margin-left: auto;
    background: rgba(255, 255, 255, 0.2);
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
}

.category-item.active .count {
    background: rgba(255, 255, 255, 0.3);
}

.quick-links h5 {
    color: #2d3748;
    margin-bottom: 1rem;
    font-weight: 600;
    font-size: 1rem;
}

.quick-link {
    display: flex;
    align-items: center;
    color: #4a5568;
    text-decoration: none;
    padding: 0.75rem 0;
    border-bottom: 1px solid #e2e8f0;
    transition: color 0.3s ease;
}

.quick-link:hover {
    color: #667eea;
    text-decoration: none;
}

.quick-link i {
    margin-right: 0.75rem;
    width: 16px;
}

.quick-link:last-child {
    border-bottom: none;
}

.popular-questions {
    margin-bottom: 3rem;
}

.section-title {
    color: #2d3748;
    font-size: 1.75rem;
    font-weight: 600;
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
}

.section-title i {
    margin-right: 0.75rem;
    color: #667eea;
}

.popular-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.popular-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    cursor: pointer;
    transition: all 0.3s ease;
}

.popular-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.popular-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    font-size: 1.25rem;
}

.popular-card h4 {
    color: #2d3748;
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    line-height: 1.4;
}

.popular-card p {
    color: #718096;
    font-size: 0.875rem;
    line-height: 1.5;
    margin-bottom: 1rem;
}

.popular-meta {
    display: flex;
    justify-content: space-between;
    font-size: 0.75rem;
    color: #a0aec0;
}

.category-section {
    margin-bottom: 3rem;
}

.category-title {
    color: #2d3748;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e2e8f0;
}

.category-title i {
    margin-right: 0.75rem;
    color: #667eea;
}

.faq-item {
    background: white;
    border-radius: 10px;
    margin-bottom: 1rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.faq-header {
    padding: 1.5rem;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background 0.3s ease;
}

.faq-header:hover {
    background: #f7fafc;
}

.faq-question {
    color: #2d3748;
    font-size: 1.125rem;
    font-weight: 600;
    margin: 0;
    flex: 1;
    line-height: 1.4;
}

.faq-toggle {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: #667eea;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.3s ease;
}

.faq-header[aria-expanded="true"] .faq-toggle {
    transform: rotate(45deg);
}

.faq-body {
    padding: 0 1.5rem 1.5rem;
}

.faq-answer {
    color: #4a5568;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.faq-answer h4 {
    color: #2d3748;
    margin-top: 1.5rem;
    margin-bottom: 1rem;
}

.faq-answer ul,
.faq-answer ol {
    margin-left: 1.5rem;
    margin-bottom: 1rem;
}

.faq-answer li {
    margin-bottom: 0.5rem;
}

.related-links {
    background: #f7fafc;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1.5rem;
}

.related-links h5 {
    color: #2d3748;
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
}

.related-links ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.related-links li {
    margin-bottom: 0.5rem;
}

.related-links a {
    color: #667eea;
    text-decoration: none;
    font-size: 0.875rem;
}

.related-links a:hover {
    text-decoration: underline;
}

.faq-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 1rem;
    border-top: 1px solid #e2e8f0;
    margin-bottom: 1rem;
}

.helpful-section {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.helpful-text {
    color: #4a5568;
    font-size: 0.875rem;
    font-weight: 500;
}

.helpful-btn {
    background: transparent;
    border: 1px solid #e2e8f0;
    color: #4a5568;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.helpful-btn:hover {
    background: #f7fafc;
    border-color: #cbd5e0;
}

.helpful-btn.voted {
    background: #667eea;
    border-color: #667eea;
    color: white;
}

.share-btn {
    background: transparent;
    border: 1px solid #e2e8f0;
    color: #4a5568;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.share-btn:hover {
    background: #f7fafc;
    border-color: #cbd5e0;
}

.faq-meta {
    color: #a0aec0;
    font-size: 0.75rem;
}

.help-section {
    margin-top: 4rem;
}

.help-card {
    background: white;
    border-radius: 20px;
    padding: 3rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.help-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    font-size: 2rem;
}

.help-content h3 {
    color: #2d3748;
    font-size: 1.75rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.help-content p {
    color: #718096;
    font-size: 1.125rem;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.help-options {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

@media (max-width: 768px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .faq-sidebar {
        margin-bottom: 2rem;
    }
    
    .popular-grid {
        grid-template-columns: 1fr;
    }
    
    .faq-header {
        padding: 1rem;
    }
    
    .faq-body {
        padding: 0 1rem 1rem;
    }
    
    .faq-actions {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .helpful-section {
        justify-content: center;
    }
    
    .help-card {
        padding: 2rem;
    }
    
    .help-options {
        flex-direction: column;
        align-items: center;
    }
    
    .help-options .btn {
        width: 100%;
        max-width: 300px;
    }
}

.category-section[style*="display: none"] {
    display: none !important;
}

.faq-item[style*="display: none"] {
    display: none !important;
}
</style>
@endpush

@push('scripts')
<script>
// FAQ Search functionality
document.getElementById('faqSearch').addEventListener('input', function() {
    const query = this.value.toLowerCase();
    const faqItems = document.querySelectorAll('.faq-item');
    const categoryItems = document.querySelectorAll('.category-item');
    
    if (query.length === 0) {
        // Show all items
        faqItems.forEach(item => item.style.display = 'block');
        document.querySelectorAll('.category-section').forEach(section => section.style.display = 'block');
        categoryItems.forEach(item => item.classList.remove('active'));
        categoryItems[0].classList.add('active');
        return;
    }
    
    let hasResults = false;
    
    faqItems.forEach(item => {
        const question = item.querySelector('.faq-question').textContent.toLowerCase();
        const answer = item.querySelector('.faq-answer').textContent.toLowerCase();
        
        if (question.includes(query) || answer.includes(query)) {
            item.style.display = 'block';
            item.closest('.category-section').style.display = 'block';
            hasResults = true;
        } else {
            item.style.display = 'none';
        }
    });
    
    // Hide empty categories
    document.querySelectorAll('.category-section').forEach(section => {
        const visibleItems = section.querySelectorAll('.faq-item[style*="block"]');
        if (visibleItems.length === 0) {
            section.style.display = 'none';
        }
    });
    
    // Update active category
    categoryItems.forEach(item => item.classList.remove('active'));
});

// Category filtering
document.querySelectorAll('.category-item').forEach(item => {
    item.addEventListener('click', function() {
        const category = this.dataset.category;
        
        // Update active state
        document.querySelectorAll('.category-item').forEach(cat => cat.classList.remove('active'));
        this.classList.add('active');
        
        // Clear search
        document.getElementById('faqSearch').value = '';
        
        // Show/hide categories
        if (category === 'all') {
            document.querySelectorAll('.category-section').forEach(section => section.style.display = 'block');
            document.querySelectorAll('.faq-item').forEach(item => item.style.display = 'block');
        } else {
            document.querySelectorAll('.category-section').forEach(section => {
                if (section.dataset.category === category) {
                    section.style.display = 'block';
                } else {
                    section.style.display = 'none';
                }
            });
        }
    });
});

// Scroll to FAQ
function scrollToFaq(faqId) {
    const faqElement = document.getElementById(`faq-${faqId}`);
    if (faqElement) {
        // Open the accordion item
        const collapseElement = faqElement.querySelector('.collapse');
        $(collapseElement).collapse('show');
        
        // Scroll to element
        setTimeout(() => {
            faqElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }, 300);
    }
}

// Mark as helpful
function markHelpful(faqId, isHelpful) {
    fetch(`/faq/${faqId}/helpful`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ helpful: isHelpful })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update button states and counts
            const faqElement = document.getElementById(`faq-${faqId}`);
            const helpfulBtn = faqElement.querySelector('.helpful-btn:first-of-type');
            const unhelpfulBtn = faqElement.querySelector('.helpful-btn:last-of-type');
            
            if (isHelpful) {
                helpfulBtn.classList.add('voted');
                unhelpfulBtn.classList.remove('voted');
                helpfulBtn.innerHTML = `<i class="fas fa-thumbs-up"></i> Yes (${data.helpful_count})`;
            } else {
                unhelpfulBtn.classList.add('voted');
                helpfulBtn.classList.remove('voted');
                unhelpfulBtn.innerHTML = `<i class="fas fa-thumbs-down"></i> No (${data.unhelpful_count})`;
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
    });
}

// Share FAQ
function shareFaq(faqId) {
    const url = `${window.location.origin}/faq#faq-${faqId}`;
    
    if (navigator.share) {
        navigator.share({
            title: 'FAQ - The Real World',
            url: url
        });
    } else {
        navigator.clipboard.writeText(url).then(() => {
            alert('FAQ link copied to clipboard!');
        });
    }
}

// Track FAQ views
document.querySelectorAll('.faq-header').forEach(header => {
    header.addEventListener('click', function() {
        const faqId = this.closest('.faq-item').id.replace('faq-', '');
        
        // Track view
        fetch(`/faq/${faqId}/view`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });
    });
});

// Auto-expand FAQ from URL hash
document.addEventListener('DOMContentLoaded', function() {
    if (window.location.hash) {
        const faqId = window.location.hash.substring(1);
        if (faqId.startsWith('faq-')) {
            scrollToFaq(faqId.replace('faq-', ''));
        }
    }
});
</script>
@endpush
