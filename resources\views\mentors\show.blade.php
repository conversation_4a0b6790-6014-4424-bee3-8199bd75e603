@extends('layouts.app')

@section('title', $mentor->name . ' - <PERSON><PERSON> Profile')

@section('content')
<div class="mentor-profile-container">
    <!-- <PERSON><PERSON> Hero -->
    <div class="mentor-hero">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-4">
                    <div class="mentor-avatar-section">
                        <div class="mentor-avatar">
                            <img src="{{ $mentor->avatar ? asset('storage/' . $mentor->avatar) : asset('images/default-avatar.png') }}" 
                                 alt="{{ $mentor->name }}">
                            @if($mentor->is_online)
                            <div class="online-indicator"></div>
                            @endif
                        </div>
                        <div class="mentor-verification">
                            @if($mentor->is_verified)
                            <div class="verified-badge">
                                <i class="fas fa-check-circle"></i>
                                Verified Mentor
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-8">
                    <div class="mentor-info">
                        <div class="mentor-header">
                            <h1 class="mentor-name">{{ $mentor->name }}</h1>
                            <p class="mentor-title">{{ $mentor->title }}</p>
                            <div class="mentor-location">
                                <i class="fas fa-map-marker-alt"></i>
                                {{ $mentor->location }}
                            </div>
                        </div>
                        
                        <div class="mentor-stats">
                            <div class="stat-item">
                                <div class="stat-number">${{ number_format($mentor->revenue) }}M+</div>
                                <div class="stat-label">Revenue Generated</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">{{ number_format($mentor->students_count) }}+</div>
                                <div class="stat-label">Students Taught</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">{{ $mentor->courses_count }}</div>
                                <div class="stat-label">Courses</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">{{ number_format($mentor->average_rating, 1) }}</div>
                                <div class="stat-label">Rating</div>
                            </div>
                        </div>
                        
                        <div class="mentor-actions">
                            @auth
                            <button class="btn btn-primary btn-lg" onclick="followMentor({{ $mentor->id }})">
                                <i class="fas fa-{{ $mentor->is_followed ? 'heart' : 'plus' }}"></i>
                                {{ $mentor->is_followed ? 'Following' : 'Follow' }}
                            </button>
                            @if($mentor->allows_direct_messages)
                            <button class="btn btn-outline-primary btn-lg" onclick="sendMessage()">
                                <i class="fas fa-envelope"></i>
                                Message
                            </button>
                            @endif
                            @else
                            <a href="{{ route('login') }}" class="btn btn-primary btn-lg">
                                <i class="fas fa-sign-in-alt"></i>
                                Login to Follow
                            </a>
                            @endauth
                            
                            <div class="social-links">
                                @if($mentor->social_links['twitter'])
                                <a href="{{ $mentor->social_links['twitter'] }}" target="_blank" class="social-link">
                                    <i class="fab fa-twitter"></i>
                                </a>
                                @endif
                                @if($mentor->social_links['instagram'])
                                <a href="{{ $mentor->social_links['instagram'] }}" target="_blank" class="social-link">
                                    <i class="fab fa-instagram"></i>
                                </a>
                                @endif
                                @if($mentor->social_links['linkedin'])
                                <a href="{{ $mentor->social_links['linkedin'] }}" target="_blank" class="social-link">
                                    <i class="fab fa-linkedin"></i>
                                </a>
                                @endif
                                @if($mentor->social_links['youtube'])
                                <a href="{{ $mentor->social_links['youtube'] }}" target="_blank" class="social-link">
                                    <i class="fab fa-youtube"></i>
                                </a>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="mentor-content">
        <div class="container">
            <div class="row">
                <!-- Main Content -->
                <div class="col-lg-8">
                    <!-- About Section -->
                    <div class="content-section">
                        <h2 class="section-title">About {{ $mentor->name }}</h2>
                        <div class="mentor-bio">
                            {!! nl2br(e($mentor->bio)) !!}
                        </div>
                        
                        @if($mentor->specialties)
                        <div class="specialties-section">
                            <h3>Specialties</h3>
                            <div class="specialties-list">
                                @foreach($mentor->specialties as $specialty)
                                <span class="specialty-tag">{{ $specialty }}</span>
                                @endforeach
                            </div>
                        </div>
                        @endif
                    </div>

                    <!-- Achievements Section -->
                    @if($mentor->achievements->count() > 0)
                    <div class="content-section">
                        <h2 class="section-title">Achievements & Milestones</h2>
                        <div class="achievements-timeline">
                            @foreach($mentor->achievements as $achievement)
                            <div class="timeline-item">
                                <div class="timeline-marker">
                                    <i class="fas fa-trophy"></i>
                                </div>
                                <div class="timeline-content">
                                    <div class="timeline-date">{{ $achievement->date->format('Y') }}</div>
                                    <h4 class="timeline-title">{{ $achievement->title }}</h4>
                                    <p class="timeline-description">{{ $achievement->description }}</p>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                    @endif

                    <!-- Courses Section -->
                    <div class="content-section">
                        <div class="section-header">
                            <h2 class="section-title">Courses by {{ $mentor->name }}</h2>
                            <a href="{{ route('courses.index', ['mentor' => $mentor->id]) }}" class="btn btn-outline-primary">
                                View All Courses
                            </a>
                        </div>
                        
                        <div class="courses-grid">
                            @foreach($mentor->courses->take(4) as $course)
                            <div class="course-card">
                                <div class="course-image">
                                    <img src="{{ $course->thumbnail ? asset('storage/' . $course->thumbnail) : asset('images/default-course-thumbnail.jpg') }}" 
                                         alt="{{ $course->title }}">
                                    @if($course->is_free)
                                    <div class="course-badge free">Free</div>
                                    @endif
                                </div>
                                <div class="course-content">
                                    <h4 class="course-title">{{ $course->title }}</h4>
                                    <p class="course-description">{{ Str::limit($course->description, 100) }}</p>
                                    <div class="course-meta">
                                        <span class="course-duration">
                                            <i class="fas fa-clock"></i>
                                            {{ $course->total_duration }}h
                                        </span>
                                        <span class="course-students">
                                            <i class="fas fa-users"></i>
                                            {{ $course->enrollments_count }}
                                        </span>
                                        <span class="course-rating">
                                            <i class="fas fa-star"></i>
                                            {{ number_format($course->average_rating, 1) }}
                                        </span>
                                    </div>
                                    <a href="{{ route('courses.show', $course) }}" class="btn btn-primary btn-sm btn-block">
                                        View Course
                                    </a>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>

                    <!-- Reviews Section -->
                    <div class="content-section">
                        <h2 class="section-title">Student Reviews</h2>
                        <div class="reviews-summary">
                            <div class="rating-overview">
                                <div class="overall-rating">
                                    <span class="rating-number">{{ number_format($mentor->average_rating, 1) }}</span>
                                    <div class="rating-stars">
                                        @for($i = 1; $i <= 5; $i++)
                                            <i class="fas fa-star {{ $i <= $mentor->average_rating ? 'active' : '' }}"></i>
                                        @endfor
                                    </div>
                                    <span class="rating-count">{{ $mentor->reviews_count }} reviews</span>
                                </div>
                                
                                <div class="rating-breakdown">
                                    @for($i = 5; $i >= 1; $i--)
                                    <div class="rating-bar">
                                        <span class="rating-label">{{ $i }} star</span>
                                        <div class="bar-container">
                                            <div class="bar-fill" style="width: {{ ($mentor->rating_breakdown[$i] ?? 0) }}%"></div>
                                        </div>
                                        <span class="rating-percentage">{{ $mentor->rating_breakdown[$i] ?? 0 }}%</span>
                                    </div>
                                    @endfor
                                </div>
                            </div>
                        </div>
                        
                        <div class="reviews-list">
                            @foreach($mentor->reviews->take(5) as $review)
                            <div class="review-item">
                                <div class="review-header">
                                    <div class="reviewer-info">
                                        <img src="{{ $review->user->avatar ? asset('storage/' . $review->user->avatar) : asset('images/default-avatar.png') }}" 
                                             alt="{{ $review->user->name }}" class="reviewer-avatar">
                                        <div class="reviewer-details">
                                            <span class="reviewer-name">{{ $review->user->name }}</span>
                                            <span class="review-date">{{ $review->created_at->diffForHumans() }}</span>
                                        </div>
                                    </div>
                                    <div class="review-rating">
                                        @for($i = 1; $i <= 5; $i++)
                                            <i class="fas fa-star {{ $i <= $review->rating ? 'active' : '' }}"></i>
                                        @endfor
                                    </div>
                                </div>
                                <div class="review-content">
                                    <p>{{ $review->comment }}</p>
                                </div>
                                @if($review->course)
                                <div class="review-course">
                                    <small class="text-muted">Course: {{ $review->course->title }}</small>
                                </div>
                                @endif
                            </div>
                            @endforeach
                        </div>
                        
                        @if($mentor->reviews_count > 5)
                        <div class="text-center">
                            <button class="btn btn-outline-primary" onclick="loadMoreReviews()">
                                Load More Reviews
                            </button>
                        </div>
                        @endif
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="col-lg-4">
                    <div class="mentor-sidebar">
                        <!-- Quick Stats -->
                        <div class="sidebar-card">
                            <h4>Quick Stats</h4>
                            <div class="quick-stats">
                                <div class="quick-stat">
                                    <div class="stat-icon">
                                        <i class="fas fa-calendar"></i>
                                    </div>
                                    <div class="stat-info">
                                        <span class="stat-label">Member Since</span>
                                        <span class="stat-value">{{ $mentor->created_at->format('M Y') }}</span>
                                    </div>
                                </div>
                                
                                <div class="quick-stat">
                                    <div class="stat-icon">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <div class="stat-info">
                                        <span class="stat-label">Response Time</span>
                                        <span class="stat-value">{{ $mentor->avg_response_time ?? 'Within 24h' }}</span>
                                    </div>
                                </div>
                                
                                <div class="quick-stat">
                                    <div class="stat-icon">
                                        <i class="fas fa-language"></i>
                                    </div>
                                    <div class="stat-info">
                                        <span class="stat-label">Languages</span>
                                        <span class="stat-value">{{ implode(', ', $mentor->languages ?? ['English']) }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Upcoming Live Calls -->
                        @if($mentor->upcomingLiveCalls->count() > 0)
                        <div class="sidebar-card">
                            <h4>Upcoming Live Calls</h4>
                            <div class="live-calls-list">
                                @foreach($mentor->upcomingLiveCalls->take(3) as $liveCall)
                                <div class="live-call-item">
                                    <div class="call-date">
                                        <div class="date-day">{{ $liveCall->scheduled_at->format('d') }}</div>
                                        <div class="date-month">{{ $liveCall->scheduled_at->format('M') }}</div>
                                    </div>
                                    <div class="call-info">
                                        <h5>{{ $liveCall->title }}</h5>
                                        <p class="call-time">
                                            <i class="fas fa-clock"></i>
                                            {{ $liveCall->scheduled_at->format('g:i A') }}
                                        </p>
                                        <a href="{{ route('live-calls.show', $liveCall) }}" class="btn btn-sm btn-primary">
                                            Join Call
                                        </a>
                                    </div>
                                </div>
                                @endforeach
                            </div>
                        </div>
                        @endif

                        <!-- Contact Info -->
                        <div class="sidebar-card">
                            <h4>Get in Touch</h4>
                            <div class="contact-options">
                                @if($mentor->allows_direct_messages)
                                <button class="contact-btn" onclick="sendMessage()">
                                    <i class="fas fa-envelope"></i>
                                    Send Message
                                </button>
                                @endif
                                
                                @if($mentor->booking_enabled)
                                <button class="contact-btn" onclick="bookConsultation()">
                                    <i class="fas fa-calendar-plus"></i>
                                    Book Consultation
                                </button>
                                @endif
                                
                                <a href="{{ route('courses.index', ['mentor' => $mentor->id]) }}" class="contact-btn">
                                    <i class="fas fa-book"></i>
                                    View All Courses
                                </a>
                            </div>
                        </div>

                        <!-- Similar Mentors -->
                        @if($similarMentors->count() > 0)
                        <div class="sidebar-card">
                            <h4>Similar Mentors</h4>
                            <div class="similar-mentors">
                                @foreach($similarMentors as $similarMentor)
                                <div class="similar-mentor-item">
                                    <img src="{{ $similarMentor->avatar ? asset('storage/' . $similarMentor->avatar) : asset('images/default-avatar.png') }}" 
                                         alt="{{ $similarMentor->name }}" class="similar-mentor-avatar">
                                    <div class="similar-mentor-info">
                                        <h6>{{ $similarMentor->name }}</h6>
                                        <p>{{ $similarMentor->title }}</p>
                                        <div class="similar-mentor-rating">
                                            @for($i = 1; $i <= 5; $i++)
                                                <i class="fas fa-star {{ $i <= $similarMentor->average_rating ? 'active' : '' }}"></i>
                                            @endfor
                                            <span>{{ number_format($similarMentor->average_rating, 1) }}</span>
                                        </div>
                                    </div>
                                    <a href="{{ route('mentors.show', $similarMentor) }}" class="btn btn-sm btn-outline-primary">
                                        View
                                    </a>
                                </div>
                                @endforeach
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
