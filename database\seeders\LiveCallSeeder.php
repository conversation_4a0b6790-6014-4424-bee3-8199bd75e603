<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\LiveCall;
use App\Models\User;
use Carbon\Carbon;

class LiveCallSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get mentor user
        $mentor = User::where('email', '<EMAIL>')->first();

        if (!$mentor) {
            $this->command->error('Mentor user not found. Please run CourseSeeder first.');
            return;
        }

        $liveCalls = [
            [
                'title' => 'Crypto Market Analysis & Trading Strategies',
                'description' => 'Join <PERSON> for an exclusive live session on cryptocurrency market analysis. Learn advanced trading strategies, risk management techniques, and how to identify profitable opportunities in the volatile crypto market.',
                'scheduled_at' => now()->addDays(2)->setTime(19, 0), // 2 days from now at 7 PM
                'duration_minutes' => 90,
                'meeting_url' => 'https://zoom.us/j/123456789',
                'meeting_id' => '123-456-789',
                'meeting_password' => 'crypto2024',
                'max_attendees' => 500,
                'required_plans' => ['challenger', 'hero', 'champion'],
                'status' => 'scheduled',
                'is_recorded' => true,
            ],
            [
                'title' => 'Building Your E-commerce Empire',
                'description' => 'Discover the secrets to building a successful e-commerce business from scratch. This live call covers product selection, marketing strategies, scaling techniques, and automation systems.',
                'scheduled_at' => now()->addDays(5)->setTime(20, 0), // 5 days from now at 8 PM
                'duration_minutes' => 120,
                'meeting_url' => 'https://zoom.us/j/987654321',
                'meeting_id' => '987-654-321',
                'meeting_password' => 'ecommerce2024',
                'max_attendees' => 300,
                'required_plans' => ['challenger', 'hero', 'champion'],
                'status' => 'scheduled',
                'is_recorded' => true,
            ],
            [
                'title' => 'Copywriting Mastery: Words That Sell',
                'description' => 'Master the art of persuasive copywriting in this intensive live session. Learn proven frameworks, psychological triggers, and advanced techniques to create copy that converts.',
                'scheduled_at' => now()->addDays(7)->setTime(18, 0), // 7 days from now at 6 PM
                'duration_minutes' => 75,
                'meeting_url' => 'https://zoom.us/j/456789123',
                'meeting_id' => '456-789-123',
                'meeting_password' => 'copywriting2024',
                'max_attendees' => 200,
                'required_plans' => ['cadet', 'challenger', 'hero', 'champion'],
                'status' => 'scheduled',
                'is_recorded' => true,
            ],
            [
                'title' => 'Q&A Session: Your Success Questions Answered',
                'description' => 'Bring your questions about business, investing, mindset, and success. This interactive Q&A session is your chance to get direct answers and personalized advice.',
                'scheduled_at' => now()->addDays(10)->setTime(19, 30), // 10 days from now at 7:30 PM
                'duration_minutes' => 60,
                'meeting_url' => 'https://zoom.us/j/789123456',
                'meeting_id' => '789-123-456',
                'meeting_password' => 'qa2024',
                'max_attendees' => 1000,
                'required_plans' => ['cadet', 'challenger', 'hero', 'champion'],
                'status' => 'scheduled',
                'is_recorded' => true,
            ],
            [
                'title' => 'Real Estate Investment Strategies',
                'description' => 'Learn advanced real estate investment strategies including rental properties, house flipping, commercial real estate, and REITs. Discover how to build wealth through real estate.',
                'scheduled_at' => now()->addDays(14)->setTime(17, 0), // 14 days from now at 5 PM
                'duration_minutes' => 105,
                'meeting_url' => 'https://zoom.us/j/321654987',
                'meeting_id' => '321-654-987',
                'meeting_password' => 'realestate2024',
                'max_attendees' => 250,
                'required_plans' => ['hero', 'champion'],
                'status' => 'scheduled',
                'is_recorded' => true,
            ],
            [
                'title' => 'Social Media Domination Workshop',
                'description' => 'Master social media marketing and build a massive following. Learn content creation strategies, engagement techniques, and monetization methods across all major platforms.',
                'scheduled_at' => now()->addDays(17)->setTime(20, 30), // 17 days from now at 8:30 PM
                'duration_minutes' => 90,
                'meeting_url' => 'https://zoom.us/j/654987321',
                'meeting_id' => '654-987-321',
                'meeting_password' => 'social2024',
                'max_attendees' => 400,
                'required_plans' => ['cadet', 'challenger', 'hero', 'champion'],
                'status' => 'scheduled',
                'is_recorded' => true,
            ],
            // Past completed call for demonstration
            [
                'title' => 'Mindset & Success Principles',
                'description' => 'A powerful session on developing the right mindset for success. Learn the mental frameworks and principles that separate winners from losers.',
                'scheduled_at' => now()->subDays(3)->setTime(19, 0), // 3 days ago
                'duration_minutes' => 80,
                'meeting_url' => 'https://zoom.us/j/111222333',
                'meeting_id' => '111-222-333',
                'meeting_password' => 'mindset2024',
                'max_attendees' => 600,
                'required_plans' => ['cadet', 'challenger', 'hero', 'champion'],
                'status' => 'completed',
                'is_recorded' => true,
                'recording_url' => 'https://zoom.us/rec/share/example-recording-url',
            ],
        ];

        foreach ($liveCalls as $callData) {
            LiveCall::create(array_merge($callData, [
                'mentor_id' => $mentor->id,
            ]));
        }

        $this->command->info('Live calls seeded successfully!');
    }
}
