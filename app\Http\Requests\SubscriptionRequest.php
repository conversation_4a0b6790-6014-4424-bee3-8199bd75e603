<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class SubscriptionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'subscription_plan_id' => ['required', 'exists:subscription_plans,id'],
            'billing_cycle' => ['required', Rule::in(['monthly', 'yearly'])],
            'payment_method' => ['required', Rule::in(['stripe', 'crypto'])],
            'payment_method_id' => ['required_if:payment_method,stripe', 'string'],
            'crypto_currency' => ['required_if:payment_method,crypto', Rule::in(['BTC', 'ETH', 'USDT', 'BNB'])],
            'crypto_address' => ['required_if:payment_method,crypto', 'string'],
            'coupon_code' => ['nullable', 'string', 'exists:coupons,code'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'subscription_plan_id.required' => 'Please select a subscription plan.',
            'subscription_plan_id.exists' => 'Selected subscription plan does not exist.',
            'billing_cycle.required' => 'Please select a billing cycle.',
            'billing_cycle.in' => 'Invalid billing cycle selected.',
            'payment_method.required' => 'Please select a payment method.',
            'payment_method.in' => 'Invalid payment method selected.',
            'payment_method_id.required_if' => 'Payment method is required for Stripe payments.',
            'crypto_currency.required_if' => 'Cryptocurrency is required for crypto payments.',
            'crypto_currency.in' => 'Invalid cryptocurrency selected.',
            'crypto_address.required_if' => 'Crypto address is required for crypto payments.',
            'coupon_code.exists' => 'Invalid coupon code.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'subscription_plan_id' => 'subscription plan',
            'billing_cycle' => 'billing cycle',
            'payment_method' => 'payment method',
            'payment_method_id' => 'payment method',
            'crypto_currency' => 'cryptocurrency',
            'crypto_address' => 'crypto address',
            'coupon_code' => 'coupon code',
        ];
    }
}
