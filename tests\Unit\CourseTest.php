<?php

namespace Tests\Unit;

use App\Models\Course;
use App\Models\User;
use App\Models\Lesson;
use App\Models\UserProgress;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CourseTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_can_create_a_course()
    {
        $mentor = User::factory()->create();
        
        $course = Course::factory()->create([
            'title' => 'Laravel Fundamentals',
            'description' => 'Learn Laravel from scratch',
            'mentor_id' => $mentor->id,
            'category' => 'Programming',
            'difficulty' => 'beginner',
            'price' => 99.99,
        ]);

        $this->assertDatabaseHas('courses', [
            'title' => 'Laravel Fundamentals',
            'description' => 'Learn Laravel from scratch',
            'mentor_id' => $mentor->id,
            'category' => 'Programming',
            'difficulty' => 'beginner',
            'price' => 99.99,
        ]);
    }

    /** @test */
    public function it_belongs_to_a_mentor()
    {
        $mentor = User::factory()->create(['first_name' => 'John', 'last_name' => 'Mentor']);
        $course = Course::factory()->create(['mentor_id' => $mentor->id]);

        $this->assertEquals('John Mentor', $course->mentor->name);
    }

    /** @test */
    public function it_can_have_many_lessons()
    {
        $course = Course::factory()->create();
        $lessons = Lesson::factory()->count(3)->create(['course_id' => $course->id]);

        $this->assertCount(3, $course->lessons);
        $this->assertInstanceOf(Lesson::class, $course->lessons->first());
    }

    /** @test */
    public function it_can_get_published_lessons_only()
    {
        $course = Course::factory()->create();
        
        Lesson::factory()->create(['course_id' => $course->id, 'is_published' => true]);
        Lesson::factory()->create(['course_id' => $course->id, 'is_published' => true]);
        Lesson::factory()->create(['course_id' => $course->id, 'is_published' => false]);

        $this->assertCount(2, $course->publishedLessons);
    }

    /** @test */
    public function it_can_calculate_total_duration()
    {
        $course = Course::factory()->create();
        
        Lesson::factory()->create(['course_id' => $course->id, 'duration_minutes' => 30]);
        Lesson::factory()->create(['course_id' => $course->id, 'duration_minutes' => 45]);
        Lesson::factory()->create(['course_id' => $course->id, 'duration_minutes' => 25]);

        $this->assertEquals(100, $course->getTotalDurationAttribute());
    }

    /** @test */
    public function it_can_get_enrollment_count()
    {
        $course = Course::factory()->create();
        $users = User::factory()->count(5)->create();

        foreach ($users as $user) {
            UserProgress::factory()->create([
                'user_id' => $user->id,
                'course_id' => $course->id,
            ]);
        }

        $this->assertEquals(5, $course->getEnrollmentCountAttribute());
    }

    /** @test */
    public function it_can_get_completion_rate()
    {
        $course = Course::factory()->create();
        $users = User::factory()->count(10)->create();

        // Enroll all users
        foreach ($users as $user) {
            UserProgress::factory()->create([
                'user_id' => $user->id,
                'course_id' => $course->id,
                'is_completed' => false,
            ]);
        }

        // Complete course for 3 users
        foreach ($users->take(3) as $user) {
            UserProgress::where('user_id', $user->id)
                ->where('course_id', $course->id)
                ->update(['is_completed' => true, 'completed_at' => now()]);
        }

        $this->assertEquals(30, $course->getCompletionRateAttribute()); // 3/10 = 30%
    }

    /** @test */
    public function it_can_get_average_rating()
    {
        $course = Course::factory()->create();
        
        // Create some ratings
        \App\Models\CourseRating::factory()->create(['course_id' => $course->id, 'rating' => 5]);
        \App\Models\CourseRating::factory()->create(['course_id' => $course->id, 'rating' => 4]);
        \App\Models\CourseRating::factory()->create(['course_id' => $course->id, 'rating' => 3]);

        $this->assertEquals(4.0, $course->getAverageRatingAttribute()); // (5+4+3)/3 = 4.0
    }

    /** @test */
    public function it_returns_zero_rating_when_no_ratings()
    {
        $course = Course::factory()->create();
        
        $this->assertEquals(0, $course->getAverageRatingAttribute());
    }

    /** @test */
    public function it_can_check_if_user_is_enrolled()
    {
        $course = Course::factory()->create();
        $user = User::factory()->create();
        $otherUser = User::factory()->create();

        UserProgress::factory()->create([
            'user_id' => $user->id,
            'course_id' => $course->id,
        ]);

        $this->assertTrue($course->isUserEnrolled($user));
        $this->assertFalse($course->isUserEnrolled($otherUser));
    }

    /** @test */
    public function it_can_check_if_user_completed_course()
    {
        $course = Course::factory()->create();
        $user = User::factory()->create();
        $otherUser = User::factory()->create();

        UserProgress::factory()->create([
            'user_id' => $user->id,
            'course_id' => $course->id,
            'is_completed' => true,
            'completed_at' => now(),
        ]);

        UserProgress::factory()->create([
            'user_id' => $otherUser->id,
            'course_id' => $course->id,
            'is_completed' => false,
        ]);

        $this->assertTrue($course->isCompletedByUser($user));
        $this->assertFalse($course->isCompletedByUser($otherUser));
    }

    /** @test */
    public function it_can_get_user_progress_percentage()
    {
        $course = Course::factory()->create();
        $user = User::factory()->create();
        
        // Create 4 lessons
        $lessons = Lesson::factory()->count(4)->create(['course_id' => $course->id]);

        // Complete 2 lessons
        foreach ($lessons->take(2) as $lesson) {
            UserProgress::factory()->create([
                'user_id' => $user->id,
                'course_id' => $course->id,
                'lesson_id' => $lesson->id,
                'is_completed' => true,
                'completed_at' => now(),
            ]);
        }

        $this->assertEquals(50, $course->getUserProgressPercentage($user)); // 2/4 = 50%
    }

    /** @test */
    public function it_returns_zero_progress_for_unenrolled_user()
    {
        $course = Course::factory()->create();
        $user = User::factory()->create();

        $this->assertEquals(0, $course->getUserProgressPercentage($user));
    }

    /** @test */
    public function it_can_scope_published_courses()
    {
        Course::factory()->create(['is_published' => true, 'title' => 'Published Course']);
        Course::factory()->create(['is_published' => false, 'title' => 'Draft Course']);

        $publishedCourses = Course::published()->get();

        $this->assertCount(1, $publishedCourses);
        $this->assertEquals('Published Course', $publishedCourses->first()->title);
    }

    /** @test */
    public function it_can_scope_featured_courses()
    {
        Course::factory()->create(['is_featured' => true, 'title' => 'Featured Course']);
        Course::factory()->create(['is_featured' => false, 'title' => 'Regular Course']);

        $featuredCourses = Course::featured()->get();

        $this->assertCount(1, $featuredCourses);
        $this->assertEquals('Featured Course', $featuredCourses->first()->title);
    }

    /** @test */
    public function it_can_scope_by_category()
    {
        Course::factory()->create(['category' => 'Programming', 'title' => 'Laravel Course']);
        Course::factory()->create(['category' => 'Design', 'title' => 'Photoshop Course']);

        $programmingCourses = Course::byCategory('Programming')->get();

        $this->assertCount(1, $programmingCourses);
        $this->assertEquals('Laravel Course', $programmingCourses->first()->title);
    }

    /** @test */
    public function it_can_scope_by_difficulty()
    {
        Course::factory()->create(['difficulty' => 'beginner', 'title' => 'Beginner Course']);
        Course::factory()->create(['difficulty' => 'advanced', 'title' => 'Advanced Course']);

        $beginnerCourses = Course::byDifficulty('beginner')->get();

        $this->assertCount(1, $beginnerCourses);
        $this->assertEquals('Beginner Course', $beginnerCourses->first()->title);
    }

    /** @test */
    public function it_can_search_courses()
    {
        Course::factory()->create(['title' => 'Laravel Fundamentals', 'description' => 'Learn Laravel basics']);
        Course::factory()->create(['title' => 'Vue.js Mastery', 'description' => 'Master Vue.js framework']);

        $searchResults = Course::search('Laravel')->get();

        $this->assertCount(1, $searchResults);
        $this->assertEquals('Laravel Fundamentals', $searchResults->first()->title);
    }

    /** @test */
    public function it_can_get_next_lesson_for_user()
    {
        $course = Course::factory()->create();
        $user = User::factory()->create();
        
        $lesson1 = Lesson::factory()->create(['course_id' => $course->id, 'order' => 1]);
        $lesson2 = Lesson::factory()->create(['course_id' => $course->id, 'order' => 2]);
        $lesson3 = Lesson::factory()->create(['course_id' => $course->id, 'order' => 3]);

        // Complete first lesson
        UserProgress::factory()->create([
            'user_id' => $user->id,
            'course_id' => $course->id,
            'lesson_id' => $lesson1->id,
            'is_completed' => true,
        ]);

        $nextLesson = $course->getNextLessonForUser($user);

        $this->assertEquals($lesson2->id, $nextLesson->id);
    }

    /** @test */
    public function it_returns_first_lesson_for_new_user()
    {
        $course = Course::factory()->create();
        $user = User::factory()->create();
        
        $lesson1 = Lesson::factory()->create(['course_id' => $course->id, 'order' => 1]);
        $lesson2 = Lesson::factory()->create(['course_id' => $course->id, 'order' => 2]);

        $nextLesson = $course->getNextLessonForUser($user);

        $this->assertEquals($lesson1->id, $nextLesson->id);
    }

    /** @test */
    public function it_can_check_required_subscription_plans()
    {
        $course = Course::factory()->create([
            'required_plans' => ['Premium', 'Pro'],
        ]);

        $this->assertTrue($course->requiresSubscriptionPlan('Premium'));
        $this->assertTrue($course->requiresSubscriptionPlan('Pro'));
        $this->assertFalse($course->requiresSubscriptionPlan('Basic'));
    }

    /** @test */
    public function it_allows_access_when_no_plans_required()
    {
        $course = Course::factory()->create([
            'required_plans' => [],
        ]);

        $this->assertFalse($course->requiresSubscriptionPlan('Premium'));
    }
}
