<?php

return [
    /*
    |--------------------------------------------------------------------------
    | LMS Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration options for The Real World LMS
    |
    */

    'name' => env('LMS_NAME', 'The Real World'),
    'tagline' => env('LMS_TAGLINE', 'Escape The Matrix'),
    'description' => env('LMS_DESCRIPTION', 'The most exclusive online university in the world'),

    /*
    |--------------------------------------------------------------------------
    | Course Settings
    |--------------------------------------------------------------------------
    */
    'courses' => [
        'max_file_size' => env('COURSE_MAX_FILE_SIZE', 100), // MB
        'allowed_video_formats' => ['mp4', 'webm', 'ogg'],
        'allowed_document_formats' => ['pdf', 'doc', 'docx', 'ppt', 'pptx'],
        'default_thumbnail' => 'images/default-course-thumbnail.jpg',
        'auto_enroll_free_courses' => env('AUTO_ENROLL_FREE_COURSES', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Subscription Settings
    |--------------------------------------------------------------------------
    */
    'subscriptions' => [
        'trial_days' => env('SUBSCRIPTION_TRIAL_DAYS', 7),
        'grace_period_days' => env('SUBSCRIPTION_GRACE_PERIOD_DAYS', 3),
        'allow_downgrades' => env('ALLOW_SUBSCRIPTION_DOWNGRADES', true),
        'prorate_upgrades' => env('PRORATE_SUBSCRIPTION_UPGRADES', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Payment Settings
    |--------------------------------------------------------------------------
    */
    'payments' => [
        'currencies' => [
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
            'BTC' => '₿',
            'ETH' => 'Ξ',
        ],
        'default_currency' => env('DEFAULT_CURRENCY', 'USD'),
        'crypto_currencies' => ['BTC', 'ETH', 'USDT', 'BNB'],
        'stripe_webhook_tolerance' => env('STRIPE_WEBHOOK_TOLERANCE', 300),
    ],

    /*
    |--------------------------------------------------------------------------
    | Live Calls Settings
    |--------------------------------------------------------------------------
    */
    'live_calls' => [
        'max_attendees' => env('LIVE_CALL_MAX_ATTENDEES', 1000),
        'reminder_hours' => env('LIVE_CALL_REMINDER_HOURS', 24),
        'recording_enabled' => env('LIVE_CALL_RECORDING_ENABLED', true),
        'chat_enabled' => env('LIVE_CALL_CHAT_ENABLED', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Community Settings
    |--------------------------------------------------------------------------
    */
    'community' => [
        'max_post_length' => env('COMMUNITY_MAX_POST_LENGTH', 5000),
        'max_comment_length' => env('COMMUNITY_MAX_COMMENT_LENGTH', 1000),
        'allow_anonymous_posts' => env('COMMUNITY_ALLOW_ANONYMOUS', false),
        'moderation_enabled' => env('COMMUNITY_MODERATION_ENABLED', true),
        'auto_approve_posts' => env('COMMUNITY_AUTO_APPROVE_POSTS', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Notification Settings
    |--------------------------------------------------------------------------
    */
    'notifications' => [
        'channels' => ['mail', 'database', 'broadcast'],
        'queue_notifications' => env('QUEUE_NOTIFICATIONS', true),
        'email_notifications_enabled' => env('EMAIL_NOTIFICATIONS_ENABLED', true),
        'push_notifications_enabled' => env('PUSH_NOTIFICATIONS_ENABLED', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Settings
    |--------------------------------------------------------------------------
    */
    'security' => [
        'two_factor_enabled' => env('TWO_FACTOR_ENABLED', true),
        'password_min_length' => env('PASSWORD_MIN_LENGTH', 8),
        'session_timeout' => env('SESSION_TIMEOUT', 120), // minutes
        'max_login_attempts' => env('MAX_LOGIN_ATTEMPTS', 5),
        'lockout_duration' => env('LOCKOUT_DURATION', 60), // minutes
    ],

    /*
    |--------------------------------------------------------------------------
    | File Storage Settings
    |--------------------------------------------------------------------------
    */
    'storage' => [
        'disk' => env('LMS_STORAGE_DISK', 'public'),
        'avatars_path' => 'avatars',
        'course_thumbnails_path' => 'course-thumbnails',
        'lesson_videos_path' => 'lesson-videos',
        'resources_path' => 'resources',
        'certificates_path' => 'certificates',
    ],

    /*
    |--------------------------------------------------------------------------
    | Analytics Settings
    |--------------------------------------------------------------------------
    */
    'analytics' => [
        'enabled' => env('ANALYTICS_ENABLED', true),
        'track_user_activity' => env('TRACK_USER_ACTIVITY', true),
        'track_course_progress' => env('TRACK_COURSE_PROGRESS', true),
        'track_video_watch_time' => env('TRACK_VIDEO_WATCH_TIME', true),
        'retention_days' => env('ANALYTICS_RETENTION_DAYS', 365),
    ],

    /*
    |--------------------------------------------------------------------------
    | Cache Settings
    |--------------------------------------------------------------------------
    */
    'cache' => [
        'course_cache_ttl' => env('COURSE_CACHE_TTL', 3600), // seconds
        'user_progress_cache_ttl' => env('USER_PROGRESS_CACHE_TTL', 1800), // seconds
        'subscription_cache_ttl' => env('SUBSCRIPTION_CACHE_TTL', 7200), // seconds
    ],

    /*
    |--------------------------------------------------------------------------
    | API Settings
    |--------------------------------------------------------------------------
    */
    'api' => [
        'rate_limit' => env('API_RATE_LIMIT', 60), // requests per minute
        'pagination_limit' => env('API_PAGINATION_LIMIT', 50),
        'version' => env('API_VERSION', 'v1'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Social Media Links
    |--------------------------------------------------------------------------
    */
    'social' => [
        'twitter' => env('SOCIAL_TWITTER', 'https://twitter.com/therealworld'),
        'instagram' => env('SOCIAL_INSTAGRAM', 'https://instagram.com/therealworld'),
        'youtube' => env('SOCIAL_YOUTUBE', 'https://youtube.com/therealworld'),
        'telegram' => env('SOCIAL_TELEGRAM', 'https://t.me/therealworld'),
        'discord' => env('SOCIAL_DISCORD', 'https://discord.gg/therealworld'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Feature Flags
    |--------------------------------------------------------------------------
    */
    'features' => [
        'certificates_enabled' => env('CERTIFICATES_ENABLED', true),
        'gamification_enabled' => env('GAMIFICATION_ENABLED', false),
        'affiliate_program_enabled' => env('AFFILIATE_PROGRAM_ENABLED', false),
        'mobile_app_enabled' => env('MOBILE_APP_ENABLED', true),
        'offline_mode_enabled' => env('OFFLINE_MODE_ENABLED', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Email Templates
    |--------------------------------------------------------------------------
    */
    'email' => [
        'from_name' => env('MAIL_FROM_NAME', 'The Real World'),
        'from_address' => env('MAIL_FROM_ADDRESS', '<EMAIL>'),
        'support_email' => env('SUPPORT_EMAIL', '<EMAIL>'),
        'logo_url' => env('EMAIL_LOGO_URL', 'https://therealworld.com/images/logo.png'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Maintenance Mode
    |--------------------------------------------------------------------------
    */
    'maintenance' => [
        'enabled' => env('MAINTENANCE_MODE_ENABLED', false),
        'message' => env('MAINTENANCE_MESSAGE', 'We are currently performing scheduled maintenance. Please check back soon.'),
        'allowed_ips' => explode(',', env('MAINTENANCE_ALLOWED_IPS', '')),
    ],

    /*
    |--------------------------------------------------------------------------
    | Backup Settings
    |--------------------------------------------------------------------------
    */
    'backup' => [
        'enabled' => env('BACKUP_ENABLED', true),
        'frequency' => env('BACKUP_FREQUENCY', 'daily'), // daily, weekly, monthly
        'retention_days' => env('BACKUP_RETENTION_DAYS', 30),
        'include_uploads' => env('BACKUP_INCLUDE_UPLOADS', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Settings
    |--------------------------------------------------------------------------
    */
    'performance' => [
        'enable_query_caching' => env('ENABLE_QUERY_CACHING', true),
        'enable_view_caching' => env('ENABLE_VIEW_CACHING', true),
        'enable_route_caching' => env('ENABLE_ROUTE_CACHING', true),
        'enable_config_caching' => env('ENABLE_CONFIG_CACHING', true),
        'lazy_load_images' => env('LAZY_LOAD_IMAGES', true),
        'compress_responses' => env('COMPRESS_RESPONSES', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Monitoring Settings
    |--------------------------------------------------------------------------
    */
    'monitoring' => [
        'enabled' => env('MONITORING_ENABLED', true),
        'log_slow_queries' => env('LOG_SLOW_QUERIES', true),
        'slow_query_threshold' => env('SLOW_QUERY_THRESHOLD', 1000), // milliseconds
        'error_reporting_enabled' => env('ERROR_REPORTING_ENABLED', true),
        'uptime_monitoring_enabled' => env('UPTIME_MONITORING_ENABLED', true),
    ],
];
