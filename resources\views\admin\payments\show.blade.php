@extends('layouts.admin')

@section('title', '- Payment Details')

@section('content')
<div class="p-6">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex justify-between items-center">
            <div>
                <nav class="flex" aria-label="Breadcrumb">
                    <ol class="flex items-center space-x-4">
                        <li>
                            <a href="{{ route('admin.payments.index') }}" class="text-gray-400 hover:text-gray-500">
                                Payments
                            </a>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <svg class="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                                </svg>
                                <span class="ml-4 text-sm font-medium text-gray-500">Payment #{{ $payment->id }}</span>
                            </div>
                        </li>
                    </ol>
                </nav>
                <h1 class="mt-2 text-2xl font-bold text-gray-900">Payment Details</h1>
            </div>
            
            @if($payment->status === 'completed')
                <div class="flex space-x-3">
                    <button onclick="openRefundModal()" 
                            class="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md transition duration-150">
                        Process Refund
                    </button>
                </div>
            @endif
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Payment Information -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Payment Information</h3>
                </div>
                <div class="px-6 py-4">
                    <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Payment ID</dt>
                            <dd class="mt-1 text-sm text-gray-900 font-mono">#{{ $payment->id }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Transaction ID</dt>
                            <dd class="mt-1 text-sm text-gray-900 font-mono">{{ $payment->transaction_id }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Amount</dt>
                            <dd class="mt-1 text-sm text-gray-900 font-semibold">${{ number_format($payment->amount, 2) }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Currency</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ strtoupper($payment->currency) }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Payment Method</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ ucfirst($payment->payment_method) }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Status</dt>
                            <dd class="mt-1">
                                @if($payment->status === 'completed')
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        Completed
                                    </span>
                                @elseif($payment->status === 'failed')
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        Failed
                                    </span>
                                @elseif($payment->status === 'refunded')
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        Refunded
                                    </span>
                                @else
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        {{ ucfirst($payment->status) }}
                                    </span>
                                @endif
                            </dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Created</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $payment->created_at->format('M d, Y \a\t g:i A') }}</dd>
                        </div>
                        @if($payment->paid_at)
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Paid At</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $payment->paid_at->format('M d, Y \a\t g:i A') }}</dd>
                        </div>
                        @endif
                        @if($payment->failed_at)
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Failed At</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $payment->failed_at->format('M d, Y \a\t g:i A') }}</dd>
                        </div>
                        @endif
                        @if($payment->failure_reason)
                        <div class="sm:col-span-2">
                            <dt class="text-sm font-medium text-gray-500">Failure Reason</dt>
                            <dd class="mt-1 text-sm text-red-600">{{ $payment->failure_reason }}</dd>
                        </div>
                        @endif
                    </dl>
                </div>
            </div>

            <!-- Payment Method Details -->
            @if($payment->payment_method === 'stripe')
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Stripe Details</h3>
                    </div>
                    <div class="px-6 py-4">
                        <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                            @if($payment->stripe_payment_intent_id)
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Payment Intent ID</dt>
                                <dd class="mt-1 text-sm text-gray-900 font-mono">{{ $payment->stripe_payment_intent_id }}</dd>
                            </div>
                            @endif
                            @if($payment->stripe_charge_id)
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Charge ID</dt>
                                <dd class="mt-1 text-sm text-gray-900 font-mono">{{ $payment->stripe_charge_id }}</dd>
                            </div>
                            @endif
                        </dl>
                    </div>
                </div>
            @elseif($payment->payment_method === 'crypto')
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Cryptocurrency Details</h3>
                    </div>
                    <div class="px-6 py-4">
                        <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                            @if($payment->crypto_currency)
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Currency</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ strtoupper($payment->crypto_currency) }}</dd>
                            </div>
                            @endif
                            @if($payment->crypto_amount)
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Amount</dt>
                                <dd class="mt-1 text-sm text-gray-900 font-mono">{{ $payment->crypto_amount }} {{ strtoupper($payment->crypto_currency) }}</dd>
                            </div>
                            @endif
                            @if($payment->crypto_address)
                            <div class="sm:col-span-2">
                                <dt class="text-sm font-medium text-gray-500">Address</dt>
                                <dd class="mt-1 text-sm text-gray-900 font-mono break-all">{{ $payment->crypto_address }}</dd>
                            </div>
                            @endif
                            @if($payment->blockchain_tx_hash)
                            <div class="sm:col-span-2">
                                <dt class="text-sm font-medium text-gray-500">Transaction Hash</dt>
                                <dd class="mt-1 text-sm text-gray-900 font-mono break-all">{{ $payment->blockchain_tx_hash }}</dd>
                            </div>
                            @endif
                        </dl>
                    </div>
                </div>
            @endif
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- User Information -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Customer</h3>
                </div>
                <div class="px-6 py-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 h-12 w-12">
                            <div class="h-12 w-12 rounded-full bg-gray-300 flex items-center justify-center">
                                <span class="text-lg font-medium text-gray-700">
                                    {{ substr($payment->user->name, 0, 2) }}
                                </span>
                            </div>
                        </div>
                        <div class="ml-4">
                            <div class="text-sm font-medium text-gray-900">
                                {{ $payment->user->name }}
                            </div>
                            <div class="text-sm text-gray-500">
                                {{ $payment->user->email }}
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-4 pt-4 border-t border-gray-200">
                        <dl class="space-y-3">
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Member Since</dt>
                                <dd class="text-sm text-gray-900">{{ $payment->user->created_at->format('M d, Y') }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Status</dt>
                                <dd class="text-sm text-gray-900">
                                    @if($payment->user->is_active)
                                        <span class="text-green-600">Active</span>
                                    @else
                                        <span class="text-red-600">Inactive</span>
                                    @endif
                                </dd>
                            </div>
                        </dl>
                        
                        <div class="mt-4">
                            <a href="{{ route('admin.users.show', $payment->user) }}" 
                               class="text-blue-600 hover:text-blue-900 text-sm font-medium">
                                View User Profile →
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Purchase Information -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Purchase Details</h3>
                </div>
                <div class="px-6 py-4">
                    @if($payment->subscription_id)
                        <div class="space-y-3">
                            <div>
                                <h4 class="text-sm font-medium text-gray-900">Subscription Payment</h4>
                                <p class="text-sm text-gray-600">{{ $payment->subscription->subscriptionPlan->name }}</p>
                            </div>
                            
                            <div class="pt-3 border-t border-gray-200">
                                <a href="{{ route('admin.subscriptions.show', $payment->subscription) }}" 
                                   class="text-blue-600 hover:text-blue-900 text-sm font-medium">
                                    View Subscription →
                                </a>
                            </div>
                        </div>
                    @elseif($payment->course_id)
                        <div class="space-y-3">
                            <div>
                                <h4 class="text-sm font-medium text-gray-900">Course Purchase</h4>
                                <p class="text-sm text-gray-600">{{ $payment->course->title }}</p>
                            </div>
                            
                            <div class="pt-3 border-t border-gray-200">
                                <a href="{{ route('admin.courses.show', $payment->course) }}" 
                                   class="text-blue-600 hover:text-blue-900 text-sm font-medium">
                                    View Course →
                                </a>
                            </div>
                        </div>
                    @else
                        <div class="text-sm text-gray-500">
                            No specific item associated with this payment.
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Refund Modal -->
@if($payment->status === 'completed')
<div id="refundModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Process Refund</h3>
            <form method="POST" action="{{ route('admin.payments.refund', $payment) }}">
                @csrf
                <div class="mb-4">
                    <label for="refund_amount" class="block text-sm font-medium text-gray-700 mb-2">Refund Amount</label>
                    <input type="number" step="0.01" name="refund_amount" id="refund_amount" 
                           max="{{ $payment->amount }}" value="{{ $payment->amount }}"
                           class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                </div>
                <div class="mb-4">
                    <label for="reason" class="block text-sm font-medium text-gray-700 mb-2">Reason</label>
                    <textarea name="reason" id="reason" rows="3" 
                              class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                              placeholder="Reason for refund..."></textarea>
                </div>
                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeRefundModal()" 
                            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-medium py-2 px-4 rounded-md">
                        Cancel
                    </button>
                    <button type="submit" 
                            class="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md">
                        Process Refund
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function openRefundModal() {
    document.getElementById('refundModal').classList.remove('hidden');
}

function closeRefundModal() {
    document.getElementById('refundModal').classList.add('hidden');
}
</script>
@endif
@endsection
