<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to The Real World</title>
    <style>
        /* Reset styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #333333;
            background-color: #f8f9fc;
        }
        
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .email-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }
        
        .logo {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .header-subtitle {
            font-size: 16px;
            opacity: 0.9;
        }
        
        .email-body {
            padding: 40px 30px;
        }
        
        .welcome-message {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .welcome-title {
            font-size: 24px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 15px;
        }
        
        .welcome-text {
            font-size: 16px;
            color: #4a5568;
            line-height: 1.6;
        }
        
        .user-info {
            background: #f7fafc;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .user-info h3 {
            color: #2d3748;
            margin-bottom: 15px;
            font-size: 18px;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .info-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        .info-label {
            font-weight: 600;
            color: #4a5568;
        }
        
        .info-value {
            color: #2d3748;
        }
        
        .next-steps {
            margin-bottom: 40px;
        }
        
        .next-steps h3 {
            color: #2d3748;
            margin-bottom: 20px;
            font-size: 20px;
        }
        
        .steps-list {
            list-style: none;
            counter-reset: step-counter;
        }
        
        .step-item {
            counter-increment: step-counter;
            margin-bottom: 20px;
            display: flex;
            align-items: flex-start;
        }
        
        .step-number {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin-right: 15px;
            flex-shrink: 0;
        }
        
        .step-number::before {
            content: counter(step-counter);
        }
        
        .step-content h4 {
            color: #2d3748;
            margin-bottom: 5px;
            font-size: 16px;
        }
        
        .step-content p {
            color: #4a5568;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .cta-section {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            margin: 10px;
            transition: transform 0.2s ease;
        }
        
        .cta-button:hover {
            transform: translateY(-2px);
            text-decoration: none;
            color: white;
        }
        
        .cta-button.secondary {
            background: transparent;
            border: 2px solid #667eea;
            color: #667eea;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .feature-card {
            background: #f7fafc;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }
        
        .feature-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-size: 20px;
            color: white;
        }
        
        .feature-title {
            font-size: 16px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 10px;
        }
        
        .feature-description {
            font-size: 14px;
            color: #4a5568;
            line-height: 1.5;
        }
        
        .support-section {
            background: #f7fafc;
            border-radius: 8px;
            padding: 25px;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .support-section h3 {
            color: #2d3748;
            margin-bottom: 10px;
            font-size: 18px;
        }
        
        .support-section p {
            color: #4a5568;
            margin-bottom: 20px;
        }
        
        .support-links {
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }
        
        .support-link {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            font-size: 14px;
        }
        
        .support-link:hover {
            text-decoration: underline;
        }
        
        .email-footer {
            background: #2d3748;
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .footer-logo {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 15px;
        }
        
        .footer-text {
            font-size: 14px;
            opacity: 0.8;
            margin-bottom: 20px;
            line-height: 1.5;
        }
        
        .social-links {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .social-link {
            display: inline-block;
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            color: white;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
        }
        
        .social-link:hover {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
        }
        
        .unsubscribe {
            font-size: 12px;
            opacity: 0.6;
        }
        
        .unsubscribe a {
            color: white;
            text-decoration: underline;
        }
        
        /* Mobile responsiveness */
        @media (max-width: 600px) {
            .email-container {
                margin: 0;
                box-shadow: none;
            }
            
            .email-header,
            .email-body,
            .email-footer {
                padding: 20px;
            }
            
            .welcome-title {
                font-size: 20px;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
            
            .cta-button {
                display: block;
                margin: 10px 0;
            }
            
            .support-links {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="email-header">
            <div class="logo">The Real World</div>
            <div class="header-subtitle">Your journey to success starts here</div>
        </div>
        
        <!-- Body -->
        <div class="email-body">
            <!-- Welcome Message -->
            <div class="welcome-message">
                <h1 class="welcome-title">Welcome to The Real World, {{ $user->name }}!</h1>
                <p class="welcome-text">
                    You've just joined thousands of ambitious individuals who are transforming their lives through real-world education and mentorship.
                </p>
            </div>
            
            <!-- User Info -->
            <div class="user-info">
                <h3>Your Account Details</h3>
                <div class="info-item">
                    <span class="info-label">Name:</span>
                    <span class="info-value">{{ $user->name }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Email:</span>
                    <span class="info-value">{{ $user->email }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Member Since:</span>
                    <span class="info-value">{{ $user->created_at->format('F d, Y') }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Account Status:</span>
                    <span class="info-value">Active</span>
                </div>
            </div>
            
            <!-- Next Steps -->
            <div class="next-steps">
                <h3>Get Started in 3 Easy Steps</h3>
                <ol class="steps-list">
                    <li class="step-item">
                        <div class="step-number"></div>
                        <div class="step-content">
                            <h4>Complete Your Profile</h4>
                            <p>Add your information and preferences to get personalized course recommendations and connect with like-minded individuals.</p>
                        </div>
                    </li>
                    <li class="step-item">
                        <div class="step-number"></div>
                        <div class="step-content">
                            <h4>Explore Our Courses</h4>
                            <p>Browse our extensive library of courses taught by successful entrepreneurs and industry experts.</p>
                        </div>
                    </li>
                    <li class="step-item">
                        <div class="step-number"></div>
                        <div class="step-content">
                            <h4>Join the Community</h4>
                            <p>Connect with other students, participate in discussions, and attend live calls with mentors.</p>
                        </div>
                    </li>
                </ol>
            </div>
            
            <!-- CTA Buttons -->
            <div class="cta-section">
                <a href="{{ route('dashboard') }}" class="cta-button">
                    Go to Dashboard
                </a>
                <a href="{{ route('courses.index') }}" class="cta-button secondary">
                    Browse Courses
                </a>
            </div>
            
            <!-- Features -->
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">📚</div>
                    <div class="feature-title">Premium Courses</div>
                    <div class="feature-description">Access to exclusive courses taught by successful entrepreneurs</div>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🎥</div>
                    <div class="feature-title">Live Mentorship</div>
                    <div class="feature-description">Join live calls and get direct guidance from industry experts</div>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">👥</div>
                    <div class="feature-title">Private Community</div>
                    <div class="feature-description">Network with ambitious individuals from around the world</div>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🏆</div>
                    <div class="feature-title">Certificates</div>
                    <div class="feature-description">Earn certificates to showcase your achievements</div>
                </div>
            </div>
            
            <!-- Support Section -->
            <div class="support-section">
                <h3>Need Help Getting Started?</h3>
                <p>Our support team is here to help you make the most of your membership.</p>
                <div class="support-links">
                    <a href="{{ route('faq') }}" class="support-link">FAQ</a>
                    <a href="{{ route('contact') }}" class="support-link">Contact Support</a>
                    <a href="{{ route('community.guidelines') }}" class="support-link">Community Guidelines</a>
                </div>
            </div>
        </div>
        
        <!-- Footer -->
        <div class="email-footer">
            <div class="footer-logo">The Real World</div>
            <div class="footer-text">
                Empowering individuals to achieve financial freedom through real-world education and mentorship.
            </div>
            
            <div class="social-links">
                <a href="#" class="social-link">📘</a>
                <a href="#" class="social-link">🐦</a>
                <a href="#" class="social-link">📷</a>
                <a href="#" class="social-link">💼</a>
            </div>
            
            <div class="unsubscribe">
                If you no longer wish to receive these emails, you can 
                <a href="{{ route('unsubscribe', ['token' => $user->unsubscribe_token]) }}">unsubscribe here</a>.
            </div>
        </div>
    </div>
</body>
</html>
