@extends('layouts.admin')

@section('title', 'Analytics')

@section('content')
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Analytics</h1>
        <div class="d-flex gap-2">
            <div class="dropdown">
                <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" data-toggle="dropdown">
                    <i class="fas fa-calendar"></i> {{ ucfirst($period) }}
                </button>
                <div class="dropdown-menu">
                    <a class="dropdown-item" href="{{ route('admin.analytics.index', ['period' => 'today']) }}">Today</a>
                    <a class="dropdown-item" href="{{ route('admin.analytics.index', ['period' => 'week']) }}">This Week</a>
                    <a class="dropdown-item" href="{{ route('admin.analytics.index', ['period' => 'month']) }}">This Month</a>
                    <a class="dropdown-item" href="{{ route('admin.analytics.index', ['period' => 'year']) }}">This Year</a>
                </div>
            </div>
            <a href="{{ route('admin.analytics.export') }}" class="btn btn-sm btn-success shadow-sm">
                <i class="fas fa-download fa-sm text-white-50"></i> Export Data
            </a>
        </div>
    </div>

    <!-- Overview Cards -->
    <div class="row">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Page Views</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($analytics['page_views']) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-eye fa-2x text-gray-300"></i>
                        </div>
                    </div>
                    <div class="row no-gutters align-items-center mt-2">
                        <div class="col">
                            <small class="text-{{ $analytics['page_views_change'] >= 0 ? 'success' : 'danger' }}">
                                <i class="fas fa-arrow-{{ $analytics['page_views_change'] >= 0 ? 'up' : 'down' }}"></i> 
                                {{ abs($analytics['page_views_change']) }}% from last period
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">New Users</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($analytics['new_users']) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-plus fa-2x text-gray-300"></i>
                        </div>
                    </div>
                    <div class="row no-gutters align-items-center mt-2">
                        <div class="col">
                            <small class="text-{{ $analytics['new_users_change'] >= 0 ? 'success' : 'danger' }}">
                                <i class="fas fa-arrow-{{ $analytics['new_users_change'] >= 0 ? 'up' : 'down' }}"></i> 
                                {{ abs($analytics['new_users_change']) }}% from last period
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Course Enrollments</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($analytics['enrollments']) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-graduation-cap fa-2x text-gray-300"></i>
                        </div>
                    </div>
                    <div class="row no-gutters align-items-center mt-2">
                        <div class="col">
                            <small class="text-{{ $analytics['enrollments_change'] >= 0 ? 'success' : 'danger' }}">
                                <i class="fas fa-arrow-{{ $analytics['enrollments_change'] >= 0 ? 'up' : 'down' }}"></i> 
                                {{ abs($analytics['enrollments_change']) }}% from last period
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Revenue</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">${{ number_format($analytics['revenue'], 2) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                    <div class="row no-gutters align-items-center mt-2">
                        <div class="col">
                            <small class="text-{{ $analytics['revenue_change'] >= 0 ? 'success' : 'danger' }}">
                                <i class="fas fa-arrow-{{ $analytics['revenue_change'] >= 0 ? 'up' : 'down' }}"></i> 
                                {{ abs($analytics['revenue_change']) }}% from last period
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row">
        <!-- Traffic Chart -->
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Traffic Overview</h6>
                    <div class="dropdown no-arrow">
                        <a class="dropdown-toggle" href="#" role="button" data-toggle="dropdown">
                            <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right shadow">
                            <div class="dropdown-header">Chart Type:</div>
                            <a class="dropdown-item" href="#" onclick="changeChartType('line')">Line Chart</a>
                            <a class="dropdown-item" href="#" onclick="changeChartType('bar')">Bar Chart</a>
                            <a class="dropdown-item" href="#" onclick="changeChartType('area')">Area Chart</a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-area">
                        <canvas id="trafficChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- User Sources -->
        <div class="col-xl-4 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Traffic Sources</h6>
                </div>
                <div class="card-body">
                    <div class="chart-pie pt-4 pb-2">
                        <canvas id="sourcesChart"></canvas>
                    </div>
                    <div class="mt-4 text-center small">
                        <span class="mr-2">
                            <i class="fas fa-circle text-primary"></i> Direct
                        </span>
                        <span class="mr-2">
                            <i class="fas fa-circle text-success"></i> Social Media
                        </span>
                        <span class="mr-2">
                            <i class="fas fa-circle text-info"></i> Search Engines
                        </span>
                        <span class="mr-2">
                            <i class="fas fa-circle text-warning"></i> Referrals
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Analytics -->
    <div class="row">
        <!-- Top Pages -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Top Pages</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-borderless">
                            <thead>
                                <tr>
                                    <th>Page</th>
                                    <th>Views</th>
                                    <th>Unique</th>
                                    <th>Bounce Rate</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($topPages as $page)
                                <tr>
                                    <td>
                                        <div class="page-info">
                                            <div class="page-title">{{ $page['title'] }}</div>
                                            <small class="text-muted">{{ $page['url'] }}</small>
                                        </div>
                                    </td>
                                    <td>{{ number_format($page['views']) }}</td>
                                    <td>{{ number_format($page['unique_views']) }}</td>
                                    <td>
                                        <span class="badge badge-{{ $page['bounce_rate'] > 70 ? 'danger' : ($page['bounce_rate'] > 50 ? 'warning' : 'success') }}">
                                            {{ $page['bounce_rate'] }}%
                                        </span>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- User Behavior -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">User Behavior</h6>
                </div>
                <div class="card-body">
                    <div class="behavior-metrics">
                        <div class="metric-item">
                            <div class="metric-label">Average Session Duration</div>
                            <div class="metric-value">{{ $userBehavior['avg_session_duration'] }}</div>
                            <div class="metric-change text-{{ $userBehavior['session_duration_change'] >= 0 ? 'success' : 'danger' }}">
                                <i class="fas fa-arrow-{{ $userBehavior['session_duration_change'] >= 0 ? 'up' : 'down' }}"></i>
                                {{ abs($userBehavior['session_duration_change']) }}%
                            </div>
                        </div>
                        
                        <div class="metric-item">
                            <div class="metric-label">Pages per Session</div>
                            <div class="metric-value">{{ number_format($userBehavior['pages_per_session'], 1) }}</div>
                            <div class="metric-change text-{{ $userBehavior['pages_per_session_change'] >= 0 ? 'success' : 'danger' }}">
                                <i class="fas fa-arrow-{{ $userBehavior['pages_per_session_change'] >= 0 ? 'up' : 'down' }}"></i>
                                {{ abs($userBehavior['pages_per_session_change']) }}%
                            </div>
                        </div>
                        
                        <div class="metric-item">
                            <div class="metric-label">Bounce Rate</div>
                            <div class="metric-value">{{ $userBehavior['bounce_rate'] }}%</div>
                            <div class="metric-change text-{{ $userBehavior['bounce_rate_change'] <= 0 ? 'success' : 'danger' }}">
                                <i class="fas fa-arrow-{{ $userBehavior['bounce_rate_change'] <= 0 ? 'down' : 'up' }}"></i>
                                {{ abs($userBehavior['bounce_rate_change']) }}%
                            </div>
                        </div>
                        
                        <div class="metric-item">
                            <div class="metric-label">Return Visitor Rate</div>
                            <div class="metric-value">{{ $userBehavior['return_visitor_rate'] }}%</div>
                            <div class="metric-change text-{{ $userBehavior['return_visitor_rate_change'] >= 0 ? 'success' : 'danger' }}">
                                <i class="fas fa-arrow-{{ $userBehavior['return_visitor_rate_change'] >= 0 ? 'up' : 'down' }}"></i>
                                {{ abs($userBehavior['return_visitor_rate_change']) }}%
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Course Performance -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Course Performance</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="coursePerformanceTable">
                            <thead>
                                <tr>
                                    <th>Course</th>
                                    <th>Enrollments</th>
                                    <th>Completion Rate</th>
                                    <th>Average Rating</th>
                                    <th>Revenue</th>
                                    <th>Engagement</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($coursePerformance as $course)
                                <tr>
                                    <td>
                                        <div class="course-info">
                                            <img src="{{ $course->thumbnail ? asset('storage/' . $course->thumbnail) : asset('images/default-course-thumbnail.jpg') }}" 
                                                 alt="{{ $course->title }}" class="course-thumbnail">
                                            <div class="course-details">
                                                <div class="course-title">{{ $course->title }}</div>
                                                <small class="text-muted">{{ $course->mentor->name }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="font-weight-bold">{{ number_format($course->enrollments_count) }}</span>
                                        <div class="enrollment-trend">
                                            <small class="text-{{ $course->enrollment_trend >= 0 ? 'success' : 'danger' }}">
                                                <i class="fas fa-arrow-{{ $course->enrollment_trend >= 0 ? 'up' : 'down' }}"></i>
                                                {{ abs($course->enrollment_trend) }}%
                                            </small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="progress mb-1">
                                            <div class="progress-bar" role="progressbar" 
                                                 style="width: {{ $course->completion_rate }}%"
                                                 aria-valuenow="{{ $course->completion_rate }}" 
                                                 aria-valuemin="0" aria-valuemax="100"></div>
                                        </div>
                                        <small>{{ $course->completion_rate }}%</small>
                                    </td>
                                    <td>
                                        <div class="rating">
                                            @for($i = 1; $i <= 5; $i++)
                                                <i class="fas fa-star {{ $i <= $course->average_rating ? 'text-warning' : 'text-muted' }}"></i>
                                            @endfor
                                            <span class="ml-1">{{ number_format($course->average_rating, 1) }}</span>
                                        </div>
                                        <small class="text-muted">({{ $course->ratings_count }} reviews)</small>
                                    </td>
                                    <td>
                                        <span class="font-weight-bold text-success">${{ number_format($course->revenue, 2) }}</span>
                                        <div class="revenue-trend">
                                            <small class="text-{{ $course->revenue_trend >= 0 ? 'success' : 'danger' }}">
                                                <i class="fas fa-arrow-{{ $course->revenue_trend >= 0 ? 'up' : 'down' }}"></i>
                                                {{ abs($course->revenue_trend) }}%
                                            </small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="engagement-score">
                                            <span class="badge badge-{{ $course->engagement_score >= 80 ? 'success' : ($course->engagement_score >= 60 ? 'warning' : 'danger') }}">
                                                {{ $course->engagement_score }}%
                                            </span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.courses.show', $course) }}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.courses.analytics', $course) }}" class="btn btn-sm btn-outline-info">
                                                <i class="fas fa-chart-bar"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.metric-item {
    background: #f8f9fc;
    border-radius: 0.35rem;
    padding: 1rem;
    margin-bottom: 1rem;
    text-align: center;
}

.metric-label {
    font-size: 0.875rem;
    color: #858796;
    margin-bottom: 0.5rem;
}

.metric-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #5a5c69;
    margin-bottom: 0.25rem;
}

.metric-change {
    font-size: 0.75rem;
    font-weight: 600;
}

.page-info {
    max-width: 200px;
}

.page-title {
    font-weight: 600;
    color: #5a5c69;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.course-info {
    display: flex;
    align-items: center;
}

.course-thumbnail {
    width: 40px;
    height: 30px;
    object-fit: cover;
    border-radius: 0.25rem;
    margin-right: 0.75rem;
}

.course-details {
    flex: 1;
    min-width: 0;
}

.course-title {
    font-weight: 600;
    color: #5a5c69;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px;
}

.enrollment-trend,
.revenue-trend {
    margin-top: 0.25rem;
}

.rating {
    display: flex;
    align-items: center;
}

.engagement-score {
    text-align: center;
}

@media (max-width: 768px) {
    .course-info {
        flex-direction: column;
        text-align: center;
    }
    
    .course-thumbnail {
        margin-right: 0;
        margin-bottom: 0.5rem;
    }
}
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Traffic Chart
const trafficCtx = document.getElementById('trafficChart').getContext('2d');
let trafficChart = new Chart(trafficCtx, {
    type: 'line',
    data: {
        labels: {!! json_encode($chartData['traffic']['labels']) !!},
        datasets: [{
            label: 'Page Views',
            data: {!! json_encode($chartData['traffic']['page_views']) !!},
            borderColor: '#4e73df',
            backgroundColor: 'rgba(78, 115, 223, 0.1)',
            borderWidth: 2,
            fill: true,
            tension: 0.4
        }, {
            label: 'Unique Visitors',
            data: {!! json_encode($chartData['traffic']['unique_visitors']) !!},
            borderColor: '#1cc88a',
            backgroundColor: 'rgba(28, 200, 138, 0.1)',
            borderWidth: 2,
            fill: true,
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: true,
                position: 'top'
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Sources Chart
const sourcesCtx = document.getElementById('sourcesChart').getContext('2d');
const sourcesChart = new Chart(sourcesCtx, {
    type: 'doughnut',
    data: {
        labels: ['Direct', 'Social Media', 'Search Engines', 'Referrals'],
        datasets: [{
            data: {!! json_encode($chartData['sources']) !!},
            backgroundColor: ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e'],
            borderWidth: 0
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        }
    }
});

function changeChartType(type) {
    trafficChart.destroy();
    trafficChart = new Chart(trafficCtx, {
        type: type,
        data: trafficChart.data,
        options: trafficChart.options
    });
}

// Initialize DataTable
$(document).ready(function() {
    $('#coursePerformanceTable').DataTable({
        "pageLength": 10,
        "order": [[ 4, "desc" ]], // Sort by revenue by default
        "columnDefs": [
            { "orderable": false, "targets": 6 } // Disable sorting on actions column
        ]
    });
});
</script>
@endpush
