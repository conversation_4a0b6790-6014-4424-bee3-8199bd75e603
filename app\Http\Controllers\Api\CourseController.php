<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Course;
use Illuminate\Http\Request;

class CourseController extends Controller
{
    /**
     * Display a listing of courses.
     */
    public function index(Request $request)
    {
        $query = Course::with(['mentor', 'lessons'])
            ->where('is_published', true);

        // Filter by category
        if ($request->has('category')) {
            $query->where('category', $request->category);
        }

        // Search
        if ($request->has('search')) {
            $query->where(function($q) use ($request) {
                $q->where('title', 'like', '%' . $request->search . '%')
                  ->orWhere('description', 'like', '%' . $request->search . '%');
            });
        }

        // Sort
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $courses = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $courses,
        ]);
    }

    /**
     * Display the specified course.
     */
    public function show(Course $course)
    {
        if (!$course->is_published) {
            return response()->json([
                'success' => false,
                'message' => 'Course not found',
            ], 404);
        }

        $course->load([
            'mentor',
            'lessons' => function($query) {
                $query->where('is_published', true)->orderBy('sort_order');
            },
            'courseRatings'
        ]);

        // Get user progress if authenticated
        if (auth()->check()) {
            $userProgress = auth()->user()->userProgress()
                ->where('course_id', $course->id)
                ->with('lesson')
                ->get();

            $course->user_progress = $userProgress;
        }

        return response()->json([
            'success' => true,
            'data' => $course,
        ]);
    }
}
