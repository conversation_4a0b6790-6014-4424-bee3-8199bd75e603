<?php

namespace App\Http\Controllers;

use App\Models\SubscriptionPlan;
use App\Models\UserSubscription;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Lara<PERSON>\Cashier\Exceptions\IncompletePayment;

class SubscriptionController extends Controller
{
    /**
     * Show subscription plans.
     */
    public function index()
    {
        $plans = SubscriptionPlan::active()->ordered()->get();
        $userSubscription = Auth::user()->activeSubscription;

        return view('subscriptions.index', compact('plans', 'userSubscription'));
    }

    /**
     * Show subscription checkout page.
     */
    public function checkout(SubscriptionPlan $plan, Request $request)
    {
        $billingCycle = $request->get('billing', 'monthly');

        if (!in_array($billingCycle, ['monthly', 'yearly'])) {
            $billingCycle = 'monthly';
        }

        return view('subscriptions.checkout', compact('plan', 'billingCycle'));
    }

    /**
     * Process subscription.
     */
    public function subscribe(Request $request)
    {
        $request->validate([
            'plan_id' => 'required|exists:subscription_plans,id',
            'billing_cycle' => 'required|in:monthly,yearly',
            'payment_method' => 'required|string',
        ]);

        $user = Auth::user();
        $plan = SubscriptionPlan::findOrFail($request->plan_id);
        $billingCycle = $request->billing_cycle;

        // Check if user already has an active subscription
        if ($user->hasActiveSubscription()) {
            return back()->withErrors(['subscription' => 'You already have an active subscription.']);
        }

        try {
            // Create Stripe customer if not exists
            if (!$user->hasStripeId()) {
                $user->createAsStripeCustomer();
            }

            // Add payment method
            $user->addPaymentMethod($request->payment_method);
            $user->updateDefaultPaymentMethod($request->payment_method);

            // Get Stripe price ID
            $stripePriceId = $plan->getStripePriceId($billingCycle);

            if (!$stripePriceId) {
                return back()->withErrors(['payment' => 'Payment configuration error. Please contact support.']);
            }

            // Create subscription
            $subscription = $user->newSubscription('default', $stripePriceId)->create($request->payment_method);

            // Create our custom subscription record
            UserSubscription::create([
                'user_id' => $user->id,
                'subscription_plan_id' => $plan->id,
                'stripe_subscription_id' => $subscription->stripe_id,
                'billing_cycle' => $billingCycle,
                'status' => 'active',
                'amount' => $plan->getPrice($billingCycle),
                'currency' => 'USD',
                'current_period_start' => now(),
                'current_period_end' => $billingCycle === 'yearly' ? now()->addYear() : now()->addMonth(),
            ]);

            return redirect()->route('dashboard')->with('success', 'Subscription activated successfully!');

        } catch (IncompletePayment $exception) {
            return redirect()->route('cashier.payment', [$exception->payment->id, 'redirect' => route('dashboard')]);
        } catch (\Exception $e) {
            return back()->withErrors(['payment' => 'Payment failed: ' . $e->getMessage()]);
        }
    }

    /**
     * Cancel subscription.
     */
    public function cancel()
    {
        $user = Auth::user();
        $subscription = $user->subscription('default');

        if ($subscription) {
            $subscription->cancel();

            // Update our custom subscription record
            $userSubscription = $user->activeSubscription;
            if ($userSubscription) {
                $userSubscription->update([
                    'status' => 'cancelled',
                    'cancelled_at' => now(),
                ]);
            }

            return redirect()->route('subscriptions.index')->with('success', 'Subscription cancelled successfully.');
        }

        return redirect()->route('subscriptions.index')->withErrors(['subscription' => 'No active subscription found.']);
    }

    /**
     * Resume subscription.
     */
    public function resume()
    {
        $user = Auth::user();
        $subscription = $user->subscription('default');

        if ($subscription && $subscription->cancelled()) {
            $subscription->resume();

            // Update our custom subscription record
            $userSubscription = $user->activeSubscription;
            if ($userSubscription) {
                $userSubscription->update([
                    'status' => 'active',
                    'cancelled_at' => null,
                ]);
            }

            return redirect()->route('subscriptions.index')->with('success', 'Subscription resumed successfully.');
        }

        return redirect()->route('subscriptions.index')->withErrors(['subscription' => 'Cannot resume subscription.']);
    }

    /**
     * Update payment method.
     */
    public function updatePaymentMethod(Request $request)
    {
        $request->validate([
            'payment_method' => 'required|string',
        ]);

        $user = Auth::user();

        try {
            $user->updateDefaultPaymentMethod($request->payment_method);
            return back()->with('success', 'Payment method updated successfully.');
        } catch (\Exception $e) {
            return back()->withErrors(['payment' => 'Failed to update payment method: ' . $e->getMessage()]);
        }
    }
}
