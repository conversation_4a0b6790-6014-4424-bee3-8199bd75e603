<?php

use Illuminate\Support\Facades\Broadcast;
use App\Models\User;
use App\Models\Course;
use App\Models\LiveCall;
use App\Models\CommunityPost;

/*
|--------------------------------------------------------------------------
| Broadcast Channels
|--------------------------------------------------------------------------
|
| Here you may register all of the event broadcasting channels that your
| application supports. The given channel authorization callbacks are
| used to check if an authenticated user can listen to the channel.
|
*/

// User-specific channels
Broadcast::channel('App.Models.User.{id}', function ($user, $id) {
    return (int) $user->id === (int) $id;
});

// User notifications channel
Broadcast::channel('user.{userId}', function ($user, $userId) {
    return (int) $user->id === (int) $userId;
});

// Course-specific channels
Broadcast::channel('course.{courseId}', function ($user, $courseId) {
    $course = Course::find($courseId);

    if (!$course || !$course->is_published) {
        return false;
    }

    // Check if user is enrolled in the course or is the mentor
    return $user->isEnrolledIn($course) || $user->id === $course->mentor_id || $user->hasRole('admin');
});

// Lesson progress channel
Broadcast::channel('lesson.{lessonId}', function ($user, $lessonId) {
    $lesson = \App\Models\Lesson::find($lessonId);

    if (!$lesson || !$lesson->is_published) {
        return false;
    }

    // Check if user has access to the lesson
    return $user->isEnrolledIn($lesson->course) || $user->id === $lesson->course->mentor_id || $user->hasRole('admin');
});

// Live call channels
Broadcast::channel('live-call.{liveCallId}', function ($user, $liveCallId) {
    $liveCall = LiveCall::find($liveCallId);

    if (!$liveCall) {
        return false;
    }

    // Check if user is registered for the live call or is the mentor
    return $liveCall->attendees()->where('user_id', $user->id)->exists() ||
           $user->id === $liveCall->mentor_id ||
           $user->hasRole('admin');
});

// Live call chat channel
Broadcast::channel('live-call-chat.{liveCallId}', function ($user, $liveCallId) {
    $liveCall = LiveCall::find($liveCallId);

    if (!$liveCall || $liveCall->status !== 'live') {
        return false;
    }

    // Only registered attendees and mentors can participate in chat
    return $liveCall->attendees()->where('user_id', $user->id)->exists() ||
           $user->id === $liveCall->mentor_id ||
           $user->hasRole('admin');
});

// Community channels
Broadcast::channel('community', function ($user) {
    // All authenticated users can access community channel
    return $user ? true : false;
});

Broadcast::channel('community.post.{postId}', function ($user, $postId) {
    $post = CommunityPost::find($postId);

    if (!$post || !$post->is_published) {
        return false;
    }

    // All authenticated users can access public posts
    return true;
});

// Admin channels
Broadcast::channel('admin', function ($user) {
    return $user->hasRole('admin');
});

Broadcast::channel('admin.notifications', function ($user) {
    return $user->hasRole('admin');
});

// Mentor channels
Broadcast::channel('mentor.{mentorId}', function ($user, $mentorId) {
    return (int) $user->id === (int) $mentorId && $user->hasRole('mentor');
});

Broadcast::channel('mentor.notifications', function ($user) {
    return $user->hasRole(['mentor', 'admin']);
});

// Global announcement channel
Broadcast::channel('announcements', function ($user) {
    // All authenticated users can receive announcements
    return $user ? true : false;
});

// System status channel
Broadcast::channel('system.status', function ($user) {
    // All authenticated users can receive system status updates
    return $user ? true : false;
});

// Payment status channel
Broadcast::channel('payment.{userId}', function ($user, $userId) {
    return (int) $user->id === (int) $userId;
});

// Subscription status channel
Broadcast::channel('subscription.{userId}', function ($user, $userId) {
    return (int) $user->id === (int) $userId;
});

// Course progress channel for mentors
Broadcast::channel('course.{courseId}.mentor', function ($user, $courseId) {
    $course = Course::find($courseId);

    if (!$course) {
        return false;
    }

    // Only the course mentor and admins can access this channel
    return $user->id === $course->mentor_id || $user->hasRole('admin');
});

// Real-time analytics channel for admins
Broadcast::channel('analytics.realtime', function ($user) {
    return $user->hasRole('admin');
});

// Support chat channels
Broadcast::channel('support.{userId}', function ($user, $userId) {
    // User can access their own support channel, admins can access all
    return (int) $user->id === (int) $userId || $user->hasRole('admin');
});

// Presence channels for online users
Broadcast::channel('online-users', function ($user) {
    return [
        'id' => $user->id,
        'name' => $user->name,
        'avatar' => $user->avatar ? asset('storage/' . $user->avatar) : null,
        'role' => $user->roles->first()->name ?? 'user',
    ];
});

// Live call presence channel
Broadcast::channel('live-call-presence.{liveCallId}', function ($user, $liveCallId) {
    $liveCall = LiveCall::find($liveCallId);

    if (!$liveCall || $liveCall->status !== 'live') {
        return false;
    }

    // Check if user is registered for the live call
    if ($liveCall->attendees()->where('user_id', $user->id)->exists() ||
        $user->id === $liveCall->mentor_id ||
        $user->hasRole('admin')) {

        return [
            'id' => $user->id,
            'name' => $user->name,
            'avatar' => $user->avatar ? asset('storage/' . $user->avatar) : null,
            'role' => $user->roles->first()->name ?? 'user',
            'is_mentor' => $user->id === $liveCall->mentor_id,
        ];
    }

    return false;
});

// Course study group channels
Broadcast::channel('study-group.{courseId}', function ($user, $courseId) {
    $course = Course::find($courseId);

    if (!$course) {
        return false;
    }

    // Only enrolled students can join study groups
    return $user->isEnrolledIn($course);
});

// Notification channels by type
Broadcast::channel('notifications.course-updates.{userId}', function ($user, $userId) {
    return (int) $user->id === (int) $userId;
});

Broadcast::channel('notifications.live-calls.{userId}', function ($user, $userId) {
    return (int) $user->id === (int) $userId;
});

Broadcast::channel('notifications.community.{userId}', function ($user, $userId) {
    return (int) $user->id === (int) $userId;
});

// Emergency broadcast channel
Broadcast::channel('emergency', function ($user) {
    // All authenticated users should receive emergency broadcasts
    return $user ? true : false;
});
