<?php

namespace App\Http\Controllers\Mentor;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Course;
use App\Models\Enrollment;
use App\Models\LiveCall;
use App\Models\User;
use App\Models\Review;
use Carbon\Carbon;

class MentorDashboardController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:mentor']);
    }

    public function index()
    {
        $mentor = Auth::user();

        try {
            // Get mentor's courses
            $courses = Course::where('mentor_id', $mentor->id)
                ->withCount(['enrollments', 'lessons', 'reviews'])
                ->with(['category'])
                ->orderBy('created_at', 'desc')
                ->get();
        } catch (\Exception $e) {
            // If tables don't exist yet, use empty collection
            $courses = collect();
        }

        // Calculate statistics with error handling
        $stats = [
            'total_courses' => $courses->count(),
            'total_students' => $this->safeSum($courses, 'enrollments_count'),
            'total_revenue' => $this->calculateTotalRevenue($mentor),
            'average_rating' => $this->calculateAverageRating($mentor),
            'active_students' => $this->getActiveStudentsCount($mentor),
            'completion_rate' => $this->calculateCompletionRate($mentor),
        ];

        // Get recent enrollments with error handling
        try {
            $recentEnrollments = Enrollment::whereHas('course', function($query) use ($mentor) {
                $query->where('mentor_id', $mentor->id);
            })
            ->with(['user', 'course'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();
        } catch (\Exception $e) {
            $recentEnrollments = collect();
        }

        // Get upcoming live calls with error handling
        try {
            $upcomingLiveCalls = LiveCall::where('mentor_id', $mentor->id)
                ->where('scheduled_at', '>', now())
                ->orderBy('scheduled_at', 'asc')
                ->limit(5)
                ->get();
        } catch (\Exception $e) {
            $upcomingLiveCalls = collect();
        }

        // Get recent reviews with error handling
        try {
            $recentReviews = Review::whereHas('course', function($query) use ($mentor) {
                $query->where('mentor_id', $mentor->id);
            })
            ->with(['user', 'course'])
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();
        } catch (\Exception $e) {
            $recentReviews = collect();
        }

        // Get revenue chart data (last 12 months)
        $revenueData = $this->getRevenueChartData($mentor);

        // Get student progress data
        $studentProgressData = $this->getStudentProgressData($mentor);

        // Get top performing courses
        $topCourses = $courses->sortByDesc('enrollments_count')->take(5);

        return view('mentor.dashboard', compact(
            'mentor',
            'courses',
            'stats',
            'recentEnrollments',
            'upcomingLiveCalls',
            'recentReviews',
            'revenueData',
            'studentProgressData',
            'topCourses'
        ));
    }

    private function calculateTotalRevenue($mentor)
    {
        try {
            // This would calculate actual revenue from enrollments
            // For now, return a placeholder value
            return Enrollment::whereHas('course', function($query) use ($mentor) {
                $query->where('mentor_id', $mentor->id);
            })->count() * 50; // Assuming $50 average per enrollment
        } catch (\Exception $e) {
            return 0;
        }
    }

    private function safeSum($collection, $field)
    {
        try {
            return $collection->sum($field);
        } catch (\Exception $e) {
            return 0;
        }
    }

    private function calculateAverageRating($mentor)
    {
        try {
            $reviews = Review::whereHas('course', function($query) use ($mentor) {
                $query->where('mentor_id', $mentor->id);
            });

            return $reviews->count() > 0 ? $reviews->avg('rating') : 0;
        } catch (\Exception $e) {
            return 0;
        }
    }

    private function getActiveStudentsCount($mentor)
    {
        try {
            return Enrollment::whereHas('course', function($query) use ($mentor) {
                $query->where('mentor_id', $mentor->id);
            })
            ->where('last_accessed_at', '>', Carbon::now()->subDays(30))
            ->distinct('user_id')
            ->count();
        } catch (\Exception $e) {
            return 0;
        }
    }

    private function calculateCompletionRate($mentor)
    {
        try {
            $totalEnrollments = Enrollment::whereHas('course', function($query) use ($mentor) {
                $query->where('mentor_id', $mentor->id);
            })->count();

            $completedEnrollments = Enrollment::whereHas('course', function($query) use ($mentor) {
                $query->where('mentor_id', $mentor->id);
            })
            ->where('completed_at', '!=', null)
            ->count();

            return $totalEnrollments > 0 ? ($completedEnrollments / $totalEnrollments) * 100 : 0;
        } catch (\Exception $e) {
            return 0;
        }
    }

    private function getRevenueChartData($mentor)
    {
        $data = [];
        $months = [];
        
        for ($i = 11; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            $months[] = $date->format('M Y');
            
            // Calculate revenue for this month
            $monthlyRevenue = Enrollment::whereHas('course', function($query) use ($mentor) {
                $query->where('mentor_id', $mentor->id);
            })
            ->whereYear('created_at', $date->year)
            ->whereMonth('created_at', $date->month)
            ->count() * 50; // Placeholder calculation
            
            $data[] = $monthlyRevenue;
        }

        return [
            'labels' => $months,
            'data' => $data
        ];
    }

    private function getStudentProgressData($mentor)
    {
        $totalStudents = Enrollment::whereHas('course', function($query) use ($mentor) {
            $query->where('mentor_id', $mentor->id);
        })->distinct('user_id')->count();

        $activeStudents = $this->getActiveStudentsCount($mentor);
        $completedStudents = Enrollment::whereHas('course', function($query) use ($mentor) {
            $query->where('mentor_id', $mentor->id);
        })
        ->where('completed_at', '!=', null)
        ->distinct('user_id')
        ->count();

        return [
            'total' => $totalStudents,
            'active' => $activeStudents,
            'completed' => $completedStudents,
            'inactive' => $totalStudents - $activeStudents
        ];
    }

    public function analytics()
    {
        $mentor = Auth::user();
        
        // Detailed analytics data
        $analyticsData = [
            'course_performance' => $this->getCoursePerformanceData($mentor),
            'student_engagement' => $this->getStudentEngagementData($mentor),
            'revenue_breakdown' => $this->getRevenueBreakdownData($mentor),
            'geographic_data' => $this->getGeographicData($mentor),
            'time_series_data' => $this->getTimeSeriesData($mentor),
        ];

        return view('mentor.analytics', compact('mentor', 'analyticsData'));
    }

    private function getCoursePerformanceData($mentor)
    {
        return Course::where('mentor_id', $mentor->id)
            ->withCount(['enrollments', 'reviews', 'lessons'])
            ->with(['reviews' => function($query) {
                $query->select('course_id', \DB::raw('AVG(rating) as avg_rating'))
                    ->groupBy('course_id');
            }])
            ->get()
            ->map(function($course) {
                return [
                    'id' => $course->id,
                    'title' => $course->title,
                    'enrollments' => $course->enrollments_count,
                    'completion_rate' => $this->getCourseCompletionRate($course->id),
                    'average_rating' => $course->reviews->avg('rating') ?? 0,
                    'revenue' => $course->enrollments_count * 50, // Placeholder
                ];
            });
    }

    private function getStudentEngagementData($mentor)
    {
        // Get engagement metrics for mentor's students
        return [
            'daily_active_users' => $this->getDailyActiveUsers($mentor),
            'session_duration' => $this->getAverageSessionDuration($mentor),
            'lesson_completion_rate' => $this->getLessonCompletionRate($mentor),
            'discussion_participation' => $this->getDiscussionParticipation($mentor),
        ];
    }

    private function getRevenueBreakdownData($mentor)
    {
        $courses = Course::where('mentor_id', $mentor->id)->get();
        
        return $courses->map(function($course) {
            return [
                'course_title' => $course->title,
                'enrollments' => $course->enrollments()->count(),
                'revenue' => $course->enrollments()->count() * 50, // Placeholder
                'percentage' => 0, // Calculate percentage of total revenue
            ];
        });
    }

    private function getGeographicData($mentor)
    {
        // Get geographic distribution of students
        return Enrollment::whereHas('course', function($query) use ($mentor) {
            $query->where('mentor_id', $mentor->id);
        })
        ->join('users', 'enrollments.user_id', '=', 'users.id')
        ->select('users.country', \DB::raw('COUNT(*) as count'))
        ->groupBy('users.country')
        ->orderBy('count', 'desc')
        ->limit(10)
        ->get();
    }

    private function getTimeSeriesData($mentor)
    {
        // Get time series data for various metrics
        $data = [];
        
        for ($i = 30; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            
            $data[] = [
                'date' => $date->format('Y-m-d'),
                'enrollments' => $this->getEnrollmentsForDate($mentor, $date),
                'active_users' => $this->getActiveUsersForDate($mentor, $date),
                'revenue' => $this->getRevenueForDate($mentor, $date),
            ];
        }
        
        return $data;
    }

    private function getCourseCompletionRate($courseId)
    {
        $totalEnrollments = Enrollment::where('course_id', $courseId)->count();
        $completedEnrollments = Enrollment::where('course_id', $courseId)
            ->where('completed_at', '!=', null)
            ->count();

        return $totalEnrollments > 0 ? ($completedEnrollments / $totalEnrollments) * 100 : 0;
    }

    private function getDailyActiveUsers($mentor)
    {
        return Enrollment::whereHas('course', function($query) use ($mentor) {
            $query->where('mentor_id', $mentor->id);
        })
        ->where('last_accessed_at', '>', Carbon::now()->subDay())
        ->distinct('user_id')
        ->count();
    }

    private function getAverageSessionDuration($mentor)
    {
        // Placeholder - would calculate from actual session data
        return 45; // minutes
    }

    private function getLessonCompletionRate($mentor)
    {
        // Placeholder - would calculate from lesson progress data
        return 78; // percentage
    }

    private function getDiscussionParticipation($mentor)
    {
        // Placeholder - would calculate from discussion/comment data
        return 65; // percentage
    }

    private function getEnrollmentsForDate($mentor, $date)
    {
        return Enrollment::whereHas('course', function($query) use ($mentor) {
            $query->where('mentor_id', $mentor->id);
        })
        ->whereDate('created_at', $date)
        ->count();
    }

    private function getActiveUsersForDate($mentor, $date)
    {
        return Enrollment::whereHas('course', function($query) use ($mentor) {
            $query->where('mentor_id', $mentor->id);
        })
        ->whereDate('last_accessed_at', $date)
        ->distinct('user_id')
        ->count();
    }

    private function getRevenueForDate($mentor, $date)
    {
        return $this->getEnrollmentsForDate($mentor, $date) * 50; // Placeholder
    }
}
