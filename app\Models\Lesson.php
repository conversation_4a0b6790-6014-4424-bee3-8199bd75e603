<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Lesson extends Model
{
    use HasFactory;

    protected $fillable = [
        'course_id',
        'title',
        'slug',
        'description',
        'content',
        'video_url',
        'video_provider',
        'duration_minutes',
        'resources',
        'sort_order',
        'is_published',
        'is_free',
    ];

    protected $casts = [
        'resources' => 'array',
        'is_published' => 'boolean',
        'is_free' => 'boolean',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($lesson) {
            if (empty($lesson->slug)) {
                $lesson->slug = Str::slug($lesson->title);
            }
        });

        static::updating(function ($lesson) {
            if ($lesson->isDirty('title') && empty($lesson->slug)) {
                $lesson->slug = Str::slug($lesson->title);
            }
        });
    }

    /**
     * Get the course that owns the lesson.
     */
    public function course()
    {
        return $this->belongsTo(Course::class);
    }

    /**
     * Get user progress for this lesson.
     */
    public function userProgress()
    {
        return $this->hasMany(UserProgress::class);
    }

    /**
     * Get progress for a specific user.
     */
    public function progressForUser($userId)
    {
        return $this->userProgress()->where('user_id', $userId)->first();
    }

    /**
     * Check if user can access this lesson.
     */
    public function canBeAccessedBy(User $user): bool
    {
        // Free lessons can be accessed by anyone
        if ($this->is_free) {
            return true;
        }

        // Check if user can access the parent course
        return $this->course->canBeAccessedBy($user);
    }

    /**
     * Check if lesson is completed by user.
     */
    public function isCompletedBy($userId): bool
    {
        $progress = $this->progressForUser($userId);
        return $progress ? $progress->is_completed : false;
    }

    /**
     * Get watch time for user.
     */
    public function getWatchTimeForUser($userId): int
    {
        $progress = $this->progressForUser($userId);
        return $progress ? $progress->watch_time_seconds : 0;
    }

    /**
     * Get completion percentage for user.
     */
    public function getCompletionPercentageForUser($userId): float
    {
        $progress = $this->progressForUser($userId);
        return $progress ? $progress->completion_percentage : 0;
    }

    /**
     * Mark lesson as started for user.
     */
    public function markAsStartedBy($userId)
    {
        return UserProgress::updateOrCreate(
            [
                'user_id' => $userId,
                'course_id' => $this->course_id,
                'lesson_id' => $this->id,
            ],
            [
                'started_at' => now(),
            ]
        );
    }

    /**
     * Mark lesson as completed for user.
     */
    public function markAsCompletedBy($userId)
    {
        return UserProgress::updateOrCreate(
            [
                'user_id' => $userId,
                'course_id' => $this->course_id,
                'lesson_id' => $this->id,
            ],
            [
                'is_completed' => true,
                'completion_percentage' => 100,
                'completed_at' => now(),
                'started_at' => now(), // In case it wasn't started before
            ]
        );
    }

    /**
     * Update watch time for user.
     */
    public function updateWatchTimeForUser($userId, $seconds)
    {
        return UserProgress::updateOrCreate(
            [
                'user_id' => $userId,
                'course_id' => $this->course_id,
                'lesson_id' => $this->id,
            ],
            [
                'watch_time_seconds' => $seconds,
                'started_at' => now(),
            ]
        );
    }

    /**
     * Get formatted duration.
     */
    public function getFormattedDurationAttribute(): string
    {
        if (!$this->duration_minutes) {
            return 'N/A';
        }

        $hours = floor($this->duration_minutes / 60);
        $minutes = $this->duration_minutes % 60;

        if ($hours > 0) {
            return $hours . 'h ' . $minutes . 'm';
        }

        return $minutes . 'm';
    }

    /**
     * Scope to get published lessons.
     */
    public function scopePublished($query)
    {
        return $query->where('is_published', true);
    }

    /**
     * Scope to get free lessons.
     */
    public function scopeFree($query)
    {
        return $query->where('is_free', true);
    }

    /**
     * Scope to order by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName()
    {
        return 'slug';
    }
}
