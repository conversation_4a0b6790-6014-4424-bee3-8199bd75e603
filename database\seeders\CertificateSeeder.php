<?php

namespace Database\Seeders;

use App\Models\Certificate;
use App\Models\Course;
use App\Models\UserProgress;
use Illuminate\Database\Seeder;

class CertificateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get completed course progress (users who completed all lessons in a course)
        $completedCourses = UserProgress::where('is_completed', true)
            ->with(['user', 'course'])
            ->get()
            ->groupBy(function($progress) {
                return $progress->user_id . '_' . $progress->course_id;
            })
            ->filter(function($userCourseProgress, $key) {
                // Check if user completed all lessons in the course
                $parts = explode('_', $key);
                $courseId = $parts[1];
                $course = Course::find($courseId);
                return $course && $userCourseProgress->count() >= $course->lessons()->count();
            })
            ->map(function($userCourseProgress) {
                return $userCourseProgress->first(); // Take first completed lesson as representative
            });

        foreach ($completedCourses as $progress) {
            $certificateNumber = 'TRW-' . strtoupper(\Str::random(8));

            Certificate::firstOrCreate(
                [
                    'user_id' => $progress->user_id,
                    'course_id' => $progress->course_id,
                ],
                [
                    'certificate_number' => $certificateNumber,
                    'issued_at' => $progress->completed_at,
                    'expires_at' => null, // Certificates don't expire
                    'is_valid' => true,
                    'verification_url' => url("/certificates/verify/{$certificateNumber}"),
                    'certificate_data' => [
                        'student_name' => $progress->user->name,
                        'course_title' => $progress->course->title,
                        'completion_date' => $progress->completed_at->format('F j, Y'),
                        'instructor' => $progress->course->mentor->name ?? 'The Real World',
                        'duration' => $progress->course->lessons->sum('duration_minutes') . ' minutes',
                        'skills' => $this->getSkillsForCourse($progress->course->category),
                    ],
                ]
            );
        }

        $this->command->info('Certificates seeded successfully!');
    }

    private function getSkillsForCourse($category)
    {
        $skills = [
            'E-commerce' => [
                'Product Research',
                'Store Setup',
                'Digital Marketing',
                'Customer Service',
                'Business Scaling',
            ],
            'Copywriting' => [
                'Persuasive Writing',
                'Sales Psychology',
                'Email Marketing',
                'Content Strategy',
                'Conversion Optimization',
            ],
            'Cryptocurrency' => [
                'Blockchain Technology',
                'Crypto Trading',
                'DeFi Protocols',
                'Risk Management',
                'Portfolio Management',
            ],
            'Stocks & Trading' => [
                'Technical Analysis',
                'Fundamental Analysis',
                'Risk Management',
                'Trading Psychology',
                'Portfolio Strategy',
            ],
            'Amazon FBA' => [
                'Product Sourcing',
                'Amazon SEO',
                'PPC Advertising',
                'Inventory Management',
                'Brand Building',
            ],
            'Freelancing' => [
                'Client Acquisition',
                'Service Delivery',
                'Pricing Strategy',
                'Business Development',
                'Professional Communication',
            ],
            'Content Creation' => [
                'Content Strategy',
                'Video Production',
                'Audience Building',
                'Monetization',
                'Brand Development',
            ],
            'Business Management' => [
                'Leadership',
                'Operations Management',
                'Strategic Planning',
                'Team Building',
                'Performance Optimization',
            ],
        ];

        return $skills[$category] ?? [
            'Problem Solving',
            'Strategic Thinking',
            'Project Management',
            'Communication',
            'Leadership',
        ];
    }
}
