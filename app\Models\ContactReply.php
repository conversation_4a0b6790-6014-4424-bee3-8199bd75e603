<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ContactReply extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'contact_message_id',
        'user_id',
        'message',
        'is_internal',
        'attachment_path',
    ];

    protected $casts = [
        'is_internal' => 'boolean',
    ];

    protected $dates = [
        'deleted_at',
    ];

    public function contactMessage()
    {
        return $this->belongsTo(ContactMessage::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function getAttachmentUrlAttribute()
    {
        return $this->attachment_path ? asset('storage/' . $this->attachment_path) : null;
    }

    public function getAttachmentNameAttribute()
    {
        return $this->attachment_path ? basename($this->attachment_path) : null;
    }

    public function scopePublic($query)
    {
        return $query->where('is_internal', false);
    }

    public function scopeInternal($query)
    {
        return $query->where('is_internal', true);
    }
}
