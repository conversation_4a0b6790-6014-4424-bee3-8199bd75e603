<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Lesson;
use App\Models\Course;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class LessonManagementController extends Controller
{
    /**
     * Display a listing of lessons.
     */
    public function index(Request $request)
    {
        $query = Lesson::with(['course', 'course.mentor']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhereHas('course', function ($courseQuery) use ($search) {
                      $courseQuery->where('title', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by course
        if ($request->filled('course')) {
            $query->where('course_id', $request->course);
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('is_published', $request->status === 'published');
        }

        $lessons = $query->latest()->paginate(20);

        // Get statistics
        $stats = [
            'total_lessons' => Lesson::count(),
            'published_lessons' => Lesson::where('is_published', true)->count(),
            'draft_lessons' => Lesson::where('is_published', false)->count(),
            'total_duration' => Lesson::sum('duration_minutes'),
        ];

        $courses = Course::select('id', 'title')->get();

        return view('admin.lessons.index', compact('lessons', 'stats', 'courses'));
    }

    /**
     * Show the form for creating a new lesson.
     */
    public function create()
    {
        $courses = Course::select('id', 'title')->get();
        return view('admin.lessons.create', compact('courses'));
    }

    /**
     * Store a newly created lesson.
     */
    public function store(Request $request)
    {
        $request->validate([
            'course_id' => 'required|exists:courses,id',
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'content' => 'nullable|string',
            'video_url' => 'nullable|url',
            'video_file' => 'nullable|file|mimes:mp4,avi,mov,wmv|max:512000', // 500MB max
            'duration_minutes' => 'required|integer|min:1',
            'order' => 'required|integer|min:0',
            'is_free' => 'boolean',
            'is_published' => 'boolean',
        ]);

        $lessonData = $request->except(['video_file']);

        // Handle video file upload
        if ($request->hasFile('video_file')) {
            $videoPath = $request->file('video_file')->store('lessons/videos', 'public');
            $lessonData['video_path'] = $videoPath;
        }

        $lesson = Lesson::create($lessonData);

        return redirect()->route('admin.lessons.show', $lesson)
            ->with('success', 'Lesson created successfully.');
    }

    /**
     * Display the specified lesson.
     */
    public function show(Lesson $lesson)
    {
        $lesson->load(['course', 'course.mentor', 'progress']);
        
        $stats = [
            'total_views' => $lesson->progress()->count(),
            'completed_views' => $lesson->progress()->where('completed_at', '!=', null)->count(),
            'average_completion_time' => $lesson->progress()
                ->whereNotNull('completed_at')
                ->avg('time_spent_seconds'),
        ];

        return view('admin.lessons.show', compact('lesson', 'stats'));
    }

    /**
     * Show the form for editing the lesson.
     */
    public function edit(Lesson $lesson)
    {
        $courses = Course::select('id', 'title')->get();
        return view('admin.lessons.edit', compact('lesson', 'courses'));
    }

    /**
     * Update the specified lesson.
     */
    public function update(Request $request, Lesson $lesson)
    {
        $request->validate([
            'course_id' => 'required|exists:courses,id',
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'content' => 'nullable|string',
            'video_url' => 'nullable|url',
            'video_file' => 'nullable|file|mimes:mp4,avi,mov,wmv|max:512000',
            'duration_minutes' => 'required|integer|min:1',
            'order' => 'required|integer|min:0',
            'is_free' => 'boolean',
            'is_published' => 'boolean',
        ]);

        $lessonData = $request->except(['video_file']);

        // Handle video file upload
        if ($request->hasFile('video_file')) {
            // Delete old video file if exists
            if ($lesson->video_path) {
                Storage::disk('public')->delete($lesson->video_path);
            }
            
            $videoPath = $request->file('video_file')->store('lessons/videos', 'public');
            $lessonData['video_path'] = $videoPath;
        }

        $lesson->update($lessonData);

        return redirect()->route('admin.lessons.show', $lesson)
            ->with('success', 'Lesson updated successfully.');
    }

    /**
     * Remove the specified lesson.
     */
    public function destroy(Lesson $lesson)
    {
        // Delete associated video file
        if ($lesson->video_path) {
            Storage::disk('public')->delete($lesson->video_path);
        }

        $lesson->delete();

        return redirect()->route('admin.lessons.index')
            ->with('success', 'Lesson deleted successfully.');
    }

    /**
     * Toggle lesson published status.
     */
    public function togglePublished(Lesson $lesson)
    {
        $lesson->update(['is_published' => !$lesson->is_published]);
        
        $status = $lesson->is_published ? 'published' : 'unpublished';
        return back()->with('success', "Lesson {$status} successfully.");
    }

    /**
     * Bulk actions for lessons.
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:publish,unpublish,delete',
            'lesson_ids' => 'required|array',
            'lesson_ids.*' => 'exists:lessons,id',
        ]);

        $lessonIds = $request->lesson_ids;
        $action = $request->action;

        switch ($action) {
            case 'publish':
                Lesson::whereIn('id', $lessonIds)->update(['is_published' => true]);
                $message = 'Lessons published successfully.';
                break;
            case 'unpublish':
                Lesson::whereIn('id', $lessonIds)->update(['is_published' => false]);
                $message = 'Lessons unpublished successfully.';
                break;
            case 'delete':
                $lessons = Lesson::whereIn('id', $lessonIds)->get();
                foreach ($lessons as $lesson) {
                    if ($lesson->video_path) {
                        Storage::disk('public')->delete($lesson->video_path);
                    }
                }
                Lesson::whereIn('id', $lessonIds)->delete();
                $message = 'Lessons deleted successfully.';
                break;
        }

        return back()->with('success', $message);
    }

    /**
     * Reorder lessons within a course.
     */
    public function reorder(Request $request)
    {
        $request->validate([
            'course_id' => 'required|exists:courses,id',
            'lesson_orders' => 'required|array',
            'lesson_orders.*.id' => 'required|exists:lessons,id',
            'lesson_orders.*.order' => 'required|integer|min:0',
        ]);

        foreach ($request->lesson_orders as $lessonOrder) {
            Lesson::where('id', $lessonOrder['id'])
                  ->where('course_id', $request->course_id)
                  ->update(['order' => $lessonOrder['order']]);
        }

        return response()->json(['success' => true, 'message' => 'Lessons reordered successfully.']);
    }

    /**
     * Get lesson analytics.
     */
    public function analytics(Lesson $lesson)
    {
        $period = request()->get('period', '30'); // days
        $startDate = now()->subDays($period);

        // Views over time
        $viewsData = $lesson->progress()
            ->where('created_at', '>=', $startDate)
            ->selectRaw('DATE(created_at) as date, COUNT(*) as views')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // Completion rate
        $totalViews = $lesson->progress()->count();
        $completedViews = $lesson->progress()->whereNotNull('completed_at')->count();
        $completionRate = $totalViews > 0 ? ($completedViews / $totalViews) * 100 : 0;

        // Average watch time
        $avgWatchTime = $lesson->progress()
            ->where('time_spent_seconds', '>', 0)
            ->avg('time_spent_seconds');

        // Drop-off points (if you track progress percentage)
        $dropOffData = $lesson->progress()
            ->selectRaw('FLOOR(progress_percentage/10)*10 as range_start, COUNT(*) as count')
            ->groupBy('range_start')
            ->orderBy('range_start')
            ->get();

        return view('admin.lessons.analytics', compact(
            'lesson', 'viewsData', 'completionRate', 'avgWatchTime', 'dropOffData', 'period'
        ));
    }
}
