@extends('layouts.app')

@section('content')
<!-- Hero Section -->
<div class="relative bg-gradient-to-r from-blue-600 to-purple-700 text-white overflow-hidden">
    <!-- Background Animation -->
    <div class="absolute inset-0 opacity-20">
        <div class="absolute top-0 left-0 w-72 h-72 bg-white rounded-full mix-blend-multiply filter blur-xl animate-float"></div>
        <div class="absolute top-0 right-0 w-72 h-72 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl animate-float" style="animation-delay: 2s;"></div>
        <div class="absolute -bottom-8 left-20 w-72 h-72 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl animate-float" style="animation-delay: 4s;"></div>
    </div>

    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
        <div class="text-center animate-on-scroll">
            <h1 class="text-4xl md:text-6xl font-bold mb-6">
                Scale from zero to <span class="text-yellow-400 animate-pulse">$10k/month</span>
            </h1>
            <p class="text-xl md:text-2xl mb-8 text-blue-100 fade-in">
                Join thousands of students learning proven strategies from successful mentors
            </p>
            <div class="space-x-4 stagger-animation">
                <x-button href="{{ route('register') }}" class="bg-yellow-400 hover:bg-yellow-500 text-black font-bold hover-lift">
                    <x-slot name="icon">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </x-slot>
                    Join The Real World
                </x-button>
                <x-button href="#features" variant="outline" class="border-2 border-white text-white hover:bg-white hover:text-blue-600 hover-lift">
                    Learn More
                </x-button>
            </div>
        </div>
    </div>

    <!-- Scroll Indicator -->
    <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <svg class="w-6 h-6 text-white opacity-75" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
        </svg>
    </div>
</div>

<!-- Features Section -->
<div id="features" class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">What You'll Get</h2>
            <p class="text-lg text-gray-600">Everything you need to build a successful online business</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div class="text-center p-6">
                <div class="bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold mb-2">Expert Courses</h3>
                <p class="text-gray-600">Learn from successful entrepreneurs in crypto, copywriting, e-commerce, and more.</p>
            </div>
            
            <div class="text-center p-6">
                <div class="bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold mb-2">Live Mentorship</h3>
                <p class="text-gray-600">Join live calls with mentors and get your questions answered in real-time.</p>
            </div>
            
            <div class="text-center p-6">
                <div class="bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold mb-2">Community Access</h3>
                <p class="text-gray-600">Connect with like-minded entrepreneurs and build valuable relationships.</p>
            </div>
        </div>
    </div>
</div>

<!-- Pricing Section -->
<div class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Choose Your Plan</h2>
            <p class="text-lg text-gray-600">Start your journey to financial freedom today</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            @foreach($subscriptionPlans as $plan)
            <div class="bg-white rounded-lg shadow-lg p-6 {{ $plan->is_featured ? 'ring-2 ring-blue-500 transform scale-105' : '' }}">
                @if($plan->is_featured)
                    <div class="bg-blue-500 text-white text-center py-1 px-4 rounded-full text-sm font-medium mb-4">
                        Most Popular
                    </div>
                @endif
                
                <h3 class="text-xl font-bold text-center mb-2">{{ $plan->name }}</h3>
                <div class="text-center mb-4">
                    <span class="text-3xl font-bold">${{ number_format($plan->monthly_price, 0) }}</span>
                    <span class="text-gray-600">/month</span>
                </div>
                
                <p class="text-gray-600 text-center mb-6">{{ $plan->description }}</p>
                
                <ul class="space-y-2 mb-6">
                    @foreach($plan->features as $feature)
                    <li class="flex items-center">
                        <svg class="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        {{ $feature }}
                    </li>
                    @endforeach
                </ul>
                
                <a href="{{ route('register') }}" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded text-center block transition duration-300">
                    Get Started
                </a>
            </div>
            @endforeach
        </div>
    </div>
</div>

<!-- Testimonials Section -->
@if($testimonials->count() > 0)
<div class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Success Stories</h2>
            <p class="text-lg text-gray-600">See what our students have achieved</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            @foreach($testimonials->take(6) as $testimonial)
            <div class="bg-gray-50 rounded-lg p-6">
                <div class="flex items-center mb-4">
                    @if($testimonial->avatar)
                        <img src="{{ $testimonial->avatar }}" alt="{{ $testimonial->name }}" class="w-12 h-12 rounded-full mr-4">
                    @else
                        <div class="w-12 h-12 bg-gray-300 rounded-full mr-4 flex items-center justify-center">
                            <span class="text-gray-600 font-medium">{{ substr($testimonial->name, 0, 1) }}</span>
                        </div>
                    @endif
                    <div>
                        <h4 class="font-semibold">{{ $testimonial->name }}</h4>
                        @if($testimonial->achievement)
                            <p class="text-sm text-green-600">{{ $testimonial->achievement }}</p>
                        @endif
                    </div>
                </div>
                <p class="text-gray-700">{{ $testimonial->content }}</p>
                <div class="flex mt-4">
                    @for($i = 1; $i <= 5; $i++)
                        <svg class="w-4 h-4 {{ $i <= $testimonial->rating ? 'text-yellow-400' : 'text-gray-300' }}" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                    @endfor
                </div>
            </div>
            @endforeach
        </div>
    </div>
</div>
@endif

<!-- CTA Section -->
<div class="bg-blue-600 text-white py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl font-bold mb-4">Ready to Transform Your Life?</h2>
        <p class="text-xl mb-8">Join thousands of successful students today</p>
        <a href="{{ route('register') }}" class="bg-yellow-400 hover:bg-yellow-500 text-black font-bold py-3 px-8 rounded-lg text-lg transition duration-300">
            Start Your Journey Now
        </a>
    </div>
</div>
@endsection
