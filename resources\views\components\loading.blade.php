@props([
    'type' => 'spinner',
    'size' => 'md',
    'color' => 'primary',
    'text' => null,
    'overlay' => false
])

@php
$sizeClasses = [
    'xs' => 'w-3 h-3',
    'sm' => 'w-4 h-4',
    'md' => 'w-6 h-6',
    'lg' => 'w-8 h-8',
    'xl' => 'w-12 h-12',
];

$colorClasses = [
    'primary' => 'text-blue-600',
    'secondary' => 'text-gray-600',
    'success' => 'text-green-600',
    'warning' => 'text-yellow-600',
    'danger' => 'text-red-600',
    'white' => 'text-white',
];

$spinnerClasses = $sizeClasses[$size] . ' ' . $colorClasses[$color];
@endphp

@if($overlay)
    <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 flex flex-col items-center space-y-4">
@endif

<div {{ $attributes->merge(['class' => 'flex items-center justify-center']) }}>
    @if($type === 'spinner')
        <svg class="{{ $spinnerClasses }} animate-spin" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
    @elseif($type === 'dots')
        <div class="flex space-x-1">
            <div class="{{ $sizeClasses[$size] }} {{ $colorClasses[$color] }} bg-current rounded-full animate-bounce" style="animation-delay: -0.32s"></div>
            <div class="{{ $sizeClasses[$size] }} {{ $colorClasses[$color] }} bg-current rounded-full animate-bounce" style="animation-delay: -0.16s"></div>
            <div class="{{ $sizeClasses[$size] }} {{ $colorClasses[$color] }} bg-current rounded-full animate-bounce"></div>
        </div>
    @elseif($type === 'pulse')
        <div class="{{ $spinnerClasses }} bg-current rounded-full animate-pulse"></div>
    @elseif($type === 'bars')
        <div class="flex space-x-1">
            <div class="w-1 {{ str_replace('w-', 'h-', $sizeClasses[$size]) }} {{ $colorClasses[$color] }} bg-current animate-pulse" style="animation-delay: -0.4s"></div>
            <div class="w-1 {{ str_replace('w-', 'h-', $sizeClasses[$size]) }} {{ $colorClasses[$color] }} bg-current animate-pulse" style="animation-delay: -0.2s"></div>
            <div class="w-1 {{ str_replace('w-', 'h-', $sizeClasses[$size]) }} {{ $colorClasses[$color] }} bg-current animate-pulse"></div>
            <div class="w-1 {{ str_replace('w-', 'h-', $sizeClasses[$size]) }} {{ $colorClasses[$color] }} bg-current animate-pulse" style="animation-delay: -0.2s"></div>
            <div class="w-1 {{ str_replace('w-', 'h-', $sizeClasses[$size]) }} {{ $colorClasses[$color] }} bg-current animate-pulse" style="animation-delay: -0.4s"></div>
        </div>
    @elseif($type === 'ring')
        <div class="{{ $spinnerClasses }} border-4 border-gray-200 border-t-current rounded-full animate-spin"></div>
    @endif
    
    @if($text)
        <span class="ml-3 text-sm font-medium {{ $colorClasses[$color] }}">{{ $text }}</span>
    @endif
</div>

@if($overlay)
        </div>
    </div>
@endif

<style>
@keyframes bounce {
    0%, 80%, 100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1);
    }
}

.animate-bounce {
    animation: bounce 1.4s ease-in-out infinite both;
}
</style>
