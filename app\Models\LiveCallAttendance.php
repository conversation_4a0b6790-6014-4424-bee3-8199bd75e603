<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LiveCallAttendance extends Model
{
    use HasFactory;

    protected $fillable = [
        'live_call_id',
        'user_id',
        'registered_at',
        'joined_at',
        'left_at',
        'duration_minutes',
        'status',
    ];

    protected $casts = [
        'registered_at' => 'datetime',
        'joined_at' => 'datetime',
        'left_at' => 'datetime',
    ];

    /**
     * Get the live call that owns the attendance.
     */
    public function liveCall()
    {
        return $this->belongsTo(LiveCall::class);
    }

    /**
     * Get the user that owns the attendance.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Mark as attended.
     */
    public function markAsAttended()
    {
        $this->update([
            'joined_at' => now(),
            'status' => 'attended',
        ]);
    }

    /**
     * Mark as left.
     */
    public function markAsLeft()
    {
        $joinedAt = $this->joined_at ?? now();
        $leftAt = now();
        $duration = $leftAt->diffInMinutes($joinedAt);

        $this->update([
            'left_at' => $leftAt,
            'duration_minutes' => $duration,
        ]);
    }

    /**
     * Mark as missed.
     */
    public function markAsMissed()
    {
        $this->update(['status' => 'missed']);
    }

    /**
     * Get formatted duration.
     */
    public function getFormattedDurationAttribute(): string
    {
        if (!$this->duration_minutes) {
            return '0m';
        }

        $hours = floor($this->duration_minutes / 60);
        $minutes = $this->duration_minutes % 60;

        if ($hours > 0) {
            return $hours . 'h ' . $minutes . 'm';
        }

        return $minutes . 'm';
    }

    /**
     * Scope to get attended records.
     */
    public function scopeAttended($query)
    {
        return $query->where('status', 'attended');
    }

    /**
     * Scope to get missed records.
     */
    public function scopeMissed($query)
    {
        return $query->where('status', 'missed');
    }
}
