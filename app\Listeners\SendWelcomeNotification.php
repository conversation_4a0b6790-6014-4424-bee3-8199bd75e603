<?php

namespace App\Listeners;

use App\Events\UserRegistered;
use App\Notifications\WelcomeNotification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class SendWelcomeNotification implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(UserRegistered $event): void
    {
        // Send welcome notification to the newly registered user
        $event->user->notify(new WelcomeNotification());
    }

    /**
     * Handle a job failure.
     */
    public function failed(UserRegistered $event, $exception)
    {
        // Log the failure or handle it appropriately
        \Log::error('Failed to send welcome notification', [
            'user_id' => $event->user->id,
            'error' => $exception->getMessage(),
        ]);
    }
}
