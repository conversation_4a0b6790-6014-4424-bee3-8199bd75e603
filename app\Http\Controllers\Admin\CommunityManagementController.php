<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\CommunityPost;
use App\Models\CommunityComment;
use App\Models\User;
use Illuminate\Http\Request;

class CommunityManagementController extends Controller
{
    /**
     * Display community overview.
     */
    public function index()
    {
        $stats = [
            'total_posts' => CommunityPost::count(),
            'total_comments' => CommunityComment::count(),
            'active_users' => User::whereHas('communityPosts', function($q) {
                $q->where('created_at', '>=', now()->subDays(30));
            })->count(),
            'pending_posts' => CommunityPost::where('status', 'pending')->count(),
            'reported_posts' => CommunityPost::where('is_reported', true)->count(),
            'reported_comments' => CommunityComment::where('is_reported', true)->count(),
        ];

        $recentPosts = CommunityPost::with(['user', 'category'])
            ->latest()
            ->limit(10)
            ->get();

        $recentComments = CommunityComment::with(['user', 'post'])
            ->latest()
            ->limit(10)
            ->get();

        return view('admin.community.index', compact('stats', 'recentPosts', 'recentComments'));
    }

    /**
     * Display posts management.
     */
    public function posts(Request $request)
    {
        $query = CommunityPost::with(['user', 'category']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%")
                  ->orWhereHas('user', function ($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by category
        if ($request->filled('category')) {
            $query->where('category_id', $request->category);
        }

        // Filter reported posts
        if ($request->filled('reported') && $request->reported === '1') {
            $query->where('is_reported', true);
        }

        $posts = $query->latest()->paginate(20);

        return view('admin.community.posts', compact('posts'));
    }

    /**
     * Display comments management.
     */
    public function comments(Request $request)
    {
        $query = CommunityComment::with(['user', 'post']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('content', 'like', "%{$search}%")
                  ->orWhereHas('user', function ($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Filter reported comments
        if ($request->filled('reported') && $request->reported === '1') {
            $query->where('is_reported', true);
        }

        $comments = $query->latest()->paginate(20);

        return view('admin.community.comments', compact('comments'));
    }

    /**
     * Approve a post.
     */
    public function approvePost(CommunityPost $post)
    {
        $post->update(['status' => 'approved']);
        return back()->with('success', 'Post approved successfully.');
    }

    /**
     * Reject a post.
     */
    public function rejectPost(CommunityPost $post)
    {
        $post->update(['status' => 'rejected']);
        return back()->with('success', 'Post rejected successfully.');
    }

    /**
     * Delete a post.
     */
    public function deletePost(CommunityPost $post)
    {
        $post->delete();
        return back()->with('success', 'Post deleted successfully.');
    }

    /**
     * Pin/unpin a post.
     */
    public function togglePinPost(CommunityPost $post)
    {
        $post->update(['is_pinned' => !$post->is_pinned]);
        $status = $post->is_pinned ? 'pinned' : 'unpinned';
        return back()->with('success', "Post {$status} successfully.");
    }

    /**
     * Feature/unfeature a post.
     */
    public function toggleFeaturePost(CommunityPost $post)
    {
        $post->update(['is_featured' => !$post->is_featured]);
        $status = $post->is_featured ? 'featured' : 'unfeatured';
        return back()->with('success', "Post {$status} successfully.");
    }

    /**
     * Delete a comment.
     */
    public function deleteComment(CommunityComment $comment)
    {
        $comment->delete();
        return back()->with('success', 'Comment deleted successfully.');
    }

    /**
     * Bulk actions for posts.
     */
    public function bulkActionPosts(Request $request)
    {
        $request->validate([
            'action' => 'required|in:approve,reject,delete,pin,unpin,feature,unfeature',
            'post_ids' => 'required|array',
            'post_ids.*' => 'exists:community_posts,id',
        ]);

        $postIds = $request->post_ids;
        $action = $request->action;

        switch ($action) {
            case 'approve':
                CommunityPost::whereIn('id', $postIds)->update(['status' => 'approved']);
                $message = 'Posts approved successfully.';
                break;
            case 'reject':
                CommunityPost::whereIn('id', $postIds)->update(['status' => 'rejected']);
                $message = 'Posts rejected successfully.';
                break;
            case 'delete':
                CommunityPost::whereIn('id', $postIds)->delete();
                $message = 'Posts deleted successfully.';
                break;
            case 'pin':
                CommunityPost::whereIn('id', $postIds)->update(['is_pinned' => true]);
                $message = 'Posts pinned successfully.';
                break;
            case 'unpin':
                CommunityPost::whereIn('id', $postIds)->update(['is_pinned' => false]);
                $message = 'Posts unpinned successfully.';
                break;
            case 'feature':
                CommunityPost::whereIn('id', $postIds)->update(['is_featured' => true]);
                $message = 'Posts featured successfully.';
                break;
            case 'unfeature':
                CommunityPost::whereIn('id', $postIds)->update(['is_featured' => false]);
                $message = 'Posts unfeatured successfully.';
                break;
        }

        return back()->with('success', $message);
    }

    /**
     * Bulk actions for comments.
     */
    public function bulkActionComments(Request $request)
    {
        $request->validate([
            'action' => 'required|in:delete',
            'comment_ids' => 'required|array',
            'comment_ids.*' => 'exists:community_comments,id',
        ]);

        $commentIds = $request->comment_ids;
        $action = $request->action;

        switch ($action) {
            case 'delete':
                CommunityComment::whereIn('id', $commentIds)->delete();
                $message = 'Comments deleted successfully.';
                break;
        }

        return back()->with('success', $message);
    }

    /**
     * Get community analytics.
     */
    public function analytics(Request $request)
    {
        $period = $request->get('period', '30'); // days
        $startDate = now()->subDays($period);

        // Posts over time
        $postsData = CommunityPost::where('created_at', '>=', $startDate)
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // Comments over time
        $commentsData = CommunityComment::where('created_at', '>=', $startDate)
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // Top contributors
        $topContributors = User::withCount(['communityPosts', 'communityComments'])
            ->having('community_posts_count', '>', 0)
            ->orderBy('community_posts_count', 'desc')
            ->limit(10)
            ->get();

        // Popular posts
        $popularPosts = CommunityPost::withCount(['likes', 'comments'])
            ->where('created_at', '>=', $startDate)
            ->orderBy('likes_count', 'desc')
            ->limit(10)
            ->get();

        return view('admin.community.analytics', compact(
            'postsData', 'commentsData', 'topContributors', 'popularPosts', 'period'
        ));
    }
}
