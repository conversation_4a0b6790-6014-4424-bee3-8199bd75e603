@extends('layouts.app')

@section('content')
<div class="py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Back Button -->
        <div class="mb-6">
            <a href="{{ route('live-calls.index') }}" class="inline-flex items-center text-blue-600 hover:text-blue-800">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
                Back to Live Calls
            </a>
        </div>

        <!-- Live Call Header -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden mb-8">
            <!-- Status Banner -->
            @if($liveCall->is_live)
                <div class="bg-red-600 text-white p-4 text-center">
                    <div class="flex items-center justify-center">
                        <div class="w-3 h-3 bg-white rounded-full mr-2 animate-pulse"></div>
                        <span class="font-bold">LIVE NOW - Join the call!</span>
                    </div>
                </div>
            @elseif($liveCall->is_upcoming)
                <div class="bg-blue-600 text-white p-4 text-center">
                    <span class="font-bold">Upcoming Live Call</span>
                </div>
            @else
                <div class="bg-gray-600 text-white p-4 text-center">
                    <span class="font-bold">Completed Live Call</span>
                    @if($liveCall->recording_url)
                        <span class="ml-4">Recording Available</span>
                    @endif
                </div>
            @endif

            <div class="p-8">
                <!-- Title and Basic Info -->
                <div class="mb-6">
                    <h1 class="text-3xl font-bold text-gray-900 mb-4">{{ $liveCall->title }}</h1>
                    
                    <div class="flex flex-wrap items-center gap-6 text-gray-600 mb-4">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            {{ $liveCall->scheduled_at->format('F j, Y') }}
                        </div>
                        <div class="flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            {{ $liveCall->scheduled_at->format('g:i A T') }}
                        </div>
                        <div class="flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                            {{ $liveCall->formatted_duration }}
                        </div>
                    </div>

                    <!-- Mentor Info -->
                    <div class="flex items-center mb-6">
                        @if($liveCall->mentor->avatar)
                            <img src="{{ $liveCall->mentor->avatar }}" alt="{{ $liveCall->mentor->name }}" class="w-12 h-12 rounded-full mr-4">
                        @else
                            <div class="w-12 h-12 bg-gray-300 rounded-full mr-4 flex items-center justify-center">
                                <span class="text-gray-600 font-medium">{{ substr($liveCall->mentor->name, 0, 1) }}</span>
                            </div>
                        @endif
                        <div>
                            <p class="font-medium text-gray-900">{{ $liveCall->mentor->name }}</p>
                            <p class="text-sm text-gray-600">Host</p>
                        </div>
                    </div>
                </div>

                <!-- Description -->
                <div class="mb-8">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">About This Call</h2>
                    <div class="prose prose-gray max-w-none">
                        {!! nl2br(e($liveCall->description)) !!}
                    </div>
                </div>

                <!-- Call Details -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h3 class="font-medium text-gray-900 mb-2">Attendance</h3>
                        <p class="text-2xl font-bold text-blue-600">{{ $liveCall->attendance_count }}</p>
                        <p class="text-sm text-gray-600">
                            @if($liveCall->max_attendees)
                                of {{ $liveCall->max_attendees }} max attendees
                            @else
                                registered attendees
                            @endif
                        </p>
                    </div>
                    
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h3 class="font-medium text-gray-900 mb-2">Recording</h3>
                        <p class="text-sm text-gray-600">
                            @if($liveCall->is_recorded)
                                ✅ This call will be recorded
                            @else
                                ❌ This call will not be recorded
                            @endif
                        </p>
                        @if($liveCall->recording_url)
                            <a href="{{ $liveCall->recording_url }}" target="_blank" 
                               class="inline-block mt-2 text-blue-600 hover:text-blue-800 text-sm font-medium">
                                Watch Recording →
                            </a>
                        @endif
                    </div>
                </div>

                <!-- Required Plans -->
                @if($liveCall->required_plans)
                    <div class="mb-8">
                        <h3 class="font-medium text-gray-900 mb-2">Required Subscription Plans</h3>
                        <div class="flex flex-wrap gap-2">
                            @foreach($liveCall->required_plans as $plan)
                                <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                                    {{ ucfirst($plan) }}
                                </span>
                            @endforeach
                        </div>
                    </div>
                @endif

                <!-- Action Buttons -->
                <div class="border-t pt-6">
                    @auth
                        @if($canAccess)
                            @if($liveCall->is_live && $isRegistered)
                                <!-- Join Live Call -->
                                <div class="text-center">
                                    <a href="{{ route('live-calls.join', $liveCall) }}" 
                                       class="inline-block bg-red-600 hover:bg-red-700 text-white px-8 py-3 rounded-lg font-bold text-lg transition duration-300">
                                        🔴 Join Live Call
                                    </a>
                                    <p class="text-sm text-gray-600 mt-2">Call is live now!</p>
                                </div>
                            @elseif($liveCall->is_upcoming)
                                @if($isRegistered)
                                    <!-- Already Registered -->
                                    <div class="text-center">
                                        <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                                            <div class="flex items-center justify-center">
                                                <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                </svg>
                                                <span class="text-green-800 font-medium">You're registered for this call!</span>
                                            </div>
                                            <p class="text-green-700 text-sm mt-2">
                                                You'll receive a reminder email before the call starts.
                                            </p>
                                        </div>
                                        
                                        <form method="POST" action="{{ route('live-calls.unregister', $liveCall) }}" class="inline">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" 
                                                    class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg font-medium transition duration-300"
                                                    onclick="return confirm('Are you sure you want to unregister from this call?')">
                                                Unregister
                                            </button>
                                        </form>
                                    </div>
                                @else
                                    <!-- Register for Call -->
                                    <div class="text-center">
                                        @if($liveCall->max_attendees && $liveCall->attendance_count >= $liveCall->max_attendees)
                                            <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                                                <p class="text-red-800 font-medium">This call is full</p>
                                                <p class="text-red-700 text-sm mt-1">Maximum attendees reached</p>
                                            </div>
                                        @else
                                            <form method="POST" action="{{ route('live-calls.register', $liveCall) }}">
                                                @csrf
                                                <button type="submit" 
                                                        class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-bold text-lg transition duration-300">
                                                    Register for Call
                                                </button>
                                            </form>
                                            <p class="text-sm text-gray-600 mt-2">Free for your subscription plan</p>
                                        @endif
                                    </div>
                                @endif
                            @else
                                <!-- Call Completed -->
                                <div class="text-center">
                                    @if($liveCall->recording_url)
                                        <a href="{{ $liveCall->recording_url }}" target="_blank"
                                           class="inline-block bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-lg font-bold text-lg transition duration-300">
                                            Watch Recording
                                        </a>
                                    @else
                                        <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                                            <p class="text-gray-800 font-medium">This call has ended</p>
                                            @if($liveCall->is_recorded)
                                                <p class="text-gray-600 text-sm mt-1">Recording will be available soon</p>
                                            @endif
                                        </div>
                                    @endif
                                </div>
                            @endif
                        @else
                            <!-- No Access -->
                            <div class="text-center">
                                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
                                    <div class="flex items-center justify-center">
                                        <svg class="w-5 h-5 text-yellow-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                        </svg>
                                        <span class="text-yellow-800 font-medium">Subscription Required</span>
                                    </div>
                                    <p class="text-yellow-700 text-sm mt-2">
                                        You need an active subscription to access this live call.
                                    </p>
                                </div>
                                
                                <a href="{{ route('subscriptions.index') }}" 
                                   class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-bold text-lg transition duration-300">
                                    View Subscription Plans
                                </a>
                            </div>
                        @endif
                    @else
                        <!-- Not Logged In -->
                        <div class="text-center">
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                                <p class="text-blue-800 font-medium">Sign in to register for this call</p>
                            </div>
                            
                            <div class="space-x-4">
                                <a href="{{ route('login') }}" 
                                   class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition duration-300">
                                    Sign In
                                </a>
                                <a href="{{ route('register') }}" 
                                   class="border border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white px-6 py-3 rounded-lg font-medium transition duration-300">
                                    Sign Up
                                </a>
                            </div>
                        </div>
                    @endauth
                </div>
            </div>
        </div>

        <!-- Meeting Details (for registered users) -->
        @auth
            @if($isRegistered && $liveCall->is_upcoming)
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                    <h3 class="font-semibold text-blue-900 mb-4">Meeting Details</h3>
                    <div class="space-y-2 text-sm">
                        @if($liveCall->meeting_id)
                            <p><span class="font-medium">Meeting ID:</span> {{ $liveCall->meeting_id }}</p>
                        @endif
                        @if($liveCall->meeting_password)
                            <p><span class="font-medium">Password:</span> {{ $liveCall->meeting_password }}</p>
                        @endif
                        <p class="text-blue-700 mt-4">
                            💡 You'll receive an email reminder 30 minutes before the call starts with the direct join link.
                        </p>
                    </div>
                </div>
            @endif
        @endauth
    </div>
</div>
@endsection
