#!/bin/bash

# The Real World LMS Deployment Script
# This script automates the deployment process to Hostinger Business hosting

set -e  # Exit on any error

# Configuration
PROJECT_NAME="realworld-lms"
DOMAIN="yourdomain.com"
BACKUP_DIR="/home/<USER>"
DEPLOY_DIR="/public_html"
TEMP_DIR="/tmp/deployment"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

# Check if running as correct user
check_user() {
    if [[ $EUID -eq 0 ]]; then
        error "This script should not be run as root"
    fi
}

# Check system requirements
check_requirements() {
    log "Checking system requirements..."
    
    # Check PHP version
    PHP_VERSION=$(php -r "echo PHP_VERSION;")
    if [[ $(echo "$PHP_VERSION" | cut -d. -f1) -lt 8 ]]; then
        error "PHP 8.2+ is required. Current version: $PHP_VERSION"
    fi
    
    # Check required PHP extensions
    REQUIRED_EXTENSIONS=("pdo" "mbstring" "tokenizer" "xml" "ctype" "json" "bcmath" "curl" "fileinfo" "openssl" "zip" "gd" "redis")
    
    for ext in "${REQUIRED_EXTENSIONS[@]}"; do
        if ! php -m | grep -q "^$ext$"; then
            error "Required PHP extension '$ext' is not installed"
        fi
    done
    
    # Check Composer
    if ! command -v composer &> /dev/null; then
        error "Composer is not installed"
    fi
    
    # Check Node.js and npm
    if ! command -v node &> /dev/null; then
        error "Node.js is not installed"
    fi
    
    if ! command -v npm &> /dev/null; then
        error "npm is not installed"
    fi
    
    log "System requirements check passed"
}

# Create backup of current deployment
create_backup() {
    log "Creating backup of current deployment..."
    
    if [ -d "$DEPLOY_DIR" ]; then
        BACKUP_NAME="backup_$(date +%Y%m%d_%H%M%S)"
        mkdir -p "$BACKUP_DIR"
        
        # Backup files
        tar -czf "$BACKUP_DIR/${BACKUP_NAME}_files.tar.gz" -C "$DEPLOY_DIR" . 2>/dev/null || true
        
        # Backup database
        if [ ! -z "$DB_DATABASE" ]; then
            mysqldump -u "$DB_USERNAME" -p"$DB_PASSWORD" "$DB_DATABASE" > "$BACKUP_DIR/${BACKUP_NAME}_database.sql"
            gzip "$BACKUP_DIR/${BACKUP_NAME}_database.sql"
        fi
        
        log "Backup created: $BACKUP_NAME"
    else
        info "No existing deployment found, skipping backup"
    fi
}

# Download and prepare application
prepare_application() {
    log "Preparing application for deployment..."
    
    # Create temporary directory
    rm -rf "$TEMP_DIR"
    mkdir -p "$TEMP_DIR"
    cd "$TEMP_DIR"
    
    # Clone or copy application files
    if [ -n "$GIT_REPOSITORY" ]; then
        git clone "$GIT_REPOSITORY" .
        git checkout "$GIT_BRANCH"
    else
        # Copy from current directory if running locally
        cp -r /path/to/your/project/* .
    fi
    
    # Install PHP dependencies
    log "Installing PHP dependencies..."
    composer install --no-dev --optimize-autoloader --no-interaction
    
    # Install Node.js dependencies and build assets
    log "Building frontend assets..."
    npm ci --only=production
    npm run build
    
    # Set up environment file
    if [ -f ".env.production" ]; then
        cp .env.production .env
    else
        error ".env.production file not found"
    fi
    
    # Generate application key if not set
    if grep -q "APP_KEY=base64:GENERATE_NEW_KEY_FOR_PRODUCTION" .env; then
        php artisan key:generate --force
    fi
    
    log "Application prepared successfully"
}

# Deploy application
deploy_application() {
    log "Deploying application..."
    
    # Create deployment directory if it doesn't exist
    mkdir -p "$DEPLOY_DIR"
    
    # Stop any running processes (if applicable)
    # systemctl stop nginx || true
    # systemctl stop php-fpm || true
    
    # Sync files to deployment directory
    rsync -av --delete \
        --exclude='.git' \
        --exclude='node_modules' \
        --exclude='tests' \
        --exclude='storage/logs/*' \
        --exclude='storage/framework/cache/*' \
        --exclude='storage/framework/sessions/*' \
        --exclude='storage/framework/views/*' \
        "$TEMP_DIR/" "$DEPLOY_DIR/"
    
    cd "$DEPLOY_DIR"
    
    # Set proper permissions
    log "Setting file permissions..."
    find . -type f -exec chmod 644 {} \;
    find . -type d -exec chmod 755 {} \;
    chmod -R 775 storage bootstrap/cache
    
    # Create symbolic links for storage
    php artisan storage:link --force
    
    log "Application deployed successfully"
}

# Run database migrations
run_migrations() {
    log "Running database migrations..."
    
    cd "$DEPLOY_DIR"
    
    # Run migrations
    php artisan migrate --force
    
    # Seed database if needed (only on first deployment)
    if [ "$SEED_DATABASE" = "true" ]; then
        php artisan db:seed --force
    fi
    
    log "Database migrations completed"
}

# Optimize application
optimize_application() {
    log "Optimizing application for production..."
    
    cd "$DEPLOY_DIR"
    
    # Clear and cache configuration
    php artisan config:cache
    php artisan route:cache
    php artisan view:cache
    php artisan event:cache
    
    # Optimize Composer autoloader
    composer dump-autoload --optimize
    
    # Clear application cache
    php artisan cache:clear
    
    log "Application optimization completed"
}

# Configure web server
configure_webserver() {
    log "Configuring web server..."
    
    # Create .htaccess for Apache (Hostinger uses Apache)
    cat > "$DEPLOY_DIR/.htaccess" << 'EOF'
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Handle Angular and Vue.js routes
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule ^(.*)$ index.php [QSA,L]
    
    # Security headers
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https:;"
    
    # HSTS (uncomment when SSL is configured)
    # Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
    
    # Prevent access to sensitive files
    <FilesMatch "\.(env|log|md)$">
        Order allow,deny
        Deny from all
    </FilesMatch>
    
    # Prevent access to vendor and storage directories
    RedirectMatch 403 ^/vendor/.*$
    RedirectMatch 403 ^/storage/.*$
    
    # Enable compression
    <IfModule mod_deflate.c>
        AddOutputFilterByType DEFLATE text/plain
        AddOutputFilterByType DEFLATE text/html
        AddOutputFilterByType DEFLATE text/xml
        AddOutputFilterByType DEFLATE text/css
        AddOutputFilterByType DEFLATE application/xml
        AddOutputFilterByType DEFLATE application/xhtml+xml
        AddOutputFilterByType DEFLATE application/rss+xml
        AddOutputFilterByType DEFLATE application/javascript
        AddOutputFilterByType DEFLATE application/x-javascript
    </IfModule>
    
    # Set cache headers
    <IfModule mod_expires.c>
        ExpiresActive on
        ExpiresByType text/css "access plus 1 year"
        ExpiresByType application/javascript "access plus 1 year"
        ExpiresByType image/png "access plus 1 year"
        ExpiresByType image/jpg "access plus 1 year"
        ExpiresByType image/jpeg "access plus 1 year"
        ExpiresByType image/gif "access plus 1 year"
        ExpiresByType image/svg+xml "access plus 1 year"
    </IfModule>
</IfModule>
EOF
    
    log "Web server configuration completed"
}

# Setup SSL certificate
setup_ssl() {
    log "Setting up SSL certificate..."
    
    # For Hostinger, SSL is usually managed through the control panel
    # This is a placeholder for SSL setup instructions
    
    info "Please configure SSL certificate through Hostinger control panel:"
    info "1. Go to SSL section in hPanel"
    info "2. Enable SSL for your domain"
    info "3. Update APP_URL in .env to use https://"
    
    log "SSL setup instructions provided"
}

# Setup monitoring and logging
setup_monitoring() {
    log "Setting up monitoring and logging..."
    
    cd "$DEPLOY_DIR"
    
    # Create log rotation configuration
    cat > "/etc/logrotate.d/$PROJECT_NAME" << EOF
$DEPLOY_DIR/storage/logs/*.log {
    daily
    missingok
    rotate 14
    compress
    notifempty
    create 644 www-data www-data
    postrotate
        php $DEPLOY_DIR/artisan cache:clear > /dev/null 2>&1 || true
    endscript
}
EOF
    
    # Setup cron jobs
    (crontab -l 2>/dev/null; echo "* * * * * cd $DEPLOY_DIR && php artisan schedule:run >> /dev/null 2>&1") | crontab -
    
    log "Monitoring and logging setup completed"
}

# Cleanup
cleanup() {
    log "Cleaning up temporary files..."
    rm -rf "$TEMP_DIR"
    log "Cleanup completed"
}

# Health check
health_check() {
    log "Performing health check..."
    
    # Check if application is responding
    HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "http://$DOMAIN" || echo "000")
    
    if [ "$HTTP_STATUS" = "200" ]; then
        log "Health check passed - Application is responding"
    else
        warning "Health check failed - HTTP status: $HTTP_STATUS"
    fi
}

# Main deployment function
main() {
    log "Starting deployment of $PROJECT_NAME to $DOMAIN"
    
    check_user
    check_requirements
    create_backup
    prepare_application
    deploy_application
    run_migrations
    optimize_application
    configure_webserver
    setup_ssl
    setup_monitoring
    cleanup
    health_check
    
    log "Deployment completed successfully!"
    info "Your application is now live at: https://$DOMAIN"
    info "Admin panel: https://$DOMAIN/admin"
}

# Handle script arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "backup")
        create_backup
        ;;
    "optimize")
        optimize_application
        ;;
    "health")
        health_check
        ;;
    *)
        echo "Usage: $0 {deploy|backup|optimize|health}"
        exit 1
        ;;
esac
