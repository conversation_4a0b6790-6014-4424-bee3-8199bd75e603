<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Role;
use App\Models\UserSubscription;
use App\Models\UserProgress;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;

class UserManagementController extends Controller
{
    /**
     * Display a listing of users.
     */
    public function index(Request $request)
    {
        $query = User::with(['roles', 'activeSubscription.subscriptionPlan']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('first_name', 'like', "%{$search}%")
                  ->orWhere('last_name', 'like', "%{$search}%");
            });
        }

        // Filter by role
        if ($request->filled('role')) {
            $query->whereHas('roles', function ($q) use ($request) {
                $q->where('name', $request->role);
            });
        }

        // Filter by subscription status
        if ($request->filled('subscription')) {
            if ($request->subscription === 'active') {
                $query->whereHas('activeSubscription');
            } elseif ($request->subscription === 'inactive') {
                $query->whereDoesntHave('activeSubscription');
            }
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        // Sort
        $sortBy = $request->get('sort', 'created_at');
        $sortDirection = $request->get('direction', 'desc');
        $query->orderBy($sortBy, $sortDirection);

        $users = $query->paginate(20);
        $roles = Role::all();

        return view('admin.users.index', compact('users', 'roles'));
    }

    /**
     * Show the form for creating a new user.
     */
    public function create()
    {
        $roles = Role::all();
        return view('admin.users.create', compact('roles'));
    }

    /**
     * Store a newly created user.
     */
    public function store(Request $request)
    {
        $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'roles' => 'array',
            'roles.*' => 'exists:roles,id',
            'is_active' => 'boolean',
        ]);

        $user = User::create([
            'first_name' => $request->first_name,
            'last_name' => $request->last_name,
            'name' => $request->first_name . ' ' . $request->last_name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'is_active' => $request->boolean('is_active', true),
            'email_verified_at' => now(),
        ]);

        if ($request->filled('roles')) {
            $user->roles()->sync($request->roles);
        }

        return redirect()->route('admin.users.index')
            ->with('success', 'User created successfully.');
    }

    /**
     * Display the specified user.
     */
    public function show(User $user)
    {
        $user->load([
            'roles',
            'subscriptions.subscriptionPlan',
            'progress.course',
            'communityPosts',
            'liveCallAttendances.liveCall'
        ]);

        // User statistics
        $stats = [
            'total_courses_enrolled' => $user->progress()->distinct('course_id')->count(),
            'completed_lessons' => $user->progress()->where('is_completed', true)->count(),
            'total_watch_time' => $user->progress()->sum('watch_time_seconds'),
            'community_posts' => $user->communityPosts()->count(),
            'live_calls_attended' => $user->liveCallAttendances()->where('status', 'attended')->count(),
        ];

        // Recent activity
        $recentActivity = [
            'lessons' => $user->progress()->with('course')->latest()->take(5)->get(),
            'posts' => $user->communityPosts()->latest()->take(5)->get(),
            'calls' => $user->liveCallAttendances()->with('liveCall')->latest()->take(5)->get(),
        ];

        return view('admin.users.show', compact('user', 'stats', 'recentActivity'));
    }

    /**
     * Show the form for editing the user.
     */
    public function edit(User $user)
    {
        $roles = Role::all();
        $user->load('roles');
        return view('admin.users.edit', compact('user', 'roles'));
    }

    /**
     * Update the specified user.
     */
    public function update(Request $request, User $user)
    {
        $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
            'password' => 'nullable|string|min:8|confirmed',
            'roles' => 'array',
            'roles.*' => 'exists:roles,id',
            'is_active' => 'boolean',
        ]);

        $updateData = [
            'first_name' => $request->first_name,
            'last_name' => $request->last_name,
            'name' => $request->first_name . ' ' . $request->last_name,
            'email' => $request->email,
            'is_active' => $request->boolean('is_active'),
        ];

        if ($request->filled('password')) {
            $updateData['password'] = Hash::make($request->password);
        }

        $user->update($updateData);

        if ($request->has('roles')) {
            $user->roles()->sync($request->roles);
        }

        return redirect()->route('admin.users.show', $user)
            ->with('success', 'User updated successfully.');
    }

    /**
     * Remove the specified user.
     */
    public function destroy(User $user)
    {
        // Prevent deletion of the current admin user
        if ($user->id === auth()->id()) {
            return back()->withErrors(['error' => 'You cannot delete your own account.']);
        }

        // Prevent deletion of super admin
        if ($user->hasRole('super-admin')) {
            return back()->withErrors(['error' => 'Super admin cannot be deleted.']);
        }

        $user->delete();

        return redirect()->route('admin.users.index')
            ->with('success', 'User deleted successfully.');
    }

    /**
     * Toggle user active status.
     */
    public function toggleStatus(User $user)
    {
        $user->update(['is_active' => !$user->is_active]);

        $status = $user->is_active ? 'activated' : 'deactivated';
        return back()->with('success', "User {$status} successfully.");
    }

    /**
     * Impersonate a user.
     */
    public function impersonate(User $user)
    {
        if ($user->hasRole('super-admin')) {
            return back()->withErrors(['error' => 'Cannot impersonate super admin.']);
        }

        session(['impersonating' => auth()->id()]);
        auth()->login($user);

        return redirect()->route('dashboard')
            ->with('success', "Now impersonating {$user->name}. Click 'Stop Impersonating' to return.");
    }

    /**
     * Stop impersonating.
     */
    public function stopImpersonating()
    {
        if (!session('impersonating')) {
            return redirect()->route('admin.dashboard');
        }

        $originalUserId = session('impersonating');
        session()->forget('impersonating');

        auth()->loginUsingId($originalUserId);

        return redirect()->route('admin.users.index')
            ->with('success', 'Stopped impersonating user.');
    }

    /**
     * Export users to CSV.
     */
    public function export(Request $request)
    {
        $query = User::with(['roles', 'activeSubscription.subscriptionPlan']);

        // Apply same filters as index
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        if ($request->filled('role')) {
            $query->whereHas('roles', function ($q) use ($request) {
                $q->where('name', $request->role);
            });
        }

        $users = $query->get();

        $filename = 'users_export_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function() use ($users) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'ID', 'Name', 'Email', 'Roles', 'Subscription', 'Status',
                'Email Verified', 'Created At', 'Last Login'
            ]);

            foreach ($users as $user) {
                fputcsv($file, [
                    $user->id,
                    $user->name,
                    $user->email,
                    $user->roles->pluck('name')->join(', '),
                    $user->activeSubscription ? $user->activeSubscription->subscriptionPlan->name : 'None',
                    $user->is_active ? 'Active' : 'Inactive',
                    $user->email_verified_at ? 'Yes' : 'No',
                    $user->created_at->format('Y-m-d H:i:s'),
                    $user->last_login_at ? $user->last_login_at->format('Y-m-d H:i:s') : 'Never'
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Perform bulk actions on users.
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:activate,deactivate,delete,assign_role',
            'user_ids' => 'required|array',
            'user_ids.*' => 'exists:users,id',
            'role' => 'required_if:action,assign_role|exists:roles,name',
        ]);

        $userIds = $request->user_ids;
        $action = $request->action;

        // Prevent actions on current user
        if (in_array(auth()->id(), $userIds)) {
            return back()->withErrors(['error' => 'Cannot perform bulk actions on your own account.']);
        }

        switch ($action) {
            case 'activate':
                User::whereIn('id', $userIds)->update(['is_active' => true]);
                $message = 'Users activated successfully.';
                break;
            case 'deactivate':
                User::whereIn('id', $userIds)->update(['is_active' => false]);
                $message = 'Users deactivated successfully.';
                break;
            case 'delete':
                User::whereIn('id', $userIds)->delete();
                $message = 'Users deleted successfully.';
                break;
            case 'assign_role':
                $users = User::whereIn('id', $userIds)->get();
                foreach ($users as $user) {
                    $user->syncRoles([$request->role]);
                }
                $message = "Role '{$request->role}' assigned to users successfully.";
                break;
        }

        return back()->with('success', $message);
    }

    /**
     * Show user activity.
     */
    public function activity(User $user)
    {
        $activities = [
            'logins' => $user->loginActivities()->latest()->limit(10)->get(),
            'enrollments' => $user->enrollments()->with('course')->latest()->limit(10)->get(),
            'progress' => $user->progress()->with('course')->latest()->limit(10)->get(),
            'subscriptions' => $user->subscriptions()->with('subscriptionPlan')->latest()->limit(5)->get(),
        ];

        return view('admin.users.activity', compact('user', 'activities'));
    }

    /**
     * Send notification to user.
     */
    public function sendNotification(Request $request, User $user)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'message' => 'required|string',
            'type' => 'required|in:info,success,warning,error',
        ]);

        // For now, we'll just flash a success message
        // In a real implementation, you'd create a notification record
        // and possibly send email/push notifications

        return back()->with('success', 'Notification sent to user successfully.');
    }
}
