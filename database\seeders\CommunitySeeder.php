<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\CommunityPost;
use App\Models\CommunityComment;
use App\Models\User;

class CommunitySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get mentor user
        $mentor = User::where('email', '<EMAIL>')->first();

        if (!$mentor) {
            $this->command->error('<PERSON><PERSON> user not found. Please run CourseSeeder first.');
            return;
        }

        // Create some sample users for community posts
        $users = [
            [
                'name' => 'John Success',
                'first_name' => '<PERSON>',
                'last_name' => 'Success',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
                'is_active' => true,
            ],
            [
                'name' => 'Sarah Winner',
                'first_name' => 'Sarah',
                'last_name' => 'Winner',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
                'is_active' => true,
            ],
            [
                'name' => '<PERSON>',
                'first_name' => '<PERSON>',
                'last_name' => 'Entrepreneur',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
                'is_active' => true,
            ],
        ];

        $createdUsers = [];
        foreach ($users as $userData) {
            $createdUsers[] = User::firstOrCreate(
                ['email' => $userData['email']],
                $userData
            );
        }

        // Add mentor to users array
        $allUsers = array_merge($createdUsers, [$mentor]);

        $posts = [
            [
                'title' => 'Just Hit My First $10K Month! 🚀',
                'content' => "I can't believe it! After 6 months of following the strategies from the e-commerce course, I finally hit my first $10,000 month!\n\nHere's what worked for me:\n- Product research using the methods from Module 2\n- Facebook ads optimization (took 3 months to master)\n- Customer service automation\n- Scaling gradually instead of going all-in\n\nTo anyone struggling: DON'T GIVE UP! It took me 4 failed products before finding my winner. The key is persistence and following the system.\n\nThank you Andrew and the community for the support! 💪",
                'category' => 'Success Stories',
                'tags' => ['ecommerce', 'success', 'milestone', 'facebook-ads'],
                'is_pinned' => true,
                'likes_count' => 47,
                'views_count' => 234,
            ],
            [
                'title' => 'Best Crypto Trading Tools in 2024?',
                'content' => "Hey everyone! I'm looking to upgrade my crypto trading setup and wondering what tools you're all using.\n\nCurrently using:\n- TradingView for charts\n- Binance for trading\n- CoinTracker for taxes\n\nWhat I'm considering:\n- 3Commas for automated trading\n- Messari for research\n- DeFiPulse for DeFi tracking\n\nAny recommendations? What's working best for you guys?",
                'category' => 'Questions',
                'tags' => ['crypto', 'trading', 'tools', 'recommendations'],
                'likes_count' => 23,
                'views_count' => 156,
            ],
            [
                'title' => 'Copywriting Framework That Doubled My Conversions',
                'content' => "After testing dozens of different approaches, I found a copywriting framework that consistently doubles my conversion rates.\n\nThe PASTOR Framework:\n\nP - Problem (identify the pain point)\nA - Amplify (make it hurt more)\nS - Story (share a relatable story)\nT - Transformation (show the solution)\nO - Offer (present your product/service)\nR - Response (clear call to action)\n\nI've used this for:\n- Email campaigns (2.3x open rates)\n- Sales pages (1.8x conversion)\n- Social media ads (2.1x CTR)\n\nThe key is spending 60% of your time on the Problem and Story sections. Most people rush to the offer.\n\nWho wants me to break down a real example?",
                'category' => 'Resources',
                'tags' => ['copywriting', 'framework', 'conversions', 'marketing'],
                'likes_count' => 89,
                'views_count' => 445,
            ],
            [
                'title' => 'Networking Event in London - Who\'s Coming?',
                'content' => "Planning a Real World meetup in London next month!\n\n📅 Date: March 15th, 2024\n📍 Location: Central London (exact venue TBD)\n⏰ Time: 6:00 PM - 10:00 PM\n\nAgenda:\n- Networking & introductions\n- Success story sharing\n- Group mastermind session\n- Dinner & drinks\n\nLooking for 20-30 serious entrepreneurs who want to connect and grow together.\n\nComment below if you're interested! Let's build something amazing together 🔥",
                'category' => 'Networking',
                'tags' => ['networking', 'london', 'meetup', 'mastermind'],
                'likes_count' => 34,
                'views_count' => 189,
            ],
            [
                'title' => 'Real Estate vs Stocks: Where Should I Invest $50K?',
                'content' => "I've saved up $50,000 and trying to decide between real estate and stock market investing.\n\nReal Estate Pros:\n- Tangible asset\n- Rental income potential\n- Tax benefits\n- Leverage opportunities\n\nStock Market Pros:\n- Higher liquidity\n- Diversification easier\n- Lower entry barriers\n- Compound growth potential\n\nMy situation:\n- 28 years old\n- Stable income ($80K/year)\n- No debt\n- High risk tolerance\n- 10+ year investment horizon\n\nWhat would you do? Any specific strategies you'd recommend?",
                'category' => 'Questions',
                'tags' => ['investing', 'real-estate', 'stocks', 'advice'],
                'likes_count' => 67,
                'views_count' => 312,
            ],
            [
                'title' => 'Social Media Growth Hack That Got Me 10K Followers',
                'content' => "Discovered a social media growth strategy that got me from 500 to 10,500 followers in 3 months.\n\nThe 'Value Ladder' Strategy:\n\n1. Create 5 pillar content types\n2. Post 3x daily (morning, afternoon, evening)\n3. Engage with 50 accounts in your niche daily\n4. Use story highlights as a funnel\n5. Cross-promote on all platforms\n\nMost important: CONSISTENCY beats perfection every time.\n\nI posted every single day for 90 days straight. Some posts flopped, others went viral. The algorithm rewards consistency.\n\nDetailed breakdown in the comments 👇",
                'category' => 'Resources',
                'tags' => ['social-media', 'growth', 'strategy', 'followers'],
                'likes_count' => 156,
                'views_count' => 678,
            ],
        ];

        foreach ($posts as $index => $postData) {
            $user = $allUsers[$index % count($allUsers)];

            $post = CommunityPost::create(array_merge($postData, [
                'user_id' => $user->id,
                'comments_count' => rand(3, 15),
                'last_activity_at' => now()->subHours(rand(1, 48)),
            ]));

            // Create some comments for each post
            $this->createCommentsForPost($post, $allUsers);
        }

        $this->command->info('Community posts and comments seeded successfully!');
    }

    private function createCommentsForPost($post, $users)
    {
        $commentTemplates = [
            "Great post! This is exactly what I needed to hear.",
            "Thanks for sharing your experience. Very inspiring!",
            "Can you share more details about this strategy?",
            "I tried something similar and got great results too.",
            "This is gold! Saving this for later reference.",
            "Amazing results! How long did it take you to see progress?",
            "Love the detailed breakdown. Very helpful!",
            "This community is incredible. So much value!",
            "Thanks for the inspiration. Time to take action!",
            "Brilliant strategy! Definitely going to implement this.",
        ];

        $numComments = rand(3, 8);

        for ($i = 0; $i < $numComments; $i++) {
            $user = $users[array_rand($users)];
            $content = $commentTemplates[array_rand($commentTemplates)];

            $comment = CommunityComment::create([
                'post_id' => $post->id,
                'user_id' => $user->id,
                'content' => $content,
                'likes_count' => rand(0, 12),
            ]);

            // Sometimes add a reply
            if (rand(1, 3) === 1) {
                $replyUser = $users[array_rand($users)];
                CommunityComment::create([
                    'post_id' => $post->id,
                    'user_id' => $replyUser->id,
                    'parent_id' => $comment->id,
                    'content' => "Thanks for the comment! " . $commentTemplates[array_rand($commentTemplates)],
                    'likes_count' => rand(0, 5),
                ]);
            }
        }
    }
}
