<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\UserSubscription;
use App\Models\SubscriptionPlan;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class SubscriptionManagementController extends Controller
{
    /**
     * Display a listing of subscriptions.
     */
    public function index(Request $request)
    {
        $query = UserSubscription::with(['user', 'subscriptionPlan']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->whereHas('user', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by plan
        if ($request->filled('plan')) {
            $query->where('subscription_plan_id', $request->plan);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->where('created_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->where('created_at', '<=', $request->date_to . ' 23:59:59');
        }

        $subscriptions = $query->latest()->paginate(20);

        // Get statistics
        $stats = [
            'total' => UserSubscription::count(),
            'active' => UserSubscription::where('status', 'active')->count(),
            'cancelled' => UserSubscription::where('status', 'cancelled')->count(),
            'expired' => UserSubscription::where('status', 'expired')->count(),
            'monthly_revenue' => UserSubscription::where('status', 'active')
                ->whereHas('subscriptionPlan', function($q) {
                    $q->where('billing_cycle', 'monthly');
                })->sum('amount'),
            'yearly_revenue' => UserSubscription::where('status', 'active')
                ->whereHas('subscriptionPlan', function($q) {
                    $q->where('billing_cycle', 'yearly');
                })->sum('amount'),
        ];

        $plans = SubscriptionPlan::all();

        return view('admin.subscriptions.index', compact('subscriptions', 'stats', 'plans'));
    }

    /**
     * Display the specified subscription.
     */
    public function show(UserSubscription $subscription)
    {
        $subscription->load(['user', 'subscriptionPlan', 'payments']);
        
        return view('admin.subscriptions.show', compact('subscription'));
    }

    /**
     * Cancel a subscription.
     */
    public function cancelSubscription(UserSubscription $subscription)
    {
        try {
            // Cancel the subscription in Stripe
            if ($subscription->stripe_subscription_id) {
                $subscription->user->subscription($subscription->name)->cancel();
            }

            $subscription->update(['status' => 'cancelled']);

            return back()->with('success', 'Subscription cancelled successfully.');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to cancel subscription: ' . $e->getMessage()]);
        }
    }

    /**
     * Resume a subscription.
     */
    public function resumeSubscription(UserSubscription $subscription)
    {
        try {
            // Resume the subscription in Stripe
            if ($subscription->stripe_subscription_id) {
                $subscription->user->subscription($subscription->name)->resume();
            }

            $subscription->update(['status' => 'active']);

            return back()->with('success', 'Subscription resumed successfully.');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to resume subscription: ' . $e->getMessage()]);
        }
    }

    /**
     * Display subscription plans.
     */
    public function plans()
    {
        $plans = SubscriptionPlan::withCount(['subscriptions'])
            ->orderBy('sort_order')
            ->get();

        return view('admin.subscription-plans.index', compact('plans'));
    }

    /**
     * Store a new subscription plan.
     */
    public function storePlan(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'slug' => 'required|string|max:255|unique:subscription_plans',
            'description' => 'required|string',
            'monthly_price' => 'required|numeric|min:0',
            'yearly_price' => 'nullable|numeric|min:0',
            'features' => 'required|array',
            'stripe_monthly_price_id' => 'required|string',
            'stripe_yearly_price_id' => 'nullable|string',
            'sort_order' => 'required|integer|min:0',
        ]);

        SubscriptionPlan::create($request->all());

        return back()->with('success', 'Subscription plan created successfully.');
    }

    /**
     * Update a subscription plan.
     */
    public function updatePlan(Request $request, SubscriptionPlan $plan)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'monthly_price' => 'required|numeric|min:0',
            'yearly_price' => 'nullable|numeric|min:0',
            'features' => 'required|array',
            'sort_order' => 'required|integer|min:0',
        ]);

        $plan->update($request->all());

        return back()->with('success', 'Subscription plan updated successfully.');
    }

    /**
     * Delete a subscription plan.
     */
    public function destroyPlan(SubscriptionPlan $plan)
    {
        if ($plan->subscriptions()->exists()) {
            return back()->withErrors(['error' => 'Cannot delete plan with active subscriptions.']);
        }

        $plan->delete();

        return back()->with('success', 'Subscription plan deleted successfully.');
    }
}
