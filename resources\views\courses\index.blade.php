@extends('layouts.app')

@section('content')
<!-- Hero Section -->
<div class="bg-gradient-to-r from-blue-600 to-purple-700 text-white py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl md:text-5xl font-bold mb-4">
                Master New Skills
            </h1>
            <p class="text-xl md:text-2xl text-blue-100 mb-8">
                Learn from successful entrepreneurs and build your empire
            </p>
            
            <!-- Search Bar -->
            <div class="max-w-2xl mx-auto">
                <form action="{{ route('courses.search') }}" method="GET" class="flex">
                    <input type="text" name="q" placeholder="Search courses..." 
                           value="{{ request('search') }}"
                           class="flex-1 px-4 py-3 rounded-l-lg text-gray-900 focus:outline-none focus:ring-2 focus:ring-yellow-400">
                    <button type="submit" class="bg-yellow-400 hover:bg-yellow-500 text-black px-6 py-3 rounded-r-lg font-medium transition duration-300">
                        Search
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Filters Section -->
<div class="bg-white border-b">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <form method="GET" action="{{ route('courses.index') }}" class="flex flex-wrap items-center gap-4">
            <!-- Category Filter -->
            <div>
                <select name="category" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                    <option value="">All Categories</option>
                    @foreach($categories as $category)
                        <option value="{{ $category }}" {{ request('category') == $category ? 'selected' : '' }}>
                            {{ $category }}
                        </option>
                    @endforeach
                </select>
            </div>
            
            <!-- Difficulty Filter -->
            <div>
                <select name="difficulty" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                    <option value="">All Levels</option>
                    @foreach($difficulties as $difficulty)
                        <option value="{{ $difficulty }}" {{ request('difficulty') == $difficulty ? 'selected' : '' }}>
                            {{ ucfirst($difficulty) }}
                        </option>
                    @endforeach
                </select>
            </div>
            
            <!-- Sort Filter -->
            <div>
                <select name="sort" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                    <option value="featured" {{ request('sort') == 'featured' ? 'selected' : '' }}>Featured</option>
                    <option value="newest" {{ request('sort') == 'newest' ? 'selected' : '' }}>Newest</option>
                    <option value="oldest" {{ request('sort') == 'oldest' ? 'selected' : '' }}>Oldest</option>
                    <option value="title" {{ request('sort') == 'title' ? 'selected' : '' }}>Title A-Z</option>
                </select>
            </div>
            
            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition duration-300">
                Apply Filters
            </button>
            
            @if(request()->hasAny(['category', 'difficulty', 'sort', 'search']))
                <a href="{{ route('courses.index') }}" class="text-gray-600 hover:text-gray-800">
                    Clear Filters
                </a>
            @endif
        </form>
    </div>
</div>

<!-- Courses Grid -->
<div class="py-12 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        @if($courses->count() > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-6">
                @foreach($courses as $course)
                <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition duration-300">
                    <!-- Course Thumbnail -->
                    <div class="relative">
                        @if($course->thumbnail)
                            <img src="{{ $course->thumbnail }}" alt="{{ $course->title }}" class="w-full h-48 object-cover">
                        @else
                            <div class="w-full h-48 bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                                <span class="text-white text-2xl font-bold">{{ substr($course->title, 0, 1) }}</span>
                            </div>
                        @endif
                        
                        @if($course->is_featured)
                            <div class="absolute top-2 left-2 bg-yellow-400 text-black px-2 py-1 rounded text-xs font-medium">
                                Featured
                            </div>
                        @endif
                        
                        <div class="absolute top-2 right-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-xs">
                            {{ $course->duration_hours }}h
                        </div>
                    </div>
                    
                    <!-- Course Info -->
                    <div class="p-4">
                        <div class="flex items-center justify-between mb-2">
                            <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">{{ $course->category }}</span>
                            <span class="text-xs text-gray-500 capitalize">{{ $course->difficulty }}</span>
                        </div>
                        
                        <h3 class="font-semibold text-lg mb-2 line-clamp-2">{{ $course->title }}</h3>
                        <p class="text-gray-600 text-sm mb-3 line-clamp-3">{{ $course->description }}</p>
                        
                        <div class="flex items-center mb-3">
                            @if($course->mentor->avatar)
                                <img src="{{ $course->mentor->avatar }}" alt="{{ $course->mentor->name }}" class="w-6 h-6 rounded-full mr-2">
                            @else
                                <div class="w-6 h-6 bg-gray-300 rounded-full mr-2 flex items-center justify-center">
                                    <span class="text-xs text-gray-600">{{ substr($course->mentor->name, 0, 1) }}</span>
                                </div>
                            @endif
                            <span class="text-sm text-gray-700">{{ $course->mentor->name }}</span>
                        </div>
                        
                        <div class="flex items-center justify-between">
                            @if($course->price > 0)
                                <span class="text-lg font-bold text-green-600">${{ number_format($course->price, 2) }}</span>
                            @else
                                <span class="text-sm text-blue-600 font-medium">Included in subscription</span>
                            @endif
                            
                            <a href="{{ route('courses.show', $course) }}" 
                               class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm font-medium transition duration-300">
                                View Course
                            </a>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
            
            <!-- Pagination -->
            <div class="mt-8">
                {{ $courses->appends(request()->query())->links() }}
            </div>
        @else
            <div class="text-center py-12">
                <div class="max-w-md mx-auto">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No courses found</h3>
                    <p class="mt-1 text-sm text-gray-500">Try adjusting your search criteria or browse all courses.</p>
                    <div class="mt-6">
                        <a href="{{ route('courses.index') }}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition duration-300">
                            Browse All Courses
                        </a>
                    </div>
                </div>
            </div>
        @endif
    </div>
</div>

<!-- Categories Section -->
<div class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Browse by Category</h2>
            <p class="text-lg text-gray-600">Explore courses in different areas of expertise</p>
        </div>
        
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            @foreach($categories as $category)
            <a href="{{ route('courses.category', $category) }}" 
               class="bg-gray-50 hover:bg-gray-100 rounded-lg p-4 text-center transition duration-300">
                <div class="text-2xl mb-2">
                    @switch($category)
                        @case('Crypto')
                            ₿
                            @break
                        @case('Copywriting')
                            ✍️
                            @break
                        @case('E-commerce')
                            🛒
                            @break
                        @case('Social Media')
                            📱
                            @break
                        @case('Freelancing')
                            💼
                            @break
                        @case('Real Estate')
                            🏠
                            @break
                        @default
                            📚
                    @endswitch
                </div>
                <h3 class="font-medium text-gray-900">{{ $category }}</h3>
            </a>
            @endforeach
        </div>
    </div>
</div>
@endsection
