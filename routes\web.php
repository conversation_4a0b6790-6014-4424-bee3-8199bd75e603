<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\SubscriptionController;
use App\Http\Controllers\WebhookController;
use App\Http\Controllers\CourseController;
use App\Http\Controllers\LessonController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\LiveCallController;
use App\Http\Controllers\CommunityController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\CertificateController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\RatingController;
use App\Http\Controllers\SearchController;
use App\Http\Controllers\Admin\AdminDashboardController;
use App\Http\Controllers\Admin\UserManagementController;
use App\Http\Controllers\Admin\CourseManagementController;
use App\Http\Controllers\Admin\LiveCallManagementController;
use App\Http\Controllers\Admin\AnalyticsController;
use App\Http\Controllers\Admin\SubscriptionManagementController;
use App\Http\Controllers\Mentor\MentorDashboardController;
use App\Http\Controllers\MentorController;
use App\Http\Controllers\FaqController;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\Admin\PaymentManagementController;
use App\Http\Controllers\Admin\CommunityManagementController;
use App\Http\Controllers\Admin\SettingsController;
use App\Http\Controllers\Admin\LessonManagementController;
use App\Http\Controllers\Admin\CategoryManagementController;
use App\Http\Controllers\Admin\NotificationManagementController;
use Illuminate\Foundation\Auth\EmailVerificationRequest;
use Illuminate\Http\Request;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// Homepage
Route::get('/', [HomeController::class, 'index'])->name('home');
Route::get('/pricing', [HomeController::class, 'pricing'])->name('pricing');
Route::get('/about', [HomeController::class, 'about'])->name('about');
Route::get('/contact', [HomeController::class, 'contact'])->name('contact');
Route::post('/contact', [HomeController::class, 'submitContact'])->name('contact.submit');
Route::get('/faq', [HomeController::class, 'faq'])->name('faq');
Route::get('/testimonials', [HomeController::class, 'testimonials'])->name('testimonials');
Route::get('/mentors', [HomeController::class, 'mentors'])->name('mentors');
Route::get('/mentors/{mentor}', [HomeController::class, 'showMentor'])->name('mentors.show');

// Legal Pages
Route::get('/terms', function () {
    return view('legal.terms');
})->name('terms');

Route::get('/privacy', function () {
    return view('legal.privacy');
})->name('privacy');

// Search Routes (public)
Route::get('/search', [SearchController::class, 'index'])->name('search');
Route::get('/search/courses', [SearchController::class, 'courses'])->name('search.courses');
Route::get('/search/mentors', [SearchController::class, 'mentors'])->name('search.mentors');
Route::get('/search/suggestions', [SearchController::class, 'suggestions'])->name('search.suggestions');

// Course Routes (public)
Route::get('/courses', [CourseController::class, 'index'])->name('courses.index');
Route::get('/courses/search', [CourseController::class, 'search'])->name('courses.search');
Route::get('/courses/category/{category}', [CourseController::class, 'category'])->name('courses.category');
Route::get('/courses/featured', [CourseController::class, 'featured'])->name('courses.featured');
Route::get('/courses/free', [CourseController::class, 'free'])->name('courses.free');
Route::get('/courses/{course}', [CourseController::class, 'show'])->name('courses.show');
Route::get('/courses/{course}/preview', [CourseController::class, 'preview'])->name('courses.preview');

// Live Calls Routes (public)
Route::get('/live-calls', [LiveCallController::class, 'index'])->name('live-calls.index');
Route::get('/live-calls/upcoming', [LiveCallController::class, 'upcoming'])->name('live-calls.upcoming');
Route::get('/live-calls/past', [LiveCallController::class, 'past'])->name('live-calls.past');
Route::get('/live-calls/{liveCall}', [LiveCallController::class, 'show'])->name('live-calls.show');

// Community Routes (public)
Route::get('/community', [CommunityController::class, 'index'])->name('community.index');
Route::get('/community/posts/{post}', [CommunityController::class, 'show'])->name('community.show');
Route::get('/community/categories', [CommunityController::class, 'categories'])->name('community.categories');
Route::get('/community/category/{category}', [CommunityController::class, 'categoryPosts'])->name('community.category');

// Certificate Verification (public)
Route::get('/certificates/verify/{certificate}', [CertificateController::class, 'verify'])->name('certificates.verify');
Route::get('/certificates/{certificate}/download', [CertificateController::class, 'download'])->name('certificates.download');

// Authentication Routes
Route::middleware('guest')->group(function () {
    Route::get('/register', [AuthController::class, 'showRegister'])->name('register');
    Route::post('/register', [AuthController::class, 'register']);

    Route::get('/login', [AuthController::class, 'showLogin'])->name('login');
    Route::post('/login', [AuthController::class, 'login']);

    // Password Reset Routes
    Route::get('/forgot-password', [AuthController::class, 'showForgotPassword'])->name('password.request');
    Route::post('/forgot-password', [AuthController::class, 'sendResetLink'])->name('password.email');
    Route::get('/reset-password/{token}', [AuthController::class, 'showResetPassword'])->name('password.reset');
    Route::post('/reset-password', [AuthController::class, 'resetPassword'])->name('password.update');
});

Route::middleware('auth')->group(function () {
    Route::post('/logout', [AuthController::class, 'logout'])->name('logout');

    // Email Verification Routes
    Route::get('/email/verify', function () {
        return view('auth.verify-email');
    })->name('verification.notice');

    Route::get('/email/verify/{id}/{hash}', function (EmailVerificationRequest $request) {
        $request->fulfill();
        return redirect('/dashboard');
    })->middleware('signed')->name('verification.verify');

    Route::post('/email/verification-notification', function (Request $request) {
        $request->user()->sendEmailVerificationNotification();
        return back()->with('message', 'Verification link sent!');
    })->middleware('throttle:6,1')->name('verification.send');
});

// Protected Routes (require authentication and email verification)
Route::middleware(['auth', 'verified'])->group(function () {
    // Dashboard Routes
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('/dashboard/courses', [DashboardController::class, 'courses'])->name('dashboard.courses');
    Route::get('/dashboard/progress', [DashboardController::class, 'progress'])->name('dashboard.progress');
    Route::get('/dashboard/certificates', [DashboardController::class, 'certificates'])->name('dashboard.certificates');
    Route::get('/dashboard/stats', [DashboardController::class, 'stats'])->name('dashboard.stats');
    Route::get('/dashboard/notifications', [DashboardController::class, 'notifications'])->name('dashboard.notifications');

    // Profile Routes
    Route::get('/profile', [ProfileController::class, 'index'])->name('profile.index');
    Route::get('/profile/edit', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::put('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
    Route::get('/profile/certificates', [ProfileController::class, 'certificates'])->name('profile.certificates');
    Route::get('/profile/certificates/{certificate}', [ProfileController::class, 'downloadCertificate'])->name('profile.certificates.download');
    Route::get('/profile/password', [ProfileController::class, 'password'])->name('profile.password');
    Route::put('/profile/password', [ProfileController::class, 'updatePassword'])->name('profile.password.update');

    // Notification Routes
    Route::get('/notifications', [NotificationController::class, 'index'])->name('notifications.index');
    Route::post('/notifications/{notification}/read', [NotificationController::class, 'markAsRead'])->name('notifications.read');
    Route::post('/notifications/read-all', [NotificationController::class, 'markAllAsRead'])->name('notifications.read-all');
    Route::delete('/notifications/{notification}', [NotificationController::class, 'destroy'])->name('notifications.destroy');
    Route::delete('/notifications', [NotificationController::class, 'destroyAll'])->name('notifications.destroy-all');

    // Subscription Routes
    Route::get('/subscriptions', [SubscriptionController::class, 'index'])->name('subscriptions.index');
    Route::get('/subscriptions/plans', [SubscriptionController::class, 'plans'])->name('subscriptions.plans');
    Route::get('/subscriptions/{plan}/checkout', [SubscriptionController::class, 'checkout'])->name('subscriptions.checkout');
    Route::post('/subscriptions/subscribe', [SubscriptionController::class, 'subscribe'])->name('subscriptions.subscribe');
    Route::post('/subscriptions/cancel', [SubscriptionController::class, 'cancel'])->name('subscriptions.cancel');
    Route::post('/subscriptions/resume', [SubscriptionController::class, 'resume'])->name('subscriptions.resume');
    Route::post('/subscriptions/upgrade', [SubscriptionController::class, 'upgrade'])->name('subscriptions.upgrade');
    Route::post('/subscriptions/downgrade', [SubscriptionController::class, 'downgrade'])->name('subscriptions.downgrade');
    Route::post('/subscriptions/payment-method', [SubscriptionController::class, 'updatePaymentMethod'])->name('subscriptions.payment-method');
    Route::get('/subscriptions/success', [SubscriptionController::class, 'success'])->name('subscriptions.success');
    Route::get('/subscriptions/cancelled', [SubscriptionController::class, 'cancelled'])->name('subscriptions.cancelled');
    Route::get('/subscriptions/invoices', [SubscriptionController::class, 'invoices'])->name('subscriptions.invoices');
    Route::get('/subscriptions/invoices/{invoice}', [SubscriptionController::class, 'downloadInvoice'])->name('subscriptions.invoices.download');

    // Payment Routes
    Route::get('/payments', [PaymentController::class, 'index'])->name('payments.index');
    Route::get('/payments/{payment}', [PaymentController::class, 'show'])->name('payments.show');
    Route::post('/payments/crypto/verify', [PaymentController::class, 'verifyCrypto'])->name('payments.crypto.verify');
    Route::get('/payments/crypto/{payment}/status', [PaymentController::class, 'cryptoStatus'])->name('payments.crypto.status');

    // Course Routes (requires auth)
    Route::post('/courses/{course}/enroll', [CourseController::class, 'enroll'])->name('courses.enroll');
    Route::delete('/courses/{course}/unenroll', [CourseController::class, 'unenroll'])->name('courses.unenroll');
    Route::post('/courses/{course}/favorite', [CourseController::class, 'toggleFavorite'])->name('courses.favorite');
    Route::post('/courses/{course}/rate', [RatingController::class, 'store'])->name('courses.rate');
    Route::put('/courses/{course}/rating/{rating}', [RatingController::class, 'update'])->name('courses.rating.update');
    Route::delete('/courses/{course}/rating/{rating}', [RatingController::class, 'destroy'])->name('courses.rating.destroy');

    // Lesson Routes (requires auth)
    Route::get('/lessons/{lesson}', [LessonController::class, 'show'])->name('lessons.show');
    Route::post('/lessons/{lesson}/complete', [LessonController::class, 'complete'])->name('lessons.complete');
    Route::post('/lessons/{lesson}/progress', [LessonController::class, 'updateProgress'])->name('lessons.progress');
    Route::post('/lessons/{lesson}/watch-time', [LessonController::class, 'updateWatchTime'])->name('lessons.watch-time');
    Route::get('/lessons/{lesson}/resources/{resource}', [LessonController::class, 'downloadResource'])->name('lessons.resource');
    Route::post('/lessons/{lesson}/bookmark', [LessonController::class, 'toggleBookmark'])->name('lessons.bookmark');
    Route::post('/lessons/{lesson}/note', [LessonController::class, 'saveNote'])->name('lessons.note');

    // Live Call Routes (requires auth)
    Route::post('/live-calls/{liveCall}/register', [LiveCallController::class, 'register'])->name('live-calls.register');
    Route::delete('/live-calls/{liveCall}/unregister', [LiveCallController::class, 'unregister'])->name('live-calls.unregister');
    Route::get('/live-calls/{liveCall}/join', [LiveCallController::class, 'join'])->name('live-calls.join');
    Route::post('/live-calls/{liveCall}/reminder', [LiveCallController::class, 'setReminder'])->name('live-calls.reminder');
    Route::get('/live-calls/{liveCall}/recording', [LiveCallController::class, 'recording'])->name('live-calls.recording');
    Route::post('/live-calls/{liveCall}/feedback', [LiveCallController::class, 'feedback'])->name('live-calls.feedback');

    // Community Routes (requires auth)
    Route::get('/community/create', [CommunityController::class, 'create'])->name('community.create');
    Route::post('/community', [CommunityController::class, 'store'])->name('community.store');
    Route::get('/community/posts/{post}/edit', [CommunityController::class, 'edit'])->name('community.edit');
    Route::put('/community/posts/{post}', [CommunityController::class, 'update'])->name('community.update');
    Route::delete('/community/posts/{post}', [CommunityController::class, 'destroy'])->name('community.destroy');
    Route::post('/community/posts/{post}/comments', [CommunityController::class, 'storeComment'])->name('community.comments.store');
    Route::put('/community/comments/{comment}', [CommunityController::class, 'updateComment'])->name('community.comments.update');
    Route::delete('/community/comments/{comment}', [CommunityController::class, 'destroyComment'])->name('community.comments.destroy');
    Route::post('/community/posts/{post}/like', [CommunityController::class, 'toggleLike'])->name('community.posts.like');
    Route::post('/community/comments/{comment}/like', [CommunityController::class, 'toggleCommentLike'])->name('community.comments.like');
    Route::post('/community/posts/{post}/report', [CommunityController::class, 'reportPost'])->name('community.posts.report');
    Route::post('/community/comments/{comment}/report', [CommunityController::class, 'reportComment'])->name('community.comments.report');
    Route::post('/community/posts/{post}/bookmark', [CommunityController::class, 'toggleBookmark'])->name('community.posts.bookmark');
    Route::get('/community/my-posts', [CommunityController::class, 'myPosts'])->name('community.my-posts');
    Route::get('/community/bookmarks', [CommunityController::class, 'bookmarks'])->name('community.bookmarks');

    // Certificate Routes (requires auth)
    Route::get('/certificates', [CertificateController::class, 'index'])->name('certificates.index');
    Route::get('/certificates/{certificate}', [CertificateController::class, 'show'])->name('certificates.show');
    Route::post('/certificates/{certificate}/share', [CertificateController::class, 'share'])->name('certificates.share');
});

// Webhooks (no middleware needed)
Route::post('/stripe/webhook', [WebhookController::class, 'handleStripeWebhook'])->name('stripe.webhook');
Route::post('/crypto/webhook', [WebhookController::class, 'handleCryptoWebhook'])->name('crypto.webhook');

// AJAX Routes (for dynamic content)
Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('/ajax/courses/{course}/progress', [CourseController::class, 'getProgress'])->name('ajax.courses.progress');
    Route::get('/ajax/notifications/unread-count', [NotificationController::class, 'unreadCount'])->name('ajax.notifications.unread-count');
    Route::get('/ajax/search/suggestions', [SearchController::class, 'ajaxSuggestions'])->name('ajax.search.suggestions');
    Route::post('/ajax/lessons/{lesson}/bookmark', [LessonController::class, 'ajaxToggleBookmark'])->name('ajax.lessons.bookmark');
    Route::get('/ajax/dashboard/stats', [DashboardController::class, 'ajaxStats'])->name('ajax.dashboard.stats');
});

// Admin Routes (requires admin role)
Route::middleware(['auth', 'verified', 'role:admin'])->prefix('admin')->name('admin.')->group(function () {
    // Dashboard
    Route::get('/', [AdminDashboardController::class, 'index'])->name('dashboard');
    Route::get('/stats', [AdminDashboardController::class, 'stats'])->name('stats');
    Route::get('/overview', [AdminDashboardController::class, 'overview'])->name('overview');

    // User Management
    Route::resource('users', UserManagementController::class);
    Route::post('users/{user}/toggle-status', [UserManagementController::class, 'toggleStatus'])->name('users.toggle-status');
    Route::post('users/{user}/impersonate', [UserManagementController::class, 'impersonate'])->name('users.impersonate');
    Route::post('users/stop-impersonating', [UserManagementController::class, 'stopImpersonating'])->name('users.stop-impersonating');
    Route::get('users-export', [UserManagementController::class, 'export'])->name('users.export');
    Route::post('users/bulk-action', [UserManagementController::class, 'bulkAction'])->name('users.bulk-action');
    Route::get('users/{user}/activity', [UserManagementController::class, 'activity'])->name('users.activity');
    Route::post('users/{user}/send-notification', [UserManagementController::class, 'sendNotification'])->name('users.send-notification');

    // Course Management
    Route::resource('courses', CourseManagementController::class);
    Route::post('courses/{course}/toggle-published', [CourseManagementController::class, 'togglePublished'])->name('courses.toggle-published');
    Route::post('courses/{course}/toggle-featured', [CourseManagementController::class, 'toggleFeatured'])->name('courses.toggle-featured');
    Route::get('courses/{course}/lessons', [CourseManagementController::class, 'lessons'])->name('courses.lessons');
    Route::get('courses/{course}/analytics', [CourseManagementController::class, 'analytics'])->name('courses.analytics');
    Route::post('courses/{course}/duplicate', [CourseManagementController::class, 'duplicate'])->name('courses.duplicate');
    Route::get('courses/{course}/students', [CourseManagementController::class, 'students'])->name('courses.students');
    Route::get('courses/{course}/reviews', [CourseManagementController::class, 'reviews'])->name('courses.reviews');

    // Lesson Management
    Route::resource('courses.lessons', LessonManagementController::class)->except(['index']);
    Route::get('lessons', [LessonManagementController::class, 'index'])->name('lessons.index');
    Route::post('lessons/{lesson}/toggle-published', [LessonManagementController::class, 'togglePublished'])->name('lessons.toggle-published');
    Route::post('lessons/reorder', [LessonManagementController::class, 'reorder'])->name('lessons.reorder');

    // Category Management
    Route::resource('categories', CategoryManagementController::class);
    Route::post('categories/{category}/toggle-active', [CategoryManagementController::class, 'toggleActive'])->name('categories.toggle-active');
    Route::post('categories/reorder', [CategoryManagementController::class, 'reorder'])->name('categories.reorder');

    // Subscription Management
    Route::resource('subscriptions', SubscriptionManagementController::class);
    Route::get('subscription-plans', [SubscriptionManagementController::class, 'plans'])->name('subscription-plans.index');
    Route::post('subscription-plans', [SubscriptionManagementController::class, 'storePlan'])->name('subscription-plans.store');
    Route::put('subscription-plans/{plan}', [SubscriptionManagementController::class, 'updatePlan'])->name('subscription-plans.update');
    Route::delete('subscription-plans/{plan}', [SubscriptionManagementController::class, 'destroyPlan'])->name('subscription-plans.destroy');
    Route::post('subscriptions/{subscription}/cancel', [SubscriptionManagementController::class, 'cancelSubscription'])->name('subscriptions.cancel');
    Route::post('subscriptions/{subscription}/resume', [SubscriptionManagementController::class, 'resumeSubscription'])->name('subscriptions.resume');

    // Payment Management
    Route::get('payments', [PaymentManagementController::class, 'index'])->name('payments.index');
    Route::get('payments/{payment}', [PaymentManagementController::class, 'show'])->name('payments.show');
    Route::post('payments/{payment}/refund', [PaymentManagementController::class, 'refund'])->name('payments.refund');
    Route::get('payments/export', [PaymentManagementController::class, 'export'])->name('payments.export');
    Route::get('revenue', [PaymentManagementController::class, 'revenue'])->name('revenue.index');

    // Live Call Management
    Route::resource('live-calls', LiveCallManagementController::class);
    Route::post('live-calls/{liveCall}/toggle-status', [LiveCallManagementController::class, 'toggleStatus'])->name('live-calls.toggle-status');
    Route::get('live-calls/{liveCall}/attendees', [LiveCallManagementController::class, 'attendees'])->name('live-calls.attendees');
    Route::post('live-calls/{liveCall}/send-reminder', [LiveCallManagementController::class, 'sendReminder'])->name('live-calls.send-reminder');

    // Community Management
    Route::get('community', [CommunityManagementController::class, 'index'])->name('community.index');
    Route::get('community/posts', [CommunityManagementController::class, 'posts'])->name('community.posts');
    Route::get('community/posts/{post}', [CommunityManagementController::class, 'showPost'])->name('community.posts.show');
    Route::post('community/posts/{post}/approve', [CommunityManagementController::class, 'approvePost'])->name('community.posts.approve');
    Route::post('community/posts/{post}/reject', [CommunityManagementController::class, 'rejectPost'])->name('community.posts.reject');
    Route::delete('community/posts/{post}', [CommunityManagementController::class, 'deletePost'])->name('community.posts.delete');
    Route::get('community/reports', [CommunityManagementController::class, 'reports'])->name('community.reports');
    Route::post('community/reports/{report}/resolve', [CommunityManagementController::class, 'resolveReport'])->name('community.reports.resolve');

    // Notification Management
    Route::get('notifications', [NotificationManagementController::class, 'index'])->name('notifications.index');
    Route::post('notifications/send', [NotificationManagementController::class, 'send'])->name('notifications.send');
    Route::post('notifications/broadcast', [NotificationManagementController::class, 'broadcast'])->name('notifications.broadcast');
    Route::get('notifications/templates', [NotificationManagementController::class, 'templates'])->name('notifications.templates');
    Route::post('notifications/templates', [NotificationManagementController::class, 'storeTemplate'])->name('notifications.templates.store');

    // Analytics
    Route::get('analytics', [AnalyticsController::class, 'index'])->name('analytics.index');
    Route::get('analytics/users', [AnalyticsController::class, 'users'])->name('analytics.users');
    Route::get('analytics/courses', [AnalyticsController::class, 'courses'])->name('analytics.courses');
    Route::get('analytics/revenue', [AnalyticsController::class, 'revenue'])->name('analytics.revenue');
    Route::get('analytics/engagement', [AnalyticsController::class, 'engagement'])->name('analytics.engagement');
    Route::get('analytics/export', [AnalyticsController::class, 'export'])->name('analytics.export');

    // Settings
    Route::get('settings', [SettingsController::class, 'index'])->name('settings.index');
    Route::put('settings/general', [SettingsController::class, 'updateGeneral'])->name('settings.general');
    Route::put('settings/email', [SettingsController::class, 'updateEmail'])->name('settings.email');
    Route::put('settings/payment', [SettingsController::class, 'updatePayment'])->name('settings.payment');
    Route::put('settings/security', [SettingsController::class, 'updateSecurity'])->name('settings.security');
    Route::post('settings/test-email', [SettingsController::class, 'testEmail'])->name('settings.test-email');
    Route::post('settings/clear-cache', [SettingsController::class, 'clearCache'])->name('settings.clear-cache');
    Route::get('settings/backup', [SettingsController::class, 'backup'])->name('settings.backup');
    Route::post('settings/backup/create', [SettingsController::class, 'createBackup'])->name('settings.backup.create');

    // System Maintenance
    Route::post('maintenance/enable', [SettingsController::class, 'enableMaintenance'])->name('maintenance.enable');
    Route::post('maintenance/disable', [SettingsController::class, 'disableMaintenance'])->name('maintenance.disable');
    Route::get('logs', [SettingsController::class, 'logs'])->name('logs.index');
    Route::get('logs/{file}', [SettingsController::class, 'showLog'])->name('logs.show');
    Route::delete('logs/{file}', [SettingsController::class, 'deleteLog'])->name('logs.delete');
});

// Mentor Routes (requires mentor role)
Route::middleware(['auth', 'verified', 'role:mentor'])->prefix('mentor')->name('mentor.')->group(function () {
    // Dashboard
    Route::get('/', [MentorDashboardController::class, 'index'])->name('dashboard');
    Route::get('/analytics', [MentorDashboardController::class, 'analytics'])->name('analytics');
});

// Test route for mentor dashboard (temporary)
Route::get('/test-mentor', function () {
    // Create a test mentor user
    $mentor = new \App\Models\User([
        'id' => 1,
        'name' => 'Andrew Tate',
        'email' => '<EMAIL>',
        'role' => 'mentor',
        'title' => 'Entrepreneur & Business Coach',
        'is_verified' => true,
        'is_featured' => true,
        'average_rating' => 4.8,
    ]);

    // Mock data for testing
    $courses = collect();
    $stats = [
        'total_courses' => 2,
        'total_students' => 150,
        'total_revenue' => 7500,
        'average_rating' => 4.8,
        'active_students' => 45,
        'completion_rate' => 78,
    ];

    $recentEnrollments = collect();
    $upcomingLiveCalls = collect();
    $recentReviews = collect();
    $revenueData = [
        'labels' => ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
        'data' => [1200, 1800, 2400, 3200, 4100, 5000]
    ];
    $studentProgressData = [
        'total' => 150,
        'active' => 45,
        'completed' => 35,
        'inactive' => 105
    ];
    $topCourses = collect();

    return view('mentor.dashboard', compact(
        'mentor',
        'courses',
        'stats',
        'recentEnrollments',
        'upcomingLiveCalls',
        'recentReviews',
        'revenueData',
        'studentProgressData',
        'topCourses'
    ));
})->name('test.mentor');

// Public Mentor Routes
Route::get('/mentors', [MentorController::class, 'index'])->name('mentors.index');
Route::get('/mentors/{mentor}', [MentorController::class, 'show'])->name('mentors.show');
Route::post('/mentors/{mentor}/follow', [MentorController::class, 'follow'])->name('mentors.follow');
Route::get('/mentors/apply', [MentorController::class, 'apply'])->name('mentors.apply');
Route::post('/mentors/apply', [MentorController::class, 'submitApplication'])->name('mentors.apply.submit');
Route::get('/mentors/apply/success', [MentorController::class, 'applicationSuccess'])->name('mentors.apply.success');

// FAQ Routes
Route::get('/faq', [FaqController::class, 'index'])->name('faq');
Route::get('/faq/{faq}', [FaqController::class, 'show'])->name('faq.show');
Route::post('/faq/{faq}/helpful', [FaqController::class, 'markHelpful'])->name('faq.helpful');
Route::post('/faq/{faq}/view', [FaqController::class, 'trackView'])->name('faq.view');
Route::get('/faq/search', [FaqController::class, 'search'])->name('faq.search');

// Contact Routes
Route::get('/contact', [ContactController::class, 'index'])->name('contact');
Route::post('/contact', [ContactController::class, 'store'])->name('contact.store');
Route::get('/contact/{ticketId}', [ContactController::class, 'show'])->name('contact.show');

require __DIR__.'/auth.php';
