@extends('layouts.admin')

@section('title', '- System Settings')

@section('content')
<div class="p-6">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">System Settings</h1>
                <p class="mt-1 text-sm text-gray-600">Configure your application settings</p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('admin.settings.logs') }}" 
                   class="bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-md transition duration-150">
                    View Logs
                </a>
                <form method="POST" action="{{ route('admin.settings.clear-cache') }}" class="inline">
                    @csrf
                    <button type="submit" 
                            class="bg-yellow-600 hover:bg-yellow-700 text-white font-medium py-2 px-4 rounded-md transition duration-150">
                        Clear Cache
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Settings Tabs -->
    <div class="bg-white shadow rounded-lg">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8 px-6" aria-label="Tabs">
                <button onclick="showTab('general')" 
                        class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm active">
                    General
                </button>
                <button onclick="showTab('payment')" 
                        class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                    Payment
                </button>
                <button onclick="showTab('email')" 
                        class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                    Email
                </button>
                <button onclick="showTab('notifications')" 
                        class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                    Notifications
                </button>
                <button onclick="showTab('system')" 
                        class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                    System
                </button>
            </nav>
        </div>

        <!-- General Settings -->
        <div id="general-tab" class="tab-content p-6">
            <form method="POST" action="{{ route('admin.settings.update') }}" enctype="multipart/form-data">
                @csrf
                <input type="hidden" name="section" value="general">
                
                <div class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="app_name" class="block text-sm font-medium text-gray-700 mb-2">Application Name</label>
                            <input type="text" name="app_name" id="app_name" 
                                   value="{{ old('app_name', $settings['app_name'] ?? 'The Real World') }}"
                                   class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        </div>
                        
                        <div>
                            <label for="app_url" class="block text-sm font-medium text-gray-700 mb-2">Application URL</label>
                            <input type="url" name="app_url" id="app_url" 
                                   value="{{ old('app_url', $settings['app_url'] ?? config('app.url')) }}"
                                   class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        </div>
                    </div>

                    <div>
                        <label for="app_description" class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                        <textarea name="app_description" id="app_description" rows="3"
                                  class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                  placeholder="Brief description of your application...">{{ old('app_description', $settings['app_description'] ?? '') }}</textarea>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="logo" class="block text-sm font-medium text-gray-700 mb-2">Logo</label>
                            <input type="file" name="logo" id="logo" accept="image/*"
                                   class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            @if(isset($settings['logo']) && $settings['logo'])
                                <div class="mt-2">
                                    <img src="{{ asset('storage/' . $settings['logo']) }}" alt="Current Logo" class="h-12">
                                </div>
                            @endif
                        </div>
                        
                        <div>
                            <label for="favicon" class="block text-sm font-medium text-gray-700 mb-2">Favicon</label>
                            <input type="file" name="favicon" id="favicon" accept="image/*"
                                   class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            @if(isset($settings['favicon']) && $settings['favicon'])
                                <div class="mt-2">
                                    <img src="{{ asset('storage/' . $settings['favicon']) }}" alt="Current Favicon" class="h-8">
                                </div>
                            @endif
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="timezone" class="block text-sm font-medium text-gray-700 mb-2">Timezone</label>
                            <select name="timezone" id="timezone" 
                                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="UTC" {{ ($settings['timezone'] ?? 'UTC') === 'UTC' ? 'selected' : '' }}>UTC</option>
                                <option value="America/New_York" {{ ($settings['timezone'] ?? '') === 'America/New_York' ? 'selected' : '' }}>Eastern Time</option>
                                <option value="America/Chicago" {{ ($settings['timezone'] ?? '') === 'America/Chicago' ? 'selected' : '' }}>Central Time</option>
                                <option value="America/Denver" {{ ($settings['timezone'] ?? '') === 'America/Denver' ? 'selected' : '' }}>Mountain Time</option>
                                <option value="America/Los_Angeles" {{ ($settings['timezone'] ?? '') === 'America/Los_Angeles' ? 'selected' : '' }}>Pacific Time</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="currency" class="block text-sm font-medium text-gray-700 mb-2">Default Currency</label>
                            <select name="currency" id="currency" 
                                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="USD" {{ ($settings['currency'] ?? 'USD') === 'USD' ? 'selected' : '' }}>USD - US Dollar</option>
                                <option value="EUR" {{ ($settings['currency'] ?? '') === 'EUR' ? 'selected' : '' }}>EUR - Euro</option>
                                <option value="GBP" {{ ($settings['currency'] ?? '') === 'GBP' ? 'selected' : '' }}>GBP - British Pound</option>
                                <option value="CAD" {{ ($settings['currency'] ?? '') === 'CAD' ? 'selected' : '' }}>CAD - Canadian Dollar</option>
                            </select>
                        </div>
                    </div>

                    <div class="flex items-center space-x-6">
                        <div class="flex items-center">
                            <input type="checkbox" name="maintenance_mode" id="maintenance_mode" value="1"
                                   {{ ($settings['maintenance_mode'] ?? false) ? 'checked' : '' }}
                                   class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            <label for="maintenance_mode" class="ml-2 text-sm text-gray-700">Maintenance Mode</label>
                        </div>
                        
                        <div class="flex items-center">
                            <input type="checkbox" name="user_registration" id="user_registration" value="1"
                                   {{ ($settings['user_registration'] ?? true) ? 'checked' : '' }}
                                   class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            <label for="user_registration" class="ml-2 text-sm text-gray-700">Allow User Registration</label>
                        </div>
                    </div>

                    <div class="flex justify-end">
                        <button type="submit" 
                                class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition duration-150">
                            Save General Settings
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Payment Settings -->
        <div id="payment-tab" class="tab-content p-6 hidden">
            <form method="POST" action="{{ route('admin.settings.update') }}">
                @csrf
                <input type="hidden" name="section" value="payment">
                
                <div class="space-y-6">
                    <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-blue-800">Payment Configuration</h3>
                                <div class="mt-2 text-sm text-blue-700">
                                    <p>Configure your payment providers. Changes will affect new transactions only.</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="stripe_publishable_key" class="block text-sm font-medium text-gray-700 mb-2">Stripe Publishable Key</label>
                            <input type="text" name="stripe_publishable_key" id="stripe_publishable_key" 
                                   value="{{ old('stripe_publishable_key', $settings['stripe_publishable_key'] ?? '') }}"
                                   class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                   placeholder="pk_test_...">
                        </div>
                        
                        <div>
                            <label for="stripe_secret_key" class="block text-sm font-medium text-gray-700 mb-2">Stripe Secret Key</label>
                            <input type="password" name="stripe_secret_key" id="stripe_secret_key" 
                                   value="{{ old('stripe_secret_key', $settings['stripe_secret_key'] ?? '') }}"
                                   class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                   placeholder="sk_test_...">
                        </div>
                    </div>

                    <div>
                        <label for="stripe_webhook_secret" class="block text-sm font-medium text-gray-700 mb-2">Stripe Webhook Secret</label>
                        <input type="password" name="stripe_webhook_secret" id="stripe_webhook_secret" 
                               value="{{ old('stripe_webhook_secret', $settings['stripe_webhook_secret'] ?? '') }}"
                               class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                               placeholder="whsec_...">
                    </div>

                    <div class="border-t border-gray-200 pt-6">
                        <h4 class="text-lg font-medium text-gray-900 mb-4">Cryptocurrency Settings</h4>
                        
                        <div class="flex items-center mb-4">
                            <input type="checkbox" name="crypto_payments_enabled" id="crypto_payments_enabled" value="1"
                                   {{ ($settings['crypto_payments_enabled'] ?? false) ? 'checked' : '' }}
                                   class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            <label for="crypto_payments_enabled" class="ml-2 text-sm text-gray-700">Enable Cryptocurrency Payments</label>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="btc_address" class="block text-sm font-medium text-gray-700 mb-2">Bitcoin Address</label>
                                <input type="text" name="btc_address" id="btc_address" 
                                       value="{{ old('btc_address', $settings['btc_address'] ?? '') }}"
                                       class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            </div>
                            
                            <div>
                                <label for="eth_address" class="block text-sm font-medium text-gray-700 mb-2">Ethereum Address</label>
                                <input type="text" name="eth_address" id="eth_address" 
                                       value="{{ old('eth_address', $settings['eth_address'] ?? '') }}"
                                       class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-end">
                        <button type="submit" 
                                class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition duration-150">
                            Save Payment Settings
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Email Settings -->
        <div id="email-tab" class="tab-content p-6 hidden">
            <form method="POST" action="{{ route('admin.settings.update') }}">
                @csrf
                <input type="hidden" name="section" value="email">
                
                <div class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="mail_driver" class="block text-sm font-medium text-gray-700 mb-2">Mail Driver</label>
                            <select name="mail_driver" id="mail_driver" 
                                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="smtp" {{ ($settings['mail_driver'] ?? 'smtp') === 'smtp' ? 'selected' : '' }}>SMTP</option>
                                <option value="mailgun" {{ ($settings['mail_driver'] ?? '') === 'mailgun' ? 'selected' : '' }}>Mailgun</option>
                                <option value="ses" {{ ($settings['mail_driver'] ?? '') === 'ses' ? 'selected' : '' }}>Amazon SES</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="mail_from_address" class="block text-sm font-medium text-gray-700 mb-2">From Address</label>
                            <input type="email" name="mail_from_address" id="mail_from_address" 
                                   value="{{ old('mail_from_address', $settings['mail_from_address'] ?? '') }}"
                                   class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="mail_from_name" class="block text-sm font-medium text-gray-700 mb-2">From Name</label>
                            <input type="text" name="mail_from_name" id="mail_from_name" 
                                   value="{{ old('mail_from_name', $settings['mail_from_name'] ?? '') }}"
                                   class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        </div>
                        
                        <div>
                            <label for="mail_host" class="block text-sm font-medium text-gray-700 mb-2">SMTP Host</label>
                            <input type="text" name="mail_host" id="mail_host" 
                                   value="{{ old('mail_host', $settings['mail_host'] ?? '') }}"
                                   class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <label for="mail_port" class="block text-sm font-medium text-gray-700 mb-2">SMTP Port</label>
                            <input type="number" name="mail_port" id="mail_port" 
                                   value="{{ old('mail_port', $settings['mail_port'] ?? '587') }}"
                                   class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        </div>
                        
                        <div>
                            <label for="mail_username" class="block text-sm font-medium text-gray-700 mb-2">SMTP Username</label>
                            <input type="text" name="mail_username" id="mail_username" 
                                   value="{{ old('mail_username', $settings['mail_username'] ?? '') }}"
                                   class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        </div>
                        
                        <div>
                            <label for="mail_password" class="block text-sm font-medium text-gray-700 mb-2">SMTP Password</label>
                            <input type="password" name="mail_password" id="mail_password" 
                                   value="{{ old('mail_password', $settings['mail_password'] ?? '') }}"
                                   class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        </div>
                    </div>

                    <div class="flex items-center">
                        <input type="checkbox" name="mail_encryption" id="mail_encryption" value="tls"
                               {{ ($settings['mail_encryption'] ?? 'tls') === 'tls' ? 'checked' : '' }}
                               class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                        <label for="mail_encryption" class="ml-2 text-sm text-gray-700">Use TLS Encryption</label>
                    </div>

                    <div class="flex justify-between">
                        <button type="button" onclick="testEmail()" 
                                class="bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-md transition duration-150">
                            Send Test Email
                        </button>
                        <button type="submit" 
                                class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition duration-150">
                            Save Email Settings
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Notifications Settings -->
        <div id="notifications-tab" class="tab-content p-6 hidden">
            <form method="POST" action="{{ route('admin.settings.update') }}">
                @csrf
                <input type="hidden" name="section" value="notifications">
                
                <div class="space-y-6">
                    <div class="space-y-4">
                        <h4 class="text-lg font-medium text-gray-900">Email Notifications</h4>
                        
                        <div class="space-y-3">
                            <div class="flex items-center">
                                <input type="checkbox" name="notify_new_user" id="notify_new_user" value="1"
                                       {{ ($settings['notify_new_user'] ?? true) ? 'checked' : '' }}
                                       class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                <label for="notify_new_user" class="ml-2 text-sm text-gray-700">New User Registration</label>
                            </div>
                            
                            <div class="flex items-center">
                                <input type="checkbox" name="notify_new_subscription" id="notify_new_subscription" value="1"
                                       {{ ($settings['notify_new_subscription'] ?? true) ? 'checked' : '' }}
                                       class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                <label for="notify_new_subscription" class="ml-2 text-sm text-gray-700">New Subscription</label>
                            </div>
                            
                            <div class="flex items-center">
                                <input type="checkbox" name="notify_payment_failed" id="notify_payment_failed" value="1"
                                       {{ ($settings['notify_payment_failed'] ?? true) ? 'checked' : '' }}
                                       class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                <label for="notify_payment_failed" class="ml-2 text-sm text-gray-700">Payment Failed</label>
                            </div>
                        </div>
                    </div>

                    <div class="border-t border-gray-200 pt-6">
                        <h4 class="text-lg font-medium text-gray-900 mb-4">Push Notifications</h4>
                        
                        <div class="flex items-center mb-4">
                            <input type="checkbox" name="push_notifications_enabled" id="push_notifications_enabled" value="1"
                                   {{ ($settings['push_notifications_enabled'] ?? false) ? 'checked' : '' }}
                                   class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            <label for="push_notifications_enabled" class="ml-2 text-sm text-gray-700">Enable Push Notifications</label>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="firebase_server_key" class="block text-sm font-medium text-gray-700 mb-2">Firebase Server Key</label>
                                <input type="password" name="firebase_server_key" id="firebase_server_key" 
                                       value="{{ old('firebase_server_key', $settings['firebase_server_key'] ?? '') }}"
                                       class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            </div>
                            
                            <div>
                                <label for="firebase_sender_id" class="block text-sm font-medium text-gray-700 mb-2">Firebase Sender ID</label>
                                <input type="text" name="firebase_sender_id" id="firebase_sender_id" 
                                       value="{{ old('firebase_sender_id', $settings['firebase_sender_id'] ?? '') }}"
                                       class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-end">
                        <button type="submit" 
                                class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition duration-150">
                            Save Notification Settings
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- System Settings -->
        <div id="system-tab" class="tab-content p-6 hidden">
            <div class="space-y-8">
                <!-- System Information -->
                <div class="bg-gray-50 rounded-lg p-6">
                    <h4 class="text-lg font-medium text-gray-900 mb-4">System Information</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <dl class="space-y-3">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">PHP Version</dt>
                                    <dd class="text-sm text-gray-900">{{ PHP_VERSION }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Laravel Version</dt>
                                    <dd class="text-sm text-gray-900">{{ app()->version() }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Environment</dt>
                                    <dd class="text-sm text-gray-900">{{ app()->environment() }}</dd>
                                </div>
                            </dl>
                        </div>
                        <div>
                            <dl class="space-y-3">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Database</dt>
                                    <dd class="text-sm text-gray-900">{{ config('database.default') }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Cache Driver</dt>
                                    <dd class="text-sm text-gray-900">{{ config('cache.default') }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Queue Driver</dt>
                                    <dd class="text-sm text-gray-900">{{ config('queue.default') }}</dd>
                                </div>
                            </dl>
                        </div>
                    </div>
                </div>

                <!-- System Actions -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="bg-white border border-gray-200 rounded-lg p-6">
                        <h5 class="text-lg font-medium text-gray-900 mb-2">Cache Management</h5>
                        <p class="text-sm text-gray-600 mb-4">Clear application cache to improve performance.</p>
                        <form method="POST" action="{{ route('admin.settings.clear-cache') }}">
                            @csrf
                            <button type="submit" 
                                    class="bg-yellow-600 hover:bg-yellow-700 text-white font-medium py-2 px-4 rounded-md transition duration-150">
                                Clear All Cache
                            </button>
                        </form>
                    </div>

                    <div class="bg-white border border-gray-200 rounded-lg p-6">
                        <h5 class="text-lg font-medium text-gray-900 mb-2">System Logs</h5>
                        <p class="text-sm text-gray-600 mb-4">View and manage system logs.</p>
                        <a href="{{ route('admin.settings.logs') }}" 
                           class="bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-md transition duration-150 inline-block">
                            View Logs
                        </a>
                    </div>
                </div>

                <!-- Backup & Maintenance -->
                <div class="bg-white border border-gray-200 rounded-lg p-6">
                    <h5 class="text-lg font-medium text-gray-900 mb-4">Backup & Maintenance</h5>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <form method="POST" action="{{ route('admin.settings.backup-database') }}">
                            @csrf
                            <button type="submit" 
                                    class="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-md transition duration-150">
                                Backup Database
                            </button>
                        </form>

                        <form method="POST" action="{{ route('admin.settings.optimize') }}">
                            @csrf
                            <button type="submit" 
                                    class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition duration-150">
                                Optimize System
                            </button>
                        </form>

                        <form method="POST" action="{{ route('admin.settings.maintenance-mode') }}">
                            @csrf
                            <button type="submit" 
                                    onclick="return confirm('Are you sure you want to toggle maintenance mode?')"
                                    class="w-full bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md transition duration-150">
                                Toggle Maintenance
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function showTab(tabName) {
    // Hide all tab contents
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.add('hidden');
    });
    
    // Remove active class from all buttons
    document.querySelectorAll('.tab-button').forEach(button => {
        button.classList.remove('active', 'border-blue-500', 'text-blue-600');
        button.classList.add('border-transparent', 'text-gray-500');
    });
    
    // Show selected tab
    document.getElementById(tabName + '-tab').classList.remove('hidden');
    
    // Add active class to clicked button
    event.target.classList.add('active', 'border-blue-500', 'text-blue-600');
    event.target.classList.remove('border-transparent', 'text-gray-500');
}

function testEmail() {
    fetch('{{ route("admin.settings.test-email") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Test email sent successfully!');
        } else {
            alert('Failed to send test email: ' + data.message);
        }
    })
    .catch(error => {
        alert('Error sending test email');
    });
}

// Initialize first tab as active
document.addEventListener('DOMContentLoaded', function() {
    const firstButton = document.querySelector('.tab-button');
    firstButton.classList.add('active', 'border-blue-500', 'text-blue-600');
    firstButton.classList.remove('border-transparent', 'text-gray-500');
});
</script>
@endsection
