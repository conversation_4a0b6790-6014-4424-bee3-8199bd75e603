@extends('layouts.admin')

@section('title', '- Notification Management')

@section('content')
<div class="p-6">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Notification Management</h1>
                <p class="mt-1 text-sm text-gray-600">Send and manage notifications to users</p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('admin.notifications.templates') }}" 
                   class="bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-md transition duration-150">
                    Manage Templates
                </a>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Sent</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ number_format($stats['total_sent']) }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Email</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ number_format($stats['email_notifications']) }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Push</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ number_format($stats['push_notifications']) }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 19h6v-2H4v2zM4 15h8v-2H4v2zM4 11h10V9H4v2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">In-App</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ number_format($stats['in_app_notifications']) }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Send Notification -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Send Notification</h3>
            </div>
            <div class="p-6">
                <form method="POST" action="{{ route('admin.notifications.send') }}">
                    @csrf
                    <div class="space-y-6">
                        <div>
                            <label for="recipient_type" class="block text-sm font-medium text-gray-700 mb-2">Recipients</label>
                            <select name="recipient_type" id="recipient_type" onchange="toggleRecipientValue()" 
                                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="all">All Users</option>
                                <option value="role">By Role</option>
                                <option value="subscription">By Subscription</option>
                                <option value="specific">Specific Users</option>
                            </select>
                        </div>

                        <div id="recipient_value_container" class="hidden">
                            <label for="recipient_value" class="block text-sm font-medium text-gray-700 mb-2">Select Recipients</label>
                            <select name="recipient_value" id="recipient_value" 
                                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <!-- Options will be populated by JavaScript -->
                            </select>
                        </div>

                        <div>
                            <label for="title" class="block text-sm font-medium text-gray-700 mb-2">Title</label>
                            <input type="text" name="title" id="title" required
                                   class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                   placeholder="Notification title...">
                        </div>

                        <div>
                            <label for="message" class="block text-sm font-medium text-gray-700 mb-2">Message</label>
                            <textarea name="message" id="message" rows="4" required
                                      class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                      placeholder="Notification message..."></textarea>
                        </div>

                        <div>
                            <label for="type" class="block text-sm font-medium text-gray-700 mb-2">Notification Type</label>
                            <select name="type" id="type" 
                                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="all">All Channels</option>
                                <option value="email">Email Only</option>
                                <option value="push">Push Only</option>
                                <option value="in_app">In-App Only</option>
                            </select>
                        </div>

                        <div class="flex items-center">
                            <input type="checkbox" name="send_immediately" id="send_immediately" checked
                                   class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            <label for="send_immediately" class="ml-2 text-sm text-gray-700">Send immediately</label>
                        </div>

                        <div id="schedule_container" class="hidden">
                            <label for="scheduled_at" class="block text-sm font-medium text-gray-700 mb-2">Schedule For</label>
                            <input type="datetime-local" name="scheduled_at" id="scheduled_at"
                                   class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        </div>

                        <div class="flex justify-end">
                            <button type="submit" 
                                    class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition duration-150">
                                Send Notification
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Broadcast Notification -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Broadcast to All Users</h3>
            </div>
            <div class="p-6">
                <form method="POST" action="{{ route('admin.notifications.broadcast') }}">
                    @csrf
                    <div class="space-y-6">
                        <div>
                            <label for="broadcast_title" class="block text-sm font-medium text-gray-700 mb-2">Title</label>
                            <input type="text" name="title" id="broadcast_title" required
                                   class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                   placeholder="Broadcast title...">
                        </div>

                        <div>
                            <label for="broadcast_message" class="block text-sm font-medium text-gray-700 mb-2">Message</label>
                            <textarea name="message" id="broadcast_message" rows="4" required
                                      class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                      placeholder="Broadcast message..."></textarea>
                        </div>

                        <div>
                            <label for="broadcast_type" class="block text-sm font-medium text-gray-700 mb-2">Type</label>
                            <select name="type" id="broadcast_type" 
                                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="all">All Channels</option>
                                <option value="email">Email Only</option>
                                <option value="push">Push Only</option>
                                <option value="in_app">In-App Only</option>
                            </select>
                        </div>

                        <div>
                            <label for="priority" class="block text-sm font-medium text-gray-700 mb-2">Priority</label>
                            <select name="priority" id="priority" 
                                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="normal">Normal</option>
                                <option value="high">High</option>
                                <option value="urgent">Urgent</option>
                                <option value="low">Low</option>
                            </select>
                        </div>

                        <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-yellow-800">Warning</h3>
                                    <div class="mt-2 text-sm text-yellow-700">
                                        <p>This will send a notification to all active users. Use with caution.</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="flex justify-end">
                            <button type="submit" 
                                    onclick="return confirm('Are you sure you want to broadcast to all users?')"
                                    class="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md transition duration-150">
                                Broadcast Now
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Recent Notifications -->
    <div class="mt-8 bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Recent Notifications</h3>
        </div>
        @if(count($recentNotifications) > 0)
            <ul class="divide-y divide-gray-200">
                @foreach($recentNotifications as $notification)
                    <li class="px-6 py-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-900">{{ $notification['title'] }}</p>
                                <p class="text-sm text-gray-500">{{ $notification['message'] }}</p>
                                <p class="text-xs text-gray-400 mt-1">
                                    Sent {{ $notification['sent_at'] }} • {{ $notification['recipients'] }} recipients
                                </p>
                            </div>
                            <div class="flex items-center">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    {{ ucfirst($notification['type']) }}
                                </span>
                            </div>
                        </div>
                    </li>
                @endforeach
            </ul>
        @else
            <div class="px-6 py-12 text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 19h6v-2H4v2zM4 15h8v-2H4v2zM4 11h10V9H4v2z"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No notifications sent yet</h3>
                <p class="mt-1 text-sm text-gray-500">Start by sending your first notification above.</p>
            </div>
        @endif
    </div>

    <!-- Test Notification -->
    <div class="mt-8 bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Test Notification</h3>
        </div>
        <div class="p-6">
            <form method="POST" action="{{ route('admin.notifications.test') }}">
                @csrf
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label for="test_type" class="block text-sm font-medium text-gray-700 mb-2">Type</label>
                        <select name="type" id="test_type" 
                                class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            <option value="email">Email</option>
                            <option value="push">Push</option>
                            <option value="in_app">In-App</option>
                        </select>
                    </div>
                    <div>
                        <label for="test_user_id" class="block text-sm font-medium text-gray-700 mb-2">Test User</label>
                        <select name="test_user_id" id="test_user_id" 
                                class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            <option value="{{ auth()->id() }}">Myself ({{ auth()->user()->name }})</option>
                        </select>
                    </div>
                    <div class="flex items-end">
                        <button type="submit" 
                                class="w-full bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-md transition duration-150">
                            Send Test
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function toggleRecipientValue() {
    const recipientType = document.getElementById('recipient_type').value;
    const container = document.getElementById('recipient_value_container');
    const select = document.getElementById('recipient_value');
    
    if (recipientType === 'all') {
        container.classList.add('hidden');
    } else {
        container.classList.remove('hidden');
        
        // Clear existing options
        select.innerHTML = '';
        
        // Add options based on type
        if (recipientType === 'role') {
            select.innerHTML = `
                <option value="student">Students</option>
                <option value="mentor">Mentors</option>
                <option value="admin">Admins</option>
            `;
        } else if (recipientType === 'subscription') {
            select.innerHTML = `
                <option value="1">Student Plan</option>
                <option value="2">Hustler Plan</option>
                <option value="3">Elite Plan</option>
            `;
        } else if (recipientType === 'specific') {
            select.innerHTML = `<option value="">Select users...</option>`;
            // In a real implementation, you'd populate this with users via AJAX
        }
    }
}

// Toggle schedule container
document.getElementById('send_immediately').addEventListener('change', function() {
    const scheduleContainer = document.getElementById('schedule_container');
    if (this.checked) {
        scheduleContainer.classList.add('hidden');
    } else {
        scheduleContainer.classList.remove('hidden');
    }
});
</script>
@endsection

@push('styles')
<style>
.notification-card {
    transition: all 0.2s ease-in-out;
}
.notification-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
</style>
@endpush
