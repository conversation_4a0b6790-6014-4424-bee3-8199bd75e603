<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('live_calls', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('description');
            $table->foreignId('mentor_id')->constrained('users')->onDelete('cascade');
            $table->datetime('scheduled_at');
            $table->integer('duration_minutes')->default(60);
            $table->string('meeting_url')->nullable(); // Zoom, Teams, etc.
            $table->string('meeting_id')->nullable();
            $table->string('meeting_password')->nullable();
            $table->string('recording_url')->nullable();
            $table->integer('max_attendees')->nullable();
            $table->json('required_plans')->nullable(); // Which plans can attend
            $table->enum('status', ['scheduled', 'live', 'completed', 'cancelled'])->default('scheduled');
            $table->boolean('is_recorded')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('live_calls');
    }
};
