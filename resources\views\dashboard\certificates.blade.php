@extends('layouts.dashboard')

@section('title', 'My Certificates')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">My Certificates</h1>
            <p class="mb-0 text-muted">Your achievements and course completion certificates</p>
        </div>
        <div>
            <button class="btn btn-outline-primary" onclick="shareAllCertificates()">
                <i class="fas fa-share-alt"></i> Share All
            </button>
        </div>
    </div>

    <!-- Certificate Stats -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Total Certificates</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $certificates->count() }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-certificate fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">This Month</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['this_month'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Most Recent</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ $stats['most_recent'] ? $stats['most_recent']->diffForHumans() : 'None' }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Completion Rate</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['completion_rate'] }}%</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-pie fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @if($certificates->count() > 0)
    <!-- Certificates Grid -->
    <div class="row">
        @foreach($certificates as $certificate)
        <div class="col-lg-6 col-xl-4 mb-4">
            <div class="certificate-card">
                <div class="certificate-preview">
                    <div class="certificate-header">
                        <div class="certificate-logo">
                            <img src="{{ asset('images/logo.png') }}" alt="The Real World">
                        </div>
                        <div class="certificate-title">Certificate of Completion</div>
                    </div>
                    
                    <div class="certificate-body">
                        <div class="certificate-recipient">
                            <div class="recipient-label">This is to certify that</div>
                            <div class="recipient-name">{{ auth()->user()->name }}</div>
                            <div class="completion-text">has successfully completed</div>
                        </div>
                        
                        <div class="certificate-course">
                            <div class="course-title">{{ $certificate->course->title }}</div>
                            <div class="course-details">
                                <div class="course-mentor">Instructor: {{ $certificate->course->mentor->name }}</div>
                                <div class="course-date">Completed: {{ $certificate->issued_at->format('F d, Y') }}</div>
                            </div>
                        </div>
                        
                        <div class="certificate-footer">
                            <div class="certificate-number">Certificate #{{ $certificate->certificate_number }}</div>
                            <div class="certificate-seal">
                                <i class="fas fa-seal"></i>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="certificate-actions">
                    <div class="certificate-info">
                        <h6 class="certificate-course-title">{{ $certificate->course->title }}</h6>
                        <p class="certificate-meta">
                            <i class="fas fa-user"></i> {{ $certificate->course->mentor->name }}
                            <span class="mx-2">•</span>
                            <i class="fas fa-calendar"></i> {{ $certificate->issued_at->format('M d, Y') }}
                        </p>
                    </div>
                    
                    <div class="certificate-buttons">
                        <a href="{{ route('certificates.show', $certificate) }}" 
                           class="btn btn-sm btn-outline-primary" target="_blank">
                            <i class="fas fa-eye"></i> View
                        </a>
                        <a href="{{ route('certificates.download', $certificate) }}" 
                           class="btn btn-sm btn-primary">
                            <i class="fas fa-download"></i> Download
                        </a>
                        <button class="btn btn-sm btn-outline-secondary" 
                                onclick="shareCertificate('{{ $certificate->id }}')">
                            <i class="fas fa-share-alt"></i> Share
                        </button>
                    </div>
                </div>
            </div>
        </div>
        @endforeach
    </div>

    <!-- Pagination -->
    @if($certificates->hasPages())
    <div class="d-flex justify-content-center">
        {{ $certificates->links() }}
    </div>
    @endif

    @else
    <!-- Empty State -->
    <div class="card shadow">
        <div class="card-body text-center py-5">
            <div class="empty-state">
                <i class="fas fa-certificate fa-4x text-gray-300 mb-4"></i>
                <h4 class="text-gray-600 mb-3">No Certificates Yet</h4>
                <p class="text-muted mb-4">
                    Complete courses to earn certificates and showcase your achievements. 
                    Each completed course will award you with a verified certificate.
                </p>
                <a href="{{ route('courses.index') }}" class="btn btn-primary">
                    <i class="fas fa-search"></i> Browse Courses
                </a>
            </div>
        </div>
    </div>
    @endif
</div>

<!-- Share Certificate Modal -->
<div class="modal fade" id="shareCertificateModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Share Certificate</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="share-options">
                    <h6 class="mb-3">Share on Social Media</h6>
                    <div class="social-buttons">
                        <button class="btn btn-social btn-linkedin" onclick="shareOnLinkedIn()">
                            <i class="fab fa-linkedin"></i> LinkedIn
                        </button>
                        <button class="btn btn-social btn-twitter" onclick="shareOnTwitter()">
                            <i class="fab fa-twitter"></i> Twitter
                        </button>
                        <button class="btn btn-social btn-facebook" onclick="shareOnFacebook()">
                            <i class="fab fa-facebook"></i> Facebook
                        </button>
                    </div>
                    
                    <hr>
                    
                    <h6 class="mb-3">Copy Link</h6>
                    <div class="input-group">
                        <input type="text" class="form-control" id="certificateLink" readonly>
                        <div class="input-group-append">
                            <button class="btn btn-outline-secondary" onclick="copyCertificateLink()">
                                <i class="fas fa-copy"></i> Copy
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.certificate-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.certificate-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.certificate-preview {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 3px solid #d4af37;
    margin: 1rem;
    padding: 1.5rem;
    border-radius: 10px;
    position: relative;
    min-height: 300px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.certificate-header {
    text-align: center;
    margin-bottom: 1rem;
}

.certificate-logo img {
    height: 40px;
    margin-bottom: 0.5rem;
}

.certificate-title {
    font-size: 1.2rem;
    font-weight: bold;
    color: #2c3e50;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.certificate-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    text-align: center;
}

.recipient-label {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 0.5rem;
}

.recipient-name {
    font-size: 1.5rem;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    border-bottom: 2px solid #d4af37;
    display: inline-block;
    padding-bottom: 0.25rem;
}

.completion-text {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 1rem;
}

.course-title {
    font-size: 1.1rem;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.course-details {
    font-size: 0.8rem;
    color: #6c757d;
}

.certificate-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #dee2e6;
}

.certificate-number {
    font-size: 0.8rem;
    color: #6c757d;
}

.certificate-seal {
    color: #d4af37;
    font-size: 1.5rem;
}

.certificate-actions {
    padding: 1.5rem;
    border-top: 1px solid #e9ecef;
    flex-shrink: 0;
}

.certificate-course-title {
    font-size: 1rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    line-height: 1.3;
}

.certificate-meta {
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

.certificate-meta i {
    margin-right: 0.25rem;
}

.certificate-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.certificate-buttons .btn {
    flex: 1;
    min-width: 70px;
}

.empty-state {
    max-width: 400px;
    margin: 0 auto;
}

.social-buttons {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.btn-social {
    flex: 1;
    min-width: 120px;
    color: white;
    border: none;
}

.btn-linkedin {
    background-color: #0077b5;
}

.btn-twitter {
    background-color: #1da1f2;
}

.btn-facebook {
    background-color: #1877f2;
}

.btn-social:hover {
    opacity: 0.9;
    color: white;
}

@media (max-width: 768px) {
    .certificate-buttons {
        flex-direction: column;
    }
    
    .certificate-buttons .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .social-buttons {
        flex-direction: column;
    }
    
    .certificate-meta {
        flex-direction: column;
        align-items: flex-start;
    }
}
</style>
@endpush

@push('scripts')
<script>
let currentCertificateId = null;

function shareCertificate(certificateId) {
    currentCertificateId = certificateId;
    const certificateUrl = `{{ url('/certificates/verify') }}/${certificateId}`;
    document.getElementById('certificateLink').value = certificateUrl;
    $('#shareCertificateModal').modal('show');
}

function shareAllCertificates() {
    const profileUrl = `{{ route('profile.certificates') }}`;
    if (navigator.share) {
        navigator.share({
            title: 'My Certificates - The Real World',
            text: 'Check out my achievements and certificates from The Real World!',
            url: profileUrl
        });
    } else {
        // Fallback to copying link
        navigator.clipboard.writeText(profileUrl).then(() => {
            showToast('Profile link copied to clipboard!', 'success');
        });
    }
}

function shareOnLinkedIn() {
    const certificateUrl = document.getElementById('certificateLink').value;
    const text = 'I just completed a course at The Real World! Check out my certificate:';
    const linkedInUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(certificateUrl)}&summary=${encodeURIComponent(text)}`;
    window.open(linkedInUrl, '_blank', 'width=600,height=400');
}

function shareOnTwitter() {
    const certificateUrl = document.getElementById('certificateLink').value;
    const text = 'I just completed a course at The Real World! 🎓 Check out my certificate:';
    const twitterUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(certificateUrl)}`;
    window.open(twitterUrl, '_blank', 'width=600,height=400');
}

function shareOnFacebook() {
    const certificateUrl = document.getElementById('certificateLink').value;
    const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(certificateUrl)}`;
    window.open(facebookUrl, '_blank', 'width=600,height=400');
}

function copyCertificateLink() {
    const linkInput = document.getElementById('certificateLink');
    linkInput.select();
    document.execCommand('copy');
    
    showToast('Certificate link copied to clipboard!', 'success');
}

function showToast(message, type = 'info') {
    // Create toast notification
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} toast-notification`;
    toast.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check' : 'info'}-circle"></i>
        ${message}
    `;
    
    // Add to page
    document.body.appendChild(toast);
    
    // Show toast
    setTimeout(() => toast.classList.add('show'), 100);
    
    // Remove toast
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => document.body.removeChild(toast), 300);
    }, 3000);
}
</script>

<style>
.toast-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    min-width: 300px;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
}

.toast-notification.show {
    opacity: 1;
    transform: translateX(0);
}

.toast-notification i {
    margin-right: 0.5rem;
}
</style>
@endpush
