@extends('layouts.app')

@section('title', 'Mentor Dashboard')

@section('content')
<div class="mentor-dashboard">
    <div class="container-fluid">
        <!-- Dashboard Header -->
        <div class="dashboard-header">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="dashboard-title">Welcome back, {{ $mentor->name }}!</h1>
                    <p class="dashboard-subtitle">Here's what's happening with your courses and students</p>
                </div>
                <div class="col-lg-4 text-right">
                    <div class="header-actions">
                        <a href="{{ route('mentor.courses.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Create Course
                        </a>
                        <a href="{{ route('mentor.live-calls.create') }}" class="btn btn-outline-primary">
                            <i class="fas fa-video"></i> Schedule Live Call
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="stats-section">
            <div class="row">
                <div class="col-lg-3 col-md-6">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-book text-primary"></i>
                        </div>
                        <div class="stat-content">
                            <h3 class="stat-number">{{ $stats['total_courses'] }}</h3>
                            <p class="stat-label">Total Courses</p>
                            <div class="stat-change positive">
                                <i class="fas fa-arrow-up"></i> +2 this month
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users text-success"></i>
                        </div>
                        <div class="stat-content">
                            <h3 class="stat-number">{{ number_format($stats['total_students']) }}</h3>
                            <p class="stat-label">Total Students</p>
                            <div class="stat-change positive">
                                <i class="fas fa-arrow-up"></i> +{{ $stats['active_students'] }} active
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-dollar-sign text-warning"></i>
                        </div>
                        <div class="stat-content">
                            <h3 class="stat-number">${{ number_format($stats['total_revenue']) }}</h3>
                            <p class="stat-label">Total Revenue</p>
                            <div class="stat-change positive">
                                <i class="fas fa-arrow-up"></i> +15% this month
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-star text-info"></i>
                        </div>
                        <div class="stat-content">
                            <h3 class="stat-number">{{ number_format($stats['average_rating'], 1) }}</h3>
                            <p class="stat-label">Average Rating</p>
                            <div class="stat-change positive">
                                <i class="fas fa-arrow-up"></i> {{ number_format($stats['completion_rate']) }}% completion
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Main Content -->
            <div class="col-lg-8">
                <!-- Revenue Chart -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3>Revenue Overview</h3>
                        <div class="card-actions">
                            <select class="form-control form-control-sm" id="revenueTimeframe">
                                <option value="12months">Last 12 Months</option>
                                <option value="6months">Last 6 Months</option>
                                <option value="3months">Last 3 Months</option>
                            </select>
                        </div>
                    </div>
                    <div class="card-body">
                        <canvas id="revenueChart" height="300"></canvas>
                    </div>
                </div>

                <!-- Top Performing Courses -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3>Top Performing Courses</h3>
                        <a href="{{ route('mentor.courses.index') }}" class="btn btn-sm btn-outline-primary">View All</a>
                    </div>
                    <div class="card-body">
                        <div class="courses-list">
                            @foreach($topCourses as $course)
                            <div class="course-item">
                                <div class="course-thumbnail">
                                    <img src="{{ $course->thumbnail ? asset('storage/' . $course->thumbnail) : asset('images/default-course-thumbnail.jpg') }}" 
                                         alt="{{ $course->title }}">
                                </div>
                                <div class="course-info">
                                    <h4 class="course-title">{{ $course->title }}</h4>
                                    <p class="course-category">{{ $course->category->name }}</p>
                                    <div class="course-stats">
                                        <span class="stat">
                                            <i class="fas fa-users"></i>
                                            {{ $course->enrollments_count }} students
                                        </span>
                                        <span class="stat">
                                            <i class="fas fa-star"></i>
                                            {{ number_format($course->average_rating ?? 0, 1) }}
                                        </span>
                                        <span class="stat">
                                            <i class="fas fa-dollar-sign"></i>
                                            ${{ number_format($course->enrollments_count * 50) }}
                                        </span>
                                    </div>
                                </div>
                                <div class="course-actions">
                                    <a href="{{ route('mentor.courses.show', $course) }}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('mentor.courses.edit', $course) }}" class="btn btn-sm btn-outline-secondary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                </div>

                <!-- Recent Enrollments -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3>Recent Enrollments</h3>
                        <span class="badge badge-primary">{{ $recentEnrollments->count() }} new</span>
                    </div>
                    <div class="card-body">
                        <div class="enrollments-list">
                            @forelse($recentEnrollments as $enrollment)
                            <div class="enrollment-item">
                                <div class="student-avatar">
                                    <img src="{{ $enrollment->user->avatar ? asset('storage/' . $enrollment->user->avatar) : asset('images/default-avatar.png') }}" 
                                         alt="{{ $enrollment->user->name }}">
                                </div>
                                <div class="enrollment-info">
                                    <h5 class="student-name">{{ $enrollment->user->name }}</h5>
                                    <p class="course-name">{{ $enrollment->course->title }}</p>
                                    <small class="enrollment-date">{{ $enrollment->created_at->diffForHumans() }}</small>
                                </div>
                                <div class="enrollment-status">
                                    @if($enrollment->completed_at)
                                    <span class="badge badge-success">Completed</span>
                                    @else
                                    <span class="badge badge-primary">In Progress</span>
                                    @endif
                                </div>
                            </div>
                            @empty
                            <div class="empty-state">
                                <i class="fas fa-users"></i>
                                <p>No recent enrollments</p>
                            </div>
                            @endforelse
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Upcoming Live Calls -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3>Upcoming Live Calls</h3>
                        <a href="{{ route('mentor.live-calls.create') }}" class="btn btn-sm btn-primary">
                            <i class="fas fa-plus"></i>
                        </a>
                    </div>
                    <div class="card-body">
                        <div class="live-calls-list">
                            @forelse($upcomingLiveCalls as $liveCall)
                            <div class="live-call-item">
                                <div class="call-date">
                                    <div class="date-day">{{ $liveCall->scheduled_at->format('d') }}</div>
                                    <div class="date-month">{{ $liveCall->scheduled_at->format('M') }}</div>
                                </div>
                                <div class="call-info">
                                    <h5 class="call-title">{{ $liveCall->title }}</h5>
                                    <p class="call-time">
                                        <i class="fas fa-clock"></i>
                                        {{ $liveCall->scheduled_at->format('g:i A') }}
                                    </p>
                                    <p class="call-participants">
                                        <i class="fas fa-users"></i>
                                        {{ $liveCall->participants_count ?? 0 }} registered
                                    </p>
                                </div>
                                <div class="call-actions">
                                    <a href="{{ route('mentor.live-calls.show', $liveCall) }}" class="btn btn-sm btn-outline-primary">
                                        View
                                    </a>
                                </div>
                            </div>
                            @empty
                            <div class="empty-state">
                                <i class="fas fa-video"></i>
                                <p>No upcoming calls</p>
                                <a href="{{ route('mentor.live-calls.create') }}" class="btn btn-sm btn-primary">
                                    Schedule Call
                                </a>
                            </div>
                            @endforelse
                        </div>
                    </div>
                </div>

                <!-- Recent Reviews -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3>Recent Reviews</h3>
                        <a href="{{ route('mentor.reviews.index') }}" class="btn btn-sm btn-outline-primary">View All</a>
                    </div>
                    <div class="card-body">
                        <div class="reviews-list">
                            @forelse($recentReviews as $review)
                            <div class="review-item">
                                <div class="review-header">
                                    <div class="reviewer-info">
                                        <img src="{{ $review->user->avatar ? asset('storage/' . $review->user->avatar) : asset('images/default-avatar.png') }}" 
                                             alt="{{ $review->user->name }}" class="reviewer-avatar">
                                        <div class="reviewer-details">
                                            <span class="reviewer-name">{{ $review->user->name }}</span>
                                            <div class="review-rating">
                                                @for($i = 1; $i <= 5; $i++)
                                                    <i class="fas fa-star {{ $i <= $review->rating ? 'active' : '' }}"></i>
                                                @endfor
                                            </div>
                                        </div>
                                    </div>
                                    <small class="review-date">{{ $review->created_at->diffForHumans() }}</small>
                                </div>
                                <div class="review-content">
                                    <p>{{ Str::limit($review->comment, 100) }}</p>
                                    <small class="course-name">{{ $review->course->title }}</small>
                                </div>
                            </div>
                            @empty
                            <div class="empty-state">
                                <i class="fas fa-star"></i>
                                <p>No recent reviews</p>
                            </div>
                            @endforelse
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3>Quick Actions</h3>
                    </div>
                    <div class="card-body">
                        <div class="quick-actions">
                            <a href="{{ route('mentor.courses.create') }}" class="quick-action">
                                <div class="action-icon">
                                    <i class="fas fa-plus"></i>
                                </div>
                                <span>Create Course</span>
                            </a>
                            
                            <a href="{{ route('mentor.live-calls.create') }}" class="quick-action">
                                <div class="action-icon">
                                    <i class="fas fa-video"></i>
                                </div>
                                <span>Schedule Call</span>
                            </a>
                            
                            <a href="{{ route('mentor.analytics') }}" class="quick-action">
                                <div class="action-icon">
                                    <i class="fas fa-chart-bar"></i>
                                </div>
                                <span>View Analytics</span>
                            </a>
                            
                            <a href="{{ route('mentor.students.index') }}" class="quick-action">
                                <div class="action-icon">
                                    <i class="fas fa-users"></i>
                                </div>
                                <span>Manage Students</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.mentor-dashboard {
    background: #f8f9fc;
    min-height: 100vh;
    padding: 2rem 0;
}

.dashboard-header {
    margin-bottom: 2rem;
}

.dashboard-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.dashboard-subtitle {
    color: #718096;
    font-size: 1.125rem;
    margin: 0;
}

.header-actions {
    display: flex;
    gap: 1rem;
}

.stats-section {
    margin-bottom: 3rem;
}

.stat-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: rgba(102, 126, 234, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1.5rem;
    font-size: 1.5rem;
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.25rem;
}

.stat-label {
    color: #718096;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.stat-change {
    font-size: 0.875rem;
    font-weight: 500;
}

.stat-change.positive {
    color: #48bb78;
}

.stat-change.negative {
    color: #e53e3e;
}

.dashboard-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    margin-bottom: 2rem;
    overflow: hidden;
}

.card-header {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2d3748;
    margin: 0;
}

.card-body {
    padding: 2rem;
}

.courses-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.course-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.course-item:hover {
    border-color: #667eea;
    background: #f7fafc;
}

.course-thumbnail {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    overflow: hidden;
    margin-right: 1rem;
    flex-shrink: 0;
}

.course-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.course-info {
    flex: 1;
}

.course-title {
    font-size: 1rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.25rem;
}

.course-category {
    color: #718096;
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
}

.course-stats {
    display: flex;
    gap: 1rem;
}

.course-stats .stat {
    font-size: 0.75rem;
    color: #4a5568;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.course-actions {
    display: flex;
    gap: 0.5rem;
}

.enrollments-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.enrollment-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 10px;
}

.student-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 1rem;
    flex-shrink: 0;
}

.student-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.enrollment-info {
    flex: 1;
}

.student-name {
    font-size: 1rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.25rem;
}

.course-name {
    color: #718096;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

.enrollment-date {
    color: #a0aec0;
    font-size: 0.75rem;
}

.enrollment-status {
    margin-left: 1rem;
}

.live-calls-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.live-call-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 10px;
}

.call-date {
    width: 50px;
    text-align: center;
    margin-right: 1rem;
    flex-shrink: 0;
}

.date-day {
    font-size: 1.25rem;
    font-weight: 700;
    color: #2d3748;
    line-height: 1;
}

.date-month {
    font-size: 0.75rem;
    color: #718096;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.call-info {
    flex: 1;
}

.call-title {
    font-size: 1rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.25rem;
}

.call-time,
.call-participants {
    color: #718096;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.call-actions {
    margin-left: 1rem;
}

.reviews-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.review-item {
    padding: 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 10px;
}

.review-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.75rem;
}

.reviewer-info {
    display: flex;
    align-items: center;
}

.reviewer-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 0.75rem;
}

.reviewer-name {
    font-weight: 600;
    color: #2d3748;
    font-size: 0.875rem;
    display: block;
    margin-bottom: 0.25rem;
}

.review-rating {
    display: flex;
    gap: 0.125rem;
}

.review-rating i {
    font-size: 0.75rem;
    color: #e2e8f0;
}

.review-rating i.active {
    color: #ffd700;
}

.review-date {
    color: #a0aec0;
    font-size: 0.75rem;
}

.review-content p {
    color: #4a5568;
    font-size: 0.875rem;
    line-height: 1.5;
    margin-bottom: 0.5rem;
}

.review-content .course-name {
    color: #718096;
    font-size: 0.75rem;
}

.quick-actions {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.quick-action {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1.5rem 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 10px;
    text-decoration: none;
    color: #4a5568;
    transition: all 0.3s ease;
}

.quick-action:hover {
    border-color: #667eea;
    background: #f7fafc;
    color: #667eea;
    text-decoration: none;
}

.action-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: rgba(102, 126, 234, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0.75rem;
    font-size: 1.25rem;
    color: #667eea;
}

.quick-action span {
    font-size: 0.875rem;
    font-weight: 500;
    text-align: center;
}

.empty-state {
    text-align: center;
    padding: 2rem;
    color: #a0aec0;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state p {
    margin-bottom: 1rem;
}

@media (max-width: 768px) {
    .dashboard-title {
        font-size: 2rem;
    }

    .header-actions {
        flex-direction: column;
        gap: 0.5rem;
        margin-top: 1rem;
    }

    .stat-card {
        flex-direction: column;
        text-align: center;
    }

    .stat-icon {
        margin-right: 0;
        margin-bottom: 1rem;
    }

    .course-item,
    .enrollment-item,
    .live-call-item {
        flex-direction: column;
        text-align: center;
    }

    .course-thumbnail,
    .student-avatar {
        margin-right: 0;
        margin-bottom: 1rem;
    }

    .call-date {
        margin-right: 0;
        margin-bottom: 1rem;
    }

    .quick-actions {
        grid-template-columns: 1fr;
    }
}
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Revenue Chart
const revenueCtx = document.getElementById('revenueChart').getContext('2d');
const revenueChart = new Chart(revenueCtx, {
    type: 'line',
    data: {
        labels: @json($revenueData['labels']),
        datasets: [{
            label: 'Revenue ($)',
            data: @json($revenueData['data']),
            borderColor: '#667eea',
            backgroundColor: 'rgba(102, 126, 234, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4,
            pointBackgroundColor: '#667eea',
            pointBorderColor: '#ffffff',
            pointBorderWidth: 2,
            pointRadius: 6,
            pointHoverRadius: 8,
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                grid: {
                    color: 'rgba(0, 0, 0, 0.05)'
                },
                ticks: {
                    callback: function(value) {
                        return '$' + value.toLocaleString();
                    }
                }
            },
            x: {
                grid: {
                    display: false
                }
            }
        },
        elements: {
            point: {
                hoverBackgroundColor: '#667eea'
            }
        }
    }
});

// Update chart based on timeframe selection
document.getElementById('revenueTimeframe').addEventListener('change', function() {
    // This would make an AJAX call to get new data
    console.log('Timeframe changed to:', this.value);
});

// Auto-refresh dashboard data every 5 minutes
setInterval(function() {
    // This would refresh key metrics
    console.log('Refreshing dashboard data...');
}, 300000);

// Animate stat numbers on page load
document.addEventListener('DOMContentLoaded', function() {
    const statNumbers = document.querySelectorAll('.stat-number');

    statNumbers.forEach(function(element) {
        const finalValue = parseInt(element.textContent.replace(/[^0-9]/g, ''));
        let currentValue = 0;
        const increment = finalValue / 50;
        const timer = setInterval(function() {
            currentValue += increment;
            if (currentValue >= finalValue) {
                currentValue = finalValue;
                clearInterval(timer);
            }

            // Format the number based on original format
            if (element.textContent.includes('$')) {
                element.textContent = '$' + Math.floor(currentValue).toLocaleString();
            } else if (element.textContent.includes('.')) {
                element.textContent = (currentValue / 10).toFixed(1);
            } else {
                element.textContent = Math.floor(currentValue).toLocaleString();
            }
        }, 20);
    });
});
</script>
@endpush
