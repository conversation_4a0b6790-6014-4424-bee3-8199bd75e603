<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get roles
        $adminRole = Role::where('name', 'admin')->first();
        $mentorRole = Role::where('name', 'mentor')->first();
        $userRole = Role::where('name', 'user')->first();

        // Create Super Admin
        $superAdmin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => '<PERSON>',
                'first_name' => '<PERSON>',
                'last_name' => 'Tate',
                'password' => Hash::make('password123'),
                'email_verified_at' => now(),
                'is_active' => true,
                'avatar' => 'avatars/andrew-tate.jpg',
                'bio' => 'Founder of The Real World. Entrepreneur, former kickboxer, and business mentor.',
                'country' => 'Romania',
            ]
        );
        if (!$superAdmin->hasRole('admin')) {
            $superAdmin->assignRole($adminRole);
        }

        // Create Admin Users
        $admin1 = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Tristan Tate',
                'first_name' => 'Tristan',
                'last_name' => 'Tate',
                'password' => Hash::make('password123'),
                'email_verified_at' => now(),
                'is_active' => true,
                'avatar' => 'avatars/tristan-tate.jpg',
                'bio' => 'Co-founder of The Real World. Business strategist and entrepreneur.',
                'country' => 'Romania',
            ]
        );
        if (!$admin1->hasRole('admin')) {
            $admin1->assignRole($adminRole);
        }

        // Create Mentor Users (The Real World Professors)
        $mentors = [
            [
                'first_name' => 'Luke',
                'last_name' => 'Belmar',
                'email' => '<EMAIL>',
                'specialty' => 'E-commerce & Dropshipping',
                'bio' => 'E-commerce expert who has built multiple 7-figure online businesses.',
                'avatar' => 'avatars/luke-belmar.jpg',
            ],
            [
                'first_name' => 'Jordan',
                'last_name' => 'Welch',
                'email' => '<EMAIL>',
                'specialty' => 'YouTube & Content Creation',
                'bio' => 'YouTube entrepreneur with millions of views and multiple successful channels.',
                'avatar' => 'avatars/jordan-welch.jpg',
            ],
            [
                'first_name' => 'Dylan',
                'last_name' => 'Madden',
                'email' => '<EMAIL>',
                'specialty' => 'Copywriting & Sales',
                'bio' => 'Master copywriter who has generated millions in sales through persuasive writing.',
                'avatar' => 'avatars/dylan-madden.jpg',
            ],
            [
                'first_name' => 'Luc',
                'last_name' => 'Lenglet',
                'email' => '<EMAIL>',
                'specialty' => 'Stocks & Trading',
                'bio' => 'Professional trader and investment strategist with years of market experience.',
                'avatar' => 'avatars/luc-lenglet.jpg',
            ],
            [
                'first_name' => 'Adam',
                'last_name' => 'Benayoun',
                'email' => '<EMAIL>',
                'specialty' => 'Amazon FBA',
                'bio' => 'Amazon FBA expert who has built successful private label businesses.',
                'avatar' => 'avatars/adam-benayoun.jpg',
            ],
            [
                'first_name' => 'Tate',
                'last_name' => 'Speech',
                'email' => '<EMAIL>',
                'specialty' => 'Freelancing & Client Acquisition',
                'bio' => 'Freelancing expert who teaches high-value skill monetization.',
                'avatar' => 'avatars/tate-speech.jpg',
            ],
            [
                'first_name' => 'Shuayb',
                'last_name' => 'Shuayb',
                'email' => '<EMAIL>',
                'specialty' => 'Cryptocurrency',
                'bio' => 'Cryptocurrency expert and blockchain technology specialist.',
                'avatar' => 'avatars/shuayb.jpg',
            ],
            [
                'first_name' => 'Arno',
                'last_name' => 'Arno',
                'email' => '<EMAIL>',
                'specialty' => 'Business Management',
                'bio' => 'Business management expert specializing in operations and scaling.',
                'avatar' => 'avatars/arno.jpg',
            ],
        ];

        foreach ($mentors as $mentorData) {
            $mentor = User::firstOrCreate(
                ['email' => $mentorData['email']],
                [
                    'name' => $mentorData['first_name'] . ' ' . $mentorData['last_name'],
                    'first_name' => $mentorData['first_name'],
                    'last_name' => $mentorData['last_name'],
                    'password' => Hash::make('password123'),
                    'email_verified_at' => now(),
                    'is_active' => true,
                    'avatar' => $mentorData['avatar'],
                    'bio' => $mentorData['bio'],
                    'country' => 'Romania',
                ]
            );
            if (!$mentor->hasRole('mentor')) {
                $mentor->assignRole($mentorRole);
            }
        }

        // Create Regular Users (Students)
        $students = [
            [
                'first_name' => 'John',
                'last_name' => 'Smith',
                'email' => '<EMAIL>',
                'country' => 'United States',
            ],
            [
                'first_name' => 'Sarah',
                'last_name' => 'Johnson',
                'email' => '<EMAIL>',
                'country' => 'Canada',
            ],
            [
                'first_name' => 'Michael',
                'last_name' => 'Brown',
                'email' => '<EMAIL>',
                'country' => 'United Kingdom',
            ],
            [
                'first_name' => 'Emma',
                'last_name' => 'Davis',
                'email' => '<EMAIL>',
                'country' => 'Australia',
            ],
            [
                'first_name' => 'David',
                'last_name' => 'Wilson',
                'email' => '<EMAIL>',
                'country' => 'Germany',
            ],
            [
                'first_name' => 'Lisa',
                'last_name' => 'Anderson',
                'email' => '<EMAIL>',
                'country' => 'France',
            ],
            [
                'first_name' => 'James',
                'last_name' => 'Taylor',
                'email' => '<EMAIL>',
                'country' => 'Italy',
            ],
            [
                'first_name' => 'Maria',
                'last_name' => 'Garcia',
                'email' => '<EMAIL>',
                'country' => 'Spain',
            ],
        ];

        foreach ($students as $studentData) {
            $student = User::firstOrCreate(
                ['email' => $studentData['email']],
                [
                    'name' => $studentData['first_name'] . ' ' . $studentData['last_name'],
                    'first_name' => $studentData['first_name'],
                    'last_name' => $studentData['last_name'],
                    'password' => Hash::make('password123'),
                    'email_verified_at' => now(),
                    'is_active' => true,
                    'country' => $studentData['country'],
                    'bio' => 'Student at The Real World, learning to build wealth and success.',
                ]
            );
            if (!$student->hasRole('user')) {
                $student->assignRole($userRole);
            }
        }

        // Create additional random users (only if we don't have enough)
        $existingUsersCount = User::count();
        if ($existingUsersCount < 70) {
            $usersToCreate = 70 - $existingUsersCount;
            User::factory($usersToCreate)->create()->each(function ($user) use ($userRole) {
                $user->assignRole($userRole);
            });
        }

        $this->command->info('Users seeded successfully!');
    }
}
