<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // Process crypto payments every 10 minutes
        $schedule->command('payments:process-crypto')->everyTenMinutes();

        // Generate certificates daily at 2 AM
        $schedule->command('certificates:generate')->dailyAt('02:00');

        // Security audit weekly
        $schedule->command('security:audit')->weekly();

        // Clean up old notifications monthly
        $schedule->command('notifications:cleanup')->monthly();

        // Backup database daily at 3 AM
        $schedule->command('backup:run')->dailyAt('03:00');
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
