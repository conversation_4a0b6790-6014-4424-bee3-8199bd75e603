<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SubscriptionPlan extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'monthly_price',
        'yearly_price',
        'features',
        'stripe_monthly_price_id',
        'stripe_yearly_price_id',
        'max_courses',
        'live_calls_access',
        'community_access',
        'mentor_access',
        'sort_order',
        'is_active',
        'is_featured',
    ];

    protected $casts = [
        'monthly_price' => 'decimal:2',
        'yearly_price' => 'decimal:2',
        'features' => 'array',
        'live_calls_access' => 'boolean',
        'community_access' => 'boolean',
        'mentor_access' => 'boolean',
        'is_active' => 'boolean',
        'is_featured' => 'boolean',
    ];

    /**
     * Get the user subscriptions for this plan.
     */
    public function userSubscriptions()
    {
        return $this->hasMany(UserSubscription::class);
    }

    /**
     * Get active subscriptions for this plan.
     */
    public function activeSubscriptions()
    {
        return $this->hasMany(UserSubscription::class)->where('status', 'active');
    }

    /**
     * Get the price for a specific billing cycle.
     */
    public function getPrice(string $cycle = 'monthly'): float
    {
        return $cycle === 'yearly' ? $this->yearly_price : $this->monthly_price;
    }

    /**
     * Get the Stripe price ID for a specific billing cycle.
     */
    public function getStripePriceId(string $cycle = 'monthly'): ?string
    {
        return $cycle === 'yearly' ? $this->stripe_yearly_price_id : $this->stripe_monthly_price_id;
    }

    /**
     * Scope to get active plans.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get featured plans.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope to order by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }
}
