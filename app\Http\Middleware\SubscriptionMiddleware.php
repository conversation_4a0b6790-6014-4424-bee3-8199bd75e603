<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class SubscriptionMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, $feature = null): Response
    {
        $user = $request->user();

        // Allow access if user is admin
        if ($user && $user->hasRole('admin')) {
            return $next($request);
        }

        // Check if user has active subscription
        if (!$user || !$user->activeSubscription) {
            return redirect()->route('pricing')->with('error', 'You need an active subscription to access this feature.');
        }

        // Check specific feature access if specified
        if ($feature && !$user->canAccess($feature)) {
            return redirect()->route('dashboard')->with('error', 'Your current plan does not include access to this feature. Please upgrade your subscription.');
        }

        return $next($request);
    }
}
