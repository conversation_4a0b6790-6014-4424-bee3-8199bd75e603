# The Real World LMS - Deployment Guide

This guide provides step-by-step instructions for deploying The Real World LMS to Hostinger Business hosting.

## Prerequisites

### Server Requirements
- **PHP**: 8.2 or higher
- **MySQL**: 8.0 or higher
- **Redis**: 6.0 or higher (for caching and sessions)
- **Node.js**: 18.0 or higher
- **Composer**: 2.0 or higher
- **SSL Certificate**: Required for production

### PHP Extensions Required
- BCMath
- Ctype
- cURL
- DOM
- Fileinfo
- JSON
- Mbstring
- OpenSSL
- PCRE
- PDO
- Tokenizer
- XML
- ZIP
- GD
- Redis

## Pre-Deployment Setup

### 1. Hostinger Configuration

1. **Access hPanel**
   - Log into your Hostinger account
   - Navigate to hPanel dashboard

2. **Database Setup**
   ```sql
   -- Create database and user
   CREATE DATABASE realworld_lms;
   CREATE USER 'lms_user'@'localhost' IDENTIFIED BY 'secure_password';
   GRANT ALL PRIVILEGES ON realworld_lms.* TO 'lms_user'@'localhost';
   FLUSH PRIVILEGES;
   ```

3. **Enable Required PHP Extensions**
   - Go to Advanced → PHP Configuration
   - Enable all required extensions listed above

4. **Set PHP Version**
   - Set PHP version to 8.2 or higher

### 2. Domain and SSL Setup

1. **Domain Configuration**
   - Point your domain to Hostinger nameservers
   - Configure DNS records in hPanel

2. **SSL Certificate**
   - Go to Security → SSL
   - Enable SSL for your domain
   - Choose "Let's Encrypt" for free SSL

### 3. File Manager Setup

1. **Access File Manager**
   - Go to Files → File Manager
   - Navigate to public_html directory

## Deployment Process

### Method 1: Automated Deployment (Recommended)

1. **Upload Deployment Script**
   ```bash
   # Upload deploy.sh to your server root directory
   chmod +x deploy.sh
   ```

2. **Configure Environment**
   ```bash
   # Copy and edit production environment file
   cp .env.production .env
   nano .env
   ```

3. **Run Deployment**
   ```bash
   ./deploy.sh deploy
   ```

### Method 2: Manual Deployment

#### Step 1: Upload Files

1. **Compress Project**
   ```bash
   # On your local machine
   tar -czf realworld-lms.tar.gz --exclude=node_modules --exclude=.git .
   ```

2. **Upload to Server**
   - Use File Manager or FTP to upload the compressed file
   - Extract in public_html directory

#### Step 2: Install Dependencies

```bash
# Navigate to project directory
cd /public_html

# Install PHP dependencies
composer install --no-dev --optimize-autoloader

# Install Node.js dependencies
npm ci --only=production

# Build assets
npm run build
```

#### Step 3: Environment Configuration

```bash
# Copy environment file
cp .env.production .env

# Generate application key
php artisan key:generate

# Edit environment variables
nano .env
```

#### Step 4: Database Setup

```bash
# Run migrations
php artisan migrate --force

# Seed database (optional)
php artisan db:seed --force
```

#### Step 5: Optimize Application

```bash
# Cache configuration
php artisan config:cache
php artisan route:cache
php artisan view:cache
php artisan event:cache

# Create storage link
php artisan storage:link
```

#### Step 6: Set Permissions

```bash
# Set proper permissions
find . -type f -exec chmod 644 {} \;
find . -type d -exec chmod 755 {} \;
chmod -R 775 storage bootstrap/cache
```

## Configuration Files

### .htaccess Configuration

Create `.htaccess` in public_html root:

```apache
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Redirect to public directory
    RewriteCond %{REQUEST_URI} !^/public/
    RewriteRule ^(.*)$ /public/$1 [L]
    
    # Handle Laravel routes
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule ^(.*)$ /public/index.php [QSA,L]
</IfModule>

# Security Headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
Header always set Referrer-Policy "strict-origin-when-cross-origin"
Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"

# Prevent access to sensitive files
<FilesMatch "\.(env|log|md)$">
    Order allow,deny
    Deny from all
</FilesMatch>
```

### Cron Jobs Setup

Add to crontab:
```bash
# Laravel scheduler
* * * * * cd /public_html && php artisan schedule:run >> /dev/null 2>&1

# Queue worker (if using queues)
* * * * * cd /public_html && php artisan queue:work --stop-when-empty >> /dev/null 2>&1
```

## Environment Variables

### Required Variables

```env
APP_NAME="The Real World LMS"
APP_ENV=production
APP_KEY=base64:YOUR_GENERATED_KEY
APP_DEBUG=false
APP_URL=https://yourdomain.com

DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=realworld_lms
DB_USERNAME=lms_user
DB_PASSWORD=your_secure_password

MAIL_MAILER=smtp
MAIL_HOST=smtp.hostinger.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_email_password
MAIL_ENCRYPTION=tls

STRIPE_KEY=pk_live_your_stripe_key
STRIPE_SECRET=sk_live_your_stripe_secret
```

## Post-Deployment

### 1. Health Check

```bash
# Run health check
./deploy.sh health

# Or manually check
curl -I https://yourdomain.com
```

### 2. Security Audit

```bash
# Run security audit
php artisan security:audit
```

### 3. Performance Testing

```bash
# Test page load times
curl -w "@curl-format.txt" -o /dev/null -s https://yourdomain.com
```

### 4. Backup Setup

```bash
# Test backup system
php artisan backup:run
```

## Monitoring and Maintenance

### 1. Log Monitoring

- Monitor logs in `storage/logs/`
- Set up log rotation
- Configure error notifications

### 2. Performance Monitoring

- Monitor server resources
- Track response times
- Monitor database performance

### 3. Security Monitoring

- Regular security audits
- Monitor failed login attempts
- Keep dependencies updated

### 4. Backup Strategy

- Daily automated backups
- Test backup restoration
- Store backups off-site

## Troubleshooting

### Common Issues

1. **500 Internal Server Error**
   - Check error logs
   - Verify file permissions
   - Check .env configuration

2. **Database Connection Error**
   - Verify database credentials
   - Check database server status
   - Test connection manually

3. **Asset Loading Issues**
   - Run `npm run build`
   - Check file permissions
   - Verify asset URLs

4. **Email Not Working**
   - Check SMTP settings
   - Verify email credentials
   - Test with mail command

### Performance Issues

1. **Slow Page Load**
   - Enable OPcache
   - Configure Redis caching
   - Optimize database queries

2. **High Memory Usage**
   - Increase PHP memory limit
   - Optimize Composer autoloader
   - Review memory-intensive operations

## Support and Updates

### Updating the Application

```bash
# Create backup
./deploy.sh backup

# Deploy new version
./deploy.sh deploy

# Run health check
./deploy.sh health
```

### Getting Help

- Check application logs
- Review error messages
- Contact support team

## Security Checklist

- [ ] SSL certificate installed and configured
- [ ] Environment variables secured
- [ ] File permissions set correctly
- [ ] Security headers configured
- [ ] Database credentials secured
- [ ] Two-factor authentication enabled for admins
- [ ] Regular security audits scheduled
- [ ] Backup system tested and working
- [ ] Error reporting configured
- [ ] Log monitoring set up

## Performance Checklist

- [ ] OPcache enabled and configured
- [ ] Redis caching enabled
- [ ] Database queries optimized
- [ ] Assets minified and compressed
- [ ] CDN configured (if applicable)
- [ ] Gzip compression enabled
- [ ] Browser caching configured
- [ ] Queue system configured
- [ ] Cron jobs set up
- [ ] Performance monitoring enabled

---

**Note**: This deployment guide assumes you have administrative access to your Hostinger Business hosting account. Some steps may require contacting Hostinger support for assistance.
