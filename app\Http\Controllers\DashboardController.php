<?php

namespace App\Http\Controllers;

use App\Models\Course;
use App\Models\LiveCall;
use App\Models\UserProgress;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class DashboardController extends Controller
{
    /**
     * Show the member dashboard.
     */
    public function index()
    {
        $user = Auth::user();

        // Get user's subscription info
        $subscription = $user->activeSubscription;

        // Get user's course progress
        $enrolledCourses = Course::whereHas('userProgress', function ($query) use ($user) {
            $query->where('user_id', $user->id);
        })->with(['mentor', 'userProgress' => function ($query) use ($user) {
            $query->where('user_id', $user->id);
        }])->get();

        // Calculate overall progress statistics
        $totalLessonsStarted = UserProgress::where('user_id', $user->id)->count();
        $totalLessonsCompleted = UserProgress::where('user_id', $user->id)
            ->where('is_completed', true)->count();
        $totalWatchTime = UserProgress::where('user_id', $user->id)
            ->sum('watch_time_seconds');

        // Get recently accessed courses
        $recentCourses = Course::whereHas('userProgress', function ($query) use ($user) {
            $query->where('user_id', $user->id)
                  ->where('started_at', '>=', now()->subDays(7));
        })->with('mentor')
          ->orderBy('updated_at', 'desc')
          ->take(4)
          ->get();

        // Get recommended courses based on user's subscription
        $recommendedCourses = collect();
        if ($subscription) {
            $recommendedCourses = Course::published()
                ->where(function ($query) use ($subscription) {
                    $query->whereJsonContains('required_plans', $subscription->subscriptionPlan->slug)
                          ->orWhereNull('required_plans')
                          ->orWhereJsonLength('required_plans', 0);
                })
                ->whereNotIn('id', $enrolledCourses->pluck('id'))
                ->with('mentor')
                ->featured()
                ->take(4)
                ->get();
        }

        // Get upcoming live calls (we'll create this model later)
        $upcomingCalls = collect(); // Placeholder for now

        // Get recent achievements/milestones
        $recentAchievements = $this->getRecentAchievements($user);

        // Calculate streak (consecutive days of activity)
        $currentStreak = $this->calculateLearningStreak($user);

        return view('dashboard.index', compact(
            'user',
            'subscription',
            'enrolledCourses',
            'totalLessonsStarted',
            'totalLessonsCompleted',
            'totalWatchTime',
            'recentCourses',
            'recommendedCourses',
            'upcomingCalls',
            'recentAchievements',
            'currentStreak'
        ));
    }

    /**
     * Show user's course progress.
     */
    public function courses()
    {
        $user = Auth::user();

        // Get all courses user has access to
        $accessibleCourses = Course::published()->get()->filter(function ($course) use ($user) {
            return $course->canBeAccessedBy($user);
        });

        // Get courses with progress
        $coursesWithProgress = $accessibleCourses->map(function ($course) use ($user) {
            $totalLessons = $course->publishedLessons->count();
            $completedLessons = UserProgress::where('user_id', $user->id)
                ->where('course_id', $course->id)
                ->where('is_completed', true)
                ->count();

            $course->progress_percentage = $totalLessons > 0 ? ($completedLessons / $totalLessons) * 100 : 0;
            $course->completed_lessons = $completedLessons;
            $course->total_lessons = $totalLessons;
            $course->is_started = UserProgress::where('user_id', $user->id)
                ->where('course_id', $course->id)
                ->exists();

            return $course;
        })->sortByDesc('is_started');

        return view('dashboard.courses', compact('coursesWithProgress'));
    }

    /**
     * Show user's learning statistics.
     */
    public function stats()
    {
        $user = Auth::user();

        // Get detailed statistics
        $stats = [
            'total_courses_enrolled' => Course::whereHas('userProgress', function ($query) use ($user) {
                $query->where('user_id', $user->id);
            })->count(),

            'total_courses_completed' => Course::whereHas('userProgress', function ($query) use ($user) {
                $query->where('user_id', $user->id)->where('is_completed', true);
            })->count(),

            'total_lessons_completed' => UserProgress::where('user_id', $user->id)
                ->where('is_completed', true)->count(),

            'total_watch_time_hours' => round(UserProgress::where('user_id', $user->id)
                ->sum('watch_time_seconds') / 3600, 1),

            'current_streak' => $this->calculateLearningStreak($user),

            'this_week_progress' => UserProgress::where('user_id', $user->id)
                ->where('updated_at', '>=', now()->startOfWeek())
                ->count(),

            'this_month_progress' => UserProgress::where('user_id', $user->id)
                ->where('updated_at', '>=', now()->startOfMonth())
                ->count(),
        ];

        // Get progress over time (last 30 days)
        $progressOverTime = [];
        for ($i = 29; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $count = UserProgress::where('user_id', $user->id)
                ->whereDate('updated_at', $date)
                ->count();
            $progressOverTime[] = [
                'date' => $date->format('M j'),
                'count' => $count
            ];
        }

        // Get category breakdown
        $categoryStats = Course::whereHas('userProgress', function ($query) use ($user) {
            $query->where('user_id', $user->id);
        })->selectRaw('category, COUNT(*) as count')
          ->groupBy('category')
          ->get();

        return view('dashboard.stats', compact('stats', 'progressOverTime', 'categoryStats'));
    }

    /**
     * Get recent achievements for the user.
     */
    private function getRecentAchievements($user)
    {
        $achievements = [];

        // Check for course completions in the last 7 days
        $recentCompletions = Course::whereHas('userProgress', function ($query) use ($user) {
            $query->where('user_id', $user->id)
                  ->where('is_completed', true)
                  ->where('completed_at', '>=', now()->subDays(7));
        })->with('userProgress')->get();

        foreach ($recentCompletions as $course) {
            $achievements[] = [
                'type' => 'course_completed',
                'title' => 'Course Completed!',
                'description' => "You completed '{$course->title}'",
                'date' => $course->userProgress->first()->completed_at,
                'icon' => '🎓'
            ];
        }

        // Check for learning streaks
        $streak = $this->calculateLearningStreak($user);
        if ($streak >= 7 && $streak % 7 == 0) {
            $achievements[] = [
                'type' => 'streak',
                'title' => 'Learning Streak!',
                'description' => "You've maintained a {$streak}-day learning streak",
                'date' => now(),
                'icon' => '🔥'
            ];
        }

        return collect($achievements)->sortByDesc('date')->take(5);
    }

    /**
     * Calculate user's learning streak.
     */
    private function calculateLearningStreak($user)
    {
        $streak = 0;
        $currentDate = now()->startOfDay();

        while (true) {
            $hasActivity = UserProgress::where('user_id', $user->id)
                ->whereDate('updated_at', $currentDate)
                ->exists();

            if (!$hasActivity) {
                break;
            }

            $streak++;
            $currentDate->subDay();
        }

        return $streak;
    }
}
