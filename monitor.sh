#!/bin/bash

# The Real World LMS Production Monitoring Script
# This script monitors the application health and performance

set -e

# Configuration
APP_URL="https://yourdomain.com"
APP_DIR="/public_html"
LOG_DIR="/var/log/trw-monitor"
ALERT_EMAIL="<EMAIL>"
SLACK_WEBHOOK=""

# Thresholds
CPU_THRESHOLD=80
MEMORY_THRESHOLD=80
DISK_THRESHOLD=85
RESPONSE_TIME_THRESHOLD=3000  # milliseconds
ERROR_RATE_THRESHOLD=5        # percentage

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Create log directory
mkdir -p "$LOG_DIR"

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}" | tee -a "$LOG_DIR/monitor.log"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}" | tee -a "$LOG_DIR/monitor.log"
}

warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}" | tee -a "$LOG_DIR/monitor.log"
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}" | tee -a "$LOG_DIR/monitor.log"
}

# Send alert function
send_alert() {
    local subject="$1"
    local message="$2"
    local severity="$3"
    
    # Email alert
    if [ -n "$ALERT_EMAIL" ]; then
        echo "$message" | mail -s "$subject" "$ALERT_EMAIL"
    fi
    
    # Slack alert
    if [ -n "$SLACK_WEBHOOK" ]; then
        local color="good"
        case "$severity" in
            "critical") color="danger" ;;
            "warning") color="warning" ;;
        esac
        
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"attachments\":[{\"color\":\"$color\",\"title\":\"$subject\",\"text\":\"$message\"}]}" \
            "$SLACK_WEBHOOK"
    fi
}

# Check application health
check_application_health() {
    log "Checking application health..."
    
    # Check if application is responding
    local response_code=$(curl -s -o /dev/null -w "%{http_code}" "$APP_URL" || echo "000")
    local response_time=$(curl -s -o /dev/null -w "%{time_total}" "$APP_URL" | awk '{print int($1*1000)}')
    
    if [ "$response_code" != "200" ]; then
        error "Application not responding - HTTP $response_code"
        send_alert "Application Down" "Application is not responding. HTTP status: $response_code" "critical"
        return 1
    fi
    
    if [ "$response_time" -gt "$RESPONSE_TIME_THRESHOLD" ]; then
        warning "Slow response time: ${response_time}ms"
        send_alert "Slow Response Time" "Application response time is ${response_time}ms (threshold: ${RESPONSE_TIME_THRESHOLD}ms)" "warning"
    fi
    
    log "Application health check passed (${response_time}ms)"
    return 0
}

# Check database connectivity
check_database() {
    log "Checking database connectivity..."
    
    cd "$APP_DIR"
    
    if php artisan tinker --execute="DB::connection()->getPdo(); echo 'Database OK';" > /dev/null 2>&1; then
        log "Database connectivity check passed"
        return 0
    else
        error "Database connectivity failed"
        send_alert "Database Connection Failed" "Cannot connect to database" "critical"
        return 1
    fi
}

# Check Redis connectivity
check_redis() {
    log "Checking Redis connectivity..."
    
    if redis-cli ping > /dev/null 2>&1; then
        log "Redis connectivity check passed"
        return 0
    else
        warning "Redis connectivity failed"
        send_alert "Redis Connection Failed" "Cannot connect to Redis server" "warning"
        return 1
    fi
}

# Check disk usage
check_disk_usage() {
    log "Checking disk usage..."
    
    local disk_usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    
    if [ "$disk_usage" -gt "$DISK_THRESHOLD" ]; then
        warning "High disk usage: ${disk_usage}%"
        send_alert "High Disk Usage" "Disk usage is at ${disk_usage}% (threshold: ${DISK_THRESHOLD}%)" "warning"
        return 1
    fi
    
    log "Disk usage check passed (${disk_usage}%)"
    return 0
}

# Check memory usage
check_memory_usage() {
    log "Checking memory usage..."
    
    local memory_usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    
    if [ "$memory_usage" -gt "$MEMORY_THRESHOLD" ]; then
        warning "High memory usage: ${memory_usage}%"
        send_alert "High Memory Usage" "Memory usage is at ${memory_usage}% (threshold: ${MEMORY_THRESHOLD}%)" "warning"
        return 1
    fi
    
    log "Memory usage check passed (${memory_usage}%)"
    return 0
}

# Check CPU usage
check_cpu_usage() {
    log "Checking CPU usage..."
    
    local cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | awk -F'%' '{print $1}')
    
    if (( $(echo "$cpu_usage > $CPU_THRESHOLD" | bc -l) )); then
        warning "High CPU usage: ${cpu_usage}%"
        send_alert "High CPU Usage" "CPU usage is at ${cpu_usage}% (threshold: ${CPU_THRESHOLD}%)" "warning"
        return 1
    fi
    
    log "CPU usage check passed (${cpu_usage}%)"
    return 0
}

# Check error logs
check_error_logs() {
    log "Checking error logs..."
    
    local log_file="$APP_DIR/storage/logs/laravel.log"
    local error_count=0
    
    if [ -f "$log_file" ]; then
        # Count errors in the last hour
        error_count=$(grep "$(date -d '1 hour ago' '+%Y-%m-%d %H')" "$log_file" | grep -c "ERROR" || echo "0")
        
        if [ "$error_count" -gt 10 ]; then
            warning "High error rate: $error_count errors in the last hour"
            send_alert "High Error Rate" "Found $error_count errors in the last hour" "warning"
            return 1
        fi
    fi
    
    log "Error log check passed ($error_count errors in last hour)"
    return 0
}

# Check SSL certificate
check_ssl_certificate() {
    log "Checking SSL certificate..."
    
    local domain=$(echo "$APP_URL" | sed 's|https://||' | sed 's|/.*||')
    local expiry_date=$(echo | openssl s_client -servername "$domain" -connect "$domain:443" 2>/dev/null | openssl x509 -noout -dates | grep notAfter | cut -d= -f2)
    local expiry_timestamp=$(date -d "$expiry_date" +%s)
    local current_timestamp=$(date +%s)
    local days_until_expiry=$(( (expiry_timestamp - current_timestamp) / 86400 ))
    
    if [ "$days_until_expiry" -lt 30 ]; then
        warning "SSL certificate expires in $days_until_expiry days"
        send_alert "SSL Certificate Expiring" "SSL certificate expires in $days_until_expiry days" "warning"
        return 1
    fi
    
    log "SSL certificate check passed (expires in $days_until_expiry days)"
    return 0
}

# Check queue status
check_queue_status() {
    log "Checking queue status..."
    
    cd "$APP_DIR"
    
    local failed_jobs=$(php artisan queue:failed --format=json | jq length 2>/dev/null || echo "0")
    
    if [ "$failed_jobs" -gt 0 ]; then
        warning "Found $failed_jobs failed queue jobs"
        send_alert "Failed Queue Jobs" "Found $failed_jobs failed queue jobs" "warning"
        return 1
    fi
    
    log "Queue status check passed"
    return 0
}

# Check storage space
check_storage_space() {
    log "Checking storage space..."
    
    local storage_usage=$(du -sh "$APP_DIR/storage" | cut -f1)
    local storage_bytes=$(du -sb "$APP_DIR/storage" | cut -f1)
    local storage_gb=$((storage_bytes / 1024 / 1024 / 1024))
    
    if [ "$storage_gb" -gt 10 ]; then
        warning "High storage usage: ${storage_usage}"
        send_alert "High Storage Usage" "Storage directory is using ${storage_usage}" "warning"
        return 1
    fi
    
    log "Storage space check passed (${storage_usage})"
    return 0
}

# Performance metrics
collect_performance_metrics() {
    log "Collecting performance metrics..."
    
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local metrics_file="$LOG_DIR/metrics.log"
    
    # System metrics
    local cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | awk -F'%' '{print $1}')
    local memory_usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    local disk_usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    local load_average=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    
    # Application metrics
    local response_time=$(curl -s -o /dev/null -w "%{time_total}" "$APP_URL" | awk '{print int($1*1000)}')
    
    # Database metrics
    cd "$APP_DIR"
    local db_connections=$(php artisan tinker --execute="echo DB::select('SHOW STATUS LIKE \"Threads_connected\"')[0]->Value;" 2>/dev/null || echo "0")
    
    # Log metrics
    echo "$timestamp,CPU:$cpu_usage,Memory:$memory_usage,Disk:$disk_usage,Load:$load_average,Response:$response_time,DB_Conn:$db_connections" >> "$metrics_file"
    
    log "Performance metrics collected"
}

# Generate health report
generate_health_report() {
    local report_file="$LOG_DIR/health_report_$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "The Real World LMS Health Report"
        echo "Generated: $(date)"
        echo "================================"
        echo
        
        echo "System Information:"
        echo "- Hostname: $(hostname)"
        echo "- Uptime: $(uptime)"
        echo "- Load Average: $(uptime | awk -F'load average:' '{print $2}')"
        echo
        
        echo "Resource Usage:"
        echo "- CPU: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | awk -F'%' '{print $1}')%"
        echo "- Memory: $(free | awk 'NR==2{printf "%.0f", $3*100/$2}')%"
        echo "- Disk: $(df / | awk 'NR==2 {print $5}')"
        echo
        
        echo "Application Status:"
        echo "- URL: $APP_URL"
        echo "- Response Code: $(curl -s -o /dev/null -w "%{http_code}" "$APP_URL")"
        echo "- Response Time: $(curl -s -o /dev/null -w "%{time_total}" "$APP_URL" | awk '{print int($1*1000)}')ms"
        echo
        
        echo "Recent Errors (last 24 hours):"
        if [ -f "$APP_DIR/storage/logs/laravel.log" ]; then
            grep "$(date -d '24 hours ago' '+%Y-%m-%d')" "$APP_DIR/storage/logs/laravel.log" | grep "ERROR" | tail -10
        else
            echo "No error log found"
        fi
        
    } > "$report_file"
    
    log "Health report generated: $report_file"
}

# Main monitoring function
main() {
    log "Starting health monitoring..."
    
    local checks_passed=0
    local total_checks=10
    
    # Run all health checks
    check_application_health && ((checks_passed++))
    check_database && ((checks_passed++))
    check_redis && ((checks_passed++))
    check_disk_usage && ((checks_passed++))
    check_memory_usage && ((checks_passed++))
    check_cpu_usage && ((checks_passed++))
    check_error_logs && ((checks_passed++))
    check_ssl_certificate && ((checks_passed++))
    check_queue_status && ((checks_passed++))
    check_storage_space && ((checks_passed++))
    
    # Collect performance metrics
    collect_performance_metrics
    
    # Generate report if requested
    if [ "${1:-}" = "report" ]; then
        generate_health_report
    fi
    
    # Summary
    local health_percentage=$((checks_passed * 100 / total_checks))
    
    if [ "$health_percentage" -eq 100 ]; then
        log "All health checks passed (${checks_passed}/${total_checks})"
    elif [ "$health_percentage" -ge 80 ]; then
        warning "Most health checks passed (${checks_passed}/${total_checks}) - ${health_percentage}%"
    else
        error "Multiple health checks failed (${checks_passed}/${total_checks}) - ${health_percentage}%"
        send_alert "System Health Alert" "Only ${checks_passed}/${total_checks} health checks passed (${health_percentage}%)" "critical"
    fi
    
    log "Health monitoring completed"
}

# Handle script arguments
case "${1:-monitor}" in
    "monitor")
        main
        ;;
    "report")
        main report
        ;;
    "metrics")
        collect_performance_metrics
        ;;
    *)
        echo "Usage: $0 {monitor|report|metrics}"
        exit 1
        ;;
esac
