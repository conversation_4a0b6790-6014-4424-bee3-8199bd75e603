@extends('layouts.dashboard')

@section('content')
<div class="py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">Learning Statistics</h1>
            <p class="text-gray-600 mt-1">Track your progress and achievements</p>
        </div>

        <!-- Key Metrics -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="bg-blue-100 rounded-full p-3 mr-4">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="text-2xl font-bold text-gray-900">{{ $stats['total_courses_enrolled'] }}</p>
                        <p class="text-gray-600 text-sm">Courses Enrolled</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="bg-green-100 rounded-full p-3 mr-4">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="text-2xl font-bold text-gray-900">{{ $stats['total_lessons_completed'] }}</p>
                        <p class="text-gray-600 text-sm">Lessons Completed</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="bg-purple-100 rounded-full p-3 mr-4">
                        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="text-2xl font-bold text-gray-900">{{ $stats['total_watch_time_hours'] }}h</p>
                        <p class="text-gray-600 text-sm">Total Watch Time</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="bg-yellow-100 rounded-full p-3 mr-4">
                        <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="text-2xl font-bold text-gray-900">{{ $stats['current_streak'] }}</p>
                        <p class="text-gray-600 text-sm">Day Streak</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Progress Chart -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Learning Activity (Last 30 Days)</h3>
                <div class="h-64">
                    <canvas id="progressChart"></canvas>
                </div>
            </div>
            
            <!-- Category Breakdown -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Courses by Category</h3>
                <div class="space-y-4">
                    @foreach($categoryStats as $category)
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-4 h-4 rounded-full mr-3" style="background-color: {{ $this->getCategoryColor($category->category) }}"></div>
                            <span class="text-gray-700">{{ $category->category }}</span>
                        </div>
                        <div class="flex items-center">
                            <span class="text-gray-900 font-medium mr-2">{{ $category->count }}</span>
                            <div class="w-20 bg-gray-200 rounded-full h-2">
                                <div class="h-2 rounded-full" 
                                     style="width: {{ ($category->count / $categoryStats->sum('count')) * 100 }}%; background-color: {{ $this->getCategoryColor($category->category) }}"></div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>

        <!-- Detailed Stats -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- This Week -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">This Week</h3>
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-gray-600">Lessons Started</span>
                        <span class="font-medium">{{ $stats['this_week_progress'] }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Days Active</span>
                        <span class="font-medium">{{ min($stats['current_streak'], 7) }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Avg. Daily Progress</span>
                        <span class="font-medium">{{ number_format($stats['this_week_progress'] / 7, 1) }}</span>
                    </div>
                </div>
            </div>
            
            <!-- This Month -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">This Month</h3>
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-gray-600">Lessons Started</span>
                        <span class="font-medium">{{ $stats['this_month_progress'] }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Courses Completed</span>
                        <span class="font-medium">{{ $stats['total_courses_completed'] }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Learning Hours</span>
                        <span class="font-medium">{{ $stats['total_watch_time_hours'] }}h</span>
                    </div>
                </div>
            </div>
            
            <!-- Achievements -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Achievements</h3>
                <div class="space-y-3">
                    @if($stats['current_streak'] >= 7)
                        <div class="flex items-center">
                            <span class="text-2xl mr-2">🔥</span>
                            <span class="text-sm text-gray-700">{{ $stats['current_streak'] }}-day streak</span>
                        </div>
                    @endif
                    
                    @if($stats['total_courses_completed'] > 0)
                        <div class="flex items-center">
                            <span class="text-2xl mr-2">🎓</span>
                            <span class="text-sm text-gray-700">{{ $stats['total_courses_completed'] }} course{{ $stats['total_courses_completed'] > 1 ? 's' : '' }} completed</span>
                        </div>
                    @endif
                    
                    @if($stats['total_watch_time_hours'] >= 10)
                        <div class="flex items-center">
                            <span class="text-2xl mr-2">⏰</span>
                            <span class="text-sm text-gray-700">{{ $stats['total_watch_time_hours'] }}+ hours learned</span>
                        </div>
                    @endif
                    
                    @if($stats['total_lessons_completed'] >= 50)
                        <div class="flex items-center">
                            <span class="text-2xl mr-2">📚</span>
                            <span class="text-sm text-gray-700">{{ $stats['total_lessons_completed'] }}+ lessons mastered</span>
                        </div>
                    @endif
                    
                    @if($stats['total_courses_enrolled'] >= 5)
                        <div class="flex items-center">
                            <span class="text-2xl mr-2">🚀</span>
                            <span class="text-sm text-gray-700">Multi-skill learner</span>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Progress Chart
const ctx = document.getElementById('progressChart').getContext('2d');
const progressChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: {!! json_encode(array_column($progressOverTime, 'date')) !!},
        datasets: [{
            label: 'Lessons Started',
            data: {!! json_encode(array_column($progressOverTime, 'count')) !!},
            borderColor: 'rgb(59, 130, 246)',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            tension: 0.4,
            fill: true
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        },
        plugins: {
            legend: {
                display: false
            }
        }
    }
});
</script>

@php
function getCategoryColor($category) {
    $colors = [
        'Crypto' => '#f59e0b',
        'Copywriting' => '#10b981',
        'E-commerce' => '#3b82f6',
        'Social Media' => '#8b5cf6',
        'Freelancing' => '#ef4444',
        'Real Estate' => '#06b6d4',
    ];
    return $colors[$category] ?? '#6b7280';
}
@endphp
@endsection
