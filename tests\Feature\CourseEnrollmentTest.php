<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Course;
use App\Models\Category;
use App\Models\SubscriptionPlan;
use App\Models\UserSubscription;
use App\Services\CourseService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class CourseEnrollmentTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $courseService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->courseService = app(CourseService::class);
    }

    public function test_user_can_enroll_in_free_course()
    {
        $user = User::factory()->create();
        $category = Category::factory()->create();
        $course = Course::factory()->create([
            'category_id' => $category->id,
            'is_free' => true,
            'is_published' => true,
        ]);

        $result = $this->courseService->enrollUser($user, $course);

        $this->assertTrue($result);
        $this->assertTrue($this->courseService->isUserEnrolled($user, $course));
    }

    public function test_user_cannot_enroll_in_paid_course_without_subscription()
    {
        $user = User::factory()->create();
        $category = Category::factory()->create();
        $course = Course::factory()->create([
            'category_id' => $category->id,
            'is_free' => false,
            'is_published' => true,
        ]);

        $result = $this->courseService->enrollUser($user, $course);

        $this->assertFalse($result);
        $this->assertFalse($this->courseService->isUserEnrolled($user, $course));
    }

    public function test_user_can_enroll_in_paid_course_with_active_subscription()
    {
        $user = User::factory()->create();
        $category = Category::factory()->create();
        $plan = SubscriptionPlan::factory()->create();

        // Create active subscription
        UserSubscription::factory()->create([
            'user_id' => $user->id,
            'subscription_plan_id' => $plan->id,
            'status' => 'active',
            'current_period_end' => now()->addMonth(),
        ]);

        $course = Course::factory()->create([
            'category_id' => $category->id,
            'is_free' => false,
            'is_published' => true,
        ]);

        $result = $this->courseService->enrollUser($user, $course);

        $this->assertTrue($result);
        $this->assertTrue($this->courseService->isUserEnrolled($user, $course));
    }

    public function test_user_cannot_enroll_twice_in_same_course()
    {
        $user = User::factory()->create();
        $category = Category::factory()->create();
        $course = Course::factory()->create([
            'category_id' => $category->id,
            'is_free' => true,
            'is_published' => true,
        ]);

        // First enrollment
        $firstResult = $this->courseService->enrollUser($user, $course);
        $this->assertTrue($firstResult);

        // Second enrollment attempt
        $secondResult = $this->courseService->enrollUser($user, $course);
        $this->assertFalse($secondResult);
    }

    public function test_enrollment_creates_progress_records_for_all_lessons()
    {
        $user = User::factory()->create();
        $category = Category::factory()->create();
        $course = Course::factory()->create([
            'category_id' => $category->id,
            'is_free' => true,
            'is_published' => true,
        ]);

        // Create lessons for the course
        $lessons = \App\Models\Lesson::factory()->count(3)->create([
            'course_id' => $course->id,
            'is_published' => true,
        ]);

        $this->courseService->enrollUser($user, $course);

        // Check that progress records were created for all lessons
        foreach ($lessons as $lesson) {
            $this->assertDatabaseHas('user_progress', [
                'user_id' => $user->id,
                'course_id' => $course->id,
                'lesson_id' => $lesson->id,
                'is_completed' => false,
                'completion_percentage' => 0,
            ]);
        }
    }

    public function test_get_user_course_progress_returns_correct_data()
    {
        $user = User::factory()->create();
        $category = Category::factory()->create();
        $course = Course::factory()->create([
            'category_id' => $category->id,
            'is_free' => true,
            'is_published' => true,
        ]);

        // Create lessons
        $lessons = \App\Models\Lesson::factory()->count(4)->create([
            'course_id' => $course->id,
            'is_published' => true,
        ]);

        $this->courseService->enrollUser($user, $course);

        // Complete 2 out of 4 lessons
        $user->userProgress()
            ->where('course_id', $course->id)
            ->limit(2)
            ->update(['is_completed' => true]);

        $progress = $this->courseService->getUserCourseProgress($user, $course);

        $this->assertEquals(4, $progress['total_lessons']);
        $this->assertEquals(2, $progress['completed_lessons']);
        $this->assertEquals(50, $progress['progress_percentage']);
        $this->assertFalse($progress['is_completed']);
    }
}
