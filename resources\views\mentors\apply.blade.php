@extends('layouts.app')

@section('title', '- Become a Mentor')

@section('content')
<div class="min-h-screen bg-gray-50 py-12">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-900 mb-4">Become a Mentor</h1>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Share your expertise and help students achieve their goals. Join our community of successful mentors.
            </p>
        </div>

        <!-- Application Form -->
        <div class="bg-white shadow-xl rounded-lg overflow-hidden">
            <div class="px-6 py-8">
                <form action="{{ route('mentors.apply.submit') }}" method="POST" enctype="multipart/form-data" class="space-y-8">
                    @csrf

                    <!-- Personal Information -->
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Personal Information</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Full Name *</label>
                                <input type="text" id="name" name="name" value="{{ old('name') }}" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                @error('name')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email Address *</label>
                                <input type="email" id="email" name="email" value="{{ old('email') }}" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                @error('email')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">Phone Number *</label>
                                <input type="tel" id="phone" name="phone" value="{{ old('phone') }}" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                @error('phone')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="title" class="block text-sm font-medium text-gray-700 mb-2">Professional Title *</label>
                                <input type="text" id="title" name="title" value="{{ old('title') }}" required
                                    placeholder="e.g., Entrepreneur, Business Coach, Marketing Expert"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                @error('title')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Professional Background -->
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Professional Background</h3>
                        
                        <div class="space-y-6">
                            <div>
                                <label for="bio" class="block text-sm font-medium text-gray-700 mb-2">Bio (minimum 100 characters) *</label>
                                <textarea id="bio" name="bio" rows="4" required
                                    placeholder="Tell us about yourself, your background, and what makes you unique..."
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">{{ old('bio') }}</textarea>
                                @error('bio')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="experience" class="block text-sm font-medium text-gray-700 mb-2">Professional Experience (minimum 200 characters) *</label>
                                <textarea id="experience" name="experience" rows="5" required
                                    placeholder="Describe your professional experience, achievements, and relevant background..."
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">{{ old('experience') }}</textarea>
                                @error('experience')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Areas of Expertise *</label>
                                <div class="grid grid-cols-2 md:grid-cols-3 gap-3">
                                    @php
                                        $expertiseAreas = [
                                            'Business Development', 'Marketing', 'Sales', 'E-commerce',
                                            'Cryptocurrency', 'Real Estate', 'Fitness', 'Mindset',
                                            'Copywriting', 'Social Media', 'Investing', 'Trading'
                                        ];
                                    @endphp
                                    @foreach($expertiseAreas as $area)
                                        <label class="flex items-center">
                                            <input type="checkbox" name="expertise[]" value="{{ $area }}" 
                                                {{ in_array($area, old('expertise', [])) ? 'checked' : '' }}
                                                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                            <span class="ml-2 text-sm text-gray-700">{{ $area }}</span>
                                        </label>
                                    @endforeach
                                </div>
                                @error('expertise')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="achievements" class="block text-sm font-medium text-gray-700 mb-2">Key Achievements (minimum 100 characters) *</label>
                                <textarea id="achievements" name="achievements" rows="4" required
                                    placeholder="List your key achievements, awards, certifications, or notable accomplishments..."
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">{{ old('achievements') }}</textarea>
                                @error('achievements')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Social Links & Portfolio -->
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Online Presence</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="portfolio_url" class="block text-sm font-medium text-gray-700 mb-2">Portfolio/Website URL</label>
                                <input type="url" id="portfolio_url" name="portfolio_url" value="{{ old('portfolio_url') }}"
                                    placeholder="https://yourwebsite.com"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                @error('portfolio_url')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="video_introduction" class="block text-sm font-medium text-gray-700 mb-2">Video Introduction URL</label>
                                <input type="url" id="video_introduction" name="video_introduction" value="{{ old('video_introduction') }}"
                                    placeholder="https://youtube.com/watch?v=..."
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                @error('video_introduction')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="social_links_twitter" class="block text-sm font-medium text-gray-700 mb-2">Twitter URL</label>
                                <input type="url" id="social_links_twitter" name="social_links[twitter]" value="{{ old('social_links.twitter') }}"
                                    placeholder="https://twitter.com/username"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>

                            <div>
                                <label for="social_links_linkedin" class="block text-sm font-medium text-gray-700 mb-2">LinkedIn URL</label>
                                <input type="url" id="social_links_linkedin" name="social_links[linkedin]" value="{{ old('social_links.linkedin') }}"
                                    placeholder="https://linkedin.com/in/username"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>

                            <div>
                                <label for="social_links_youtube" class="block text-sm font-medium text-gray-700 mb-2">YouTube URL</label>
                                <input type="url" id="social_links_youtube" name="social_links[youtube]" value="{{ old('social_links.youtube') }}"
                                    placeholder="https://youtube.com/@username"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>

                            <div>
                                <label for="social_links_instagram" class="block text-sm font-medium text-gray-700 mb-2">Instagram URL</label>
                                <input type="url" id="social_links_instagram" name="social_links[instagram]" value="{{ old('social_links.instagram') }}"
                                    placeholder="https://instagram.com/username"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                        </div>
                    </div>

                    <!-- File Upload -->
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Additional Documents</h3>
                        <div>
                            <label for="resume" class="block text-sm font-medium text-gray-700 mb-2">Resume/CV (PDF, DOC, DOCX - Max 2MB)</label>
                            <input type="file" id="resume" name="resume" accept=".pdf,.doc,.docx"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            @error('resume')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="flex justify-end pt-6 border-t border-gray-200">
                        <button type="submit" 
                            class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-8 rounded-lg transition duration-300 transform hover:scale-105">
                            Submit Application
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection
