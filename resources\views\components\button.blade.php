@props([
    'variant' => 'primary',
    'size' => 'md',
    'type' => 'button',
    'href' => null,
    'loading' => false,
    'disabled' => false,
    'icon' => null,
    'iconPosition' => 'left'
])

@php
$baseClasses = 'inline-flex items-center justify-center font-medium rounded-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';

$variantClasses = [
    'primary' => 'text-white shadow-md hover:shadow-lg transform hover:-translate-y-0.5 bg-primary-gradient focus:ring-blue-500',
    'secondary' => 'bg-gray-100 text-gray-900 hover:bg-gray-200 shadow-sm hover:shadow-md focus:ring-gray-500',
    'success' => 'text-white shadow-md hover:shadow-lg transform hover:-translate-y-0.5 bg-success-gradient focus:ring-green-500',
    'danger' => 'text-white shadow-md hover:shadow-lg transform hover:-translate-y-0.5 bg-danger-gradient focus:ring-red-500',
    'warning' => 'text-white shadow-md hover:shadow-lg transform hover:-translate-y-0.5 bg-warning-gradient focus:ring-yellow-500',
    'outline' => 'border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white focus:ring-blue-500',
    'ghost' => 'text-gray-600 hover:bg-gray-100 hover:text-gray-900 focus:ring-gray-500',
];

$sizeClasses = [
    'xs' => 'px-2.5 py-1.5 text-xs',
    'sm' => 'px-3 py-2 text-sm',
    'md' => 'px-4 py-2 text-sm',
    'lg' => 'px-6 py-3 text-base',
    'xl' => 'px-8 py-4 text-lg',
];

$classes = $baseClasses . ' ' . ($variantClasses[$variant] ?? $variantClasses['primary']) . ' ' . ($sizeClasses[$size] ?? $sizeClasses['md']);

$tag = $href ? 'a' : 'button';
@endphp

@if($href)
    <a href="{{ $href }}"
       @if($disabled || $loading) disabled @endif
       {{ $attributes->merge(['class' => $classes]) }}>
@else
    <button type="{{ $type }}"
            @if($disabled || $loading) disabled @endif
            {{ $attributes->merge(['class' => $classes]) }}>
@endif
    @if($loading)
        <div class="loading-spinner mr-2"></div>
    @elseif($icon && $iconPosition === 'left')
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            {!! $icon !!}
        </svg>
    @endif
    
    {{ $slot }}
    
    @if($icon && $iconPosition === 'right')
        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            {!! $icon !!}
        </svg>
    @endif

@if($href)
    </a>
@else
    </button>
@endif

<style>
.bg-primary-gradient {
    background: linear-gradient(135deg, #2563eb 0%, #7c3aed 100%);
}

.bg-primary-gradient:hover {
    filter: brightness(1.1);
}

.bg-success-gradient {
    background: linear-gradient(135deg, #059669 0%, #10b981 100%);
}

.bg-success-gradient:hover {
    filter: brightness(1.1);
}

.bg-danger-gradient {
    background: linear-gradient(135deg, #dc2626 0%, #ea580c 100%);
}

.bg-danger-gradient:hover {
    filter: brightness(1.1);
}

.bg-warning-gradient {
    background: linear-gradient(135deg, #d97706 0%, #eab308 100%);
}

.bg-warning-gradient:hover {
    filter: brightness(1.1);
}
</style>
