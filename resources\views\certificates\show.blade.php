@extends('layouts.app')

@section('title', 'Certificate - ' . $certificate->course->title)

@section('content')
<div class="certificate-container">
    <div class="container">
        <!-- Certificate Actions -->
        <div class="certificate-actions">
            <div class="actions-left">
                <a href="{{ route('dashboard.certificates') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Certificates
                </a>
            </div>
            <div class="actions-right">
                <button class="btn btn-primary" onclick="downloadCertificate()">
                    <i class="fas fa-download"></i> Download PDF
                </button>
                <button class="btn btn-success" onclick="shareCertificate()">
                    <i class="fas fa-share-alt"></i> Share
                </button>
                <button class="btn btn-info" onclick="printCertificate()">
                    <i class="fas fa-print"></i> Print
                </button>
            </div>
        </div>

        <!-- Certificate Display -->
        <div class="certificate-display" id="certificateDisplay">
            <div class="certificate-paper">
                <!-- Certificate Header -->
                <div class="certificate-header">
                    <div class="certificate-logo">
                        <img src="{{ asset('images/logo-certificate.png') }}" alt="The Real World" class="logo-image">
                    </div>
                    <div class="certificate-title">
                        <h1>Certificate of Completion</h1>
                        <div class="certificate-subtitle">The Real World Academy</div>
                    </div>
                </div>

                <!-- Certificate Body -->
                <div class="certificate-body">
                    <div class="certificate-text">
                        <div class="awarded-text">This is to certify that</div>
                        
                        <div class="recipient-name">{{ $certificate->user->name }}</div>
                        
                        <div class="completion-text">has successfully completed the course</div>
                        
                        <div class="course-title">{{ $certificate->course->title }}</div>
                        
                        <div class="course-details">
                            <div class="course-info">
                                <span class="info-label">Duration:</span>
                                <span class="info-value">{{ $certificate->course->total_duration }} hours</span>
                            </div>
                            <div class="course-info">
                                <span class="info-label">Completion Date:</span>
                                <span class="info-value">{{ $certificate->issued_at->format('F d, Y') }}</span>
                            </div>
                            <div class="course-info">
                                <span class="info-label">Grade:</span>
                                <span class="info-value">{{ $certificate->grade ?? 'Pass' }}</span>
                            </div>
                        </div>
                        
                        <div class="achievement-text">
                            with dedication and commitment to excellence in learning
                        </div>
                    </div>

                    <!-- Certificate Signatures -->
                    <div class="certificate-signatures">
                        <div class="signature-section">
                            <div class="signature-line">
                                <img src="{{ $certificate->course->mentor->signature ? asset('storage/' . $certificate->course->mentor->signature) : asset('images/default-signature.png') }}" 
                                     alt="Mentor Signature" class="signature-image">
                            </div>
                            <div class="signature-info">
                                <div class="signature-name">{{ $certificate->course->mentor->name }}</div>
                                <div class="signature-title">Course Mentor</div>
                            </div>
                        </div>
                        
                        <div class="signature-section">
                            <div class="signature-line">
                                <img src="{{ asset('images/ceo-signature.png') }}" alt="CEO Signature" class="signature-image">
                            </div>
                            <div class="signature-info">
                                <div class="signature-name">Andrew Tate</div>
                                <div class="signature-title">Founder & CEO</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Certificate Footer -->
                <div class="certificate-footer">
                    <div class="certificate-id">
                        <div class="id-label">Certificate ID:</div>
                        <div class="id-value">{{ $certificate->certificate_id }}</div>
                    </div>
                    
                    <div class="verification-info">
                        <div class="verification-text">
                            Verify this certificate at: 
                            <span class="verification-url">{{ route('certificates.verify', $certificate->certificate_id) }}</span>
                        </div>
                        <div class="qr-code">
                            <img src="{{ $certificate->qr_code_url }}" alt="QR Code" class="qr-image">
                        </div>
                    </div>
                </div>

                <!-- Decorative Elements -->
                <div class="certificate-border">
                    <div class="border-corner top-left"></div>
                    <div class="border-corner top-right"></div>
                    <div class="border-corner bottom-left"></div>
                    <div class="border-corner bottom-right"></div>
                </div>
                
                <div class="certificate-seal">
                    <div class="seal-outer">
                        <div class="seal-inner">
                            <div class="seal-text">THE REAL WORLD</div>
                            <div class="seal-year">{{ $certificate->issued_at->format('Y') }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Certificate Details -->
        <div class="certificate-details">
            <div class="row">
                <div class="col-md-6">
                    <div class="details-card">
                        <h3>Course Information</h3>
                        <div class="detail-item">
                            <span class="detail-label">Course Title:</span>
                            <span class="detail-value">{{ $certificate->course->title }}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Category:</span>
                            <span class="detail-value">{{ $certificate->course->category->name }}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Difficulty:</span>
                            <span class="detail-value">{{ ucfirst($certificate->course->difficulty_level) }}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Total Duration:</span>
                            <span class="detail-value">{{ $certificate->course->total_duration }} hours</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Lessons Completed:</span>
                            <span class="detail-value">{{ $certificate->course->lessons_count }} lessons</span>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="details-card">
                        <h3>Achievement Details</h3>
                        <div class="detail-item">
                            <span class="detail-label">Completion Date:</span>
                            <span class="detail-value">{{ $certificate->issued_at->format('F d, Y') }}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Time Taken:</span>
                            <span class="detail-value">{{ $certificate->completion_time ?? 'N/A' }}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Final Grade:</span>
                            <span class="detail-value">{{ $certificate->grade ?? 'Pass' }}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Certificate Status:</span>
                            <span class="detail-value">
                                <span class="badge badge-success">Verified</span>
                            </span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Verification URL:</span>
                            <span class="detail-value">
                                <a href="{{ route('certificates.verify', $certificate->certificate_id) }}" target="_blank">
                                    Verify Certificate
                                </a>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sharing Options -->
        <div class="sharing-section">
            <h3>Share Your Achievement</h3>
            <p>Showcase your accomplishment on social media and professional networks</p>
            
            <div class="sharing-buttons">
                <button class="share-btn linkedin" onclick="shareOnLinkedIn()">
                    <i class="fab fa-linkedin"></i>
                    Share on LinkedIn
                </button>
                <button class="share-btn twitter" onclick="shareOnTwitter()">
                    <i class="fab fa-twitter"></i>
                    Share on Twitter
                </button>
                <button class="share-btn facebook" onclick="shareOnFacebook()">
                    <i class="fab fa-facebook"></i>
                    Share on Facebook
                </button>
                <button class="share-btn copy" onclick="copyShareLink()">
                    <i class="fas fa-link"></i>
                    Copy Link
                </button>
            </div>
        </div>

        <!-- Related Certificates -->
        @if($relatedCertificates->count() > 0)
        <div class="related-certificates">
            <h3>Your Other Certificates</h3>
            <div class="certificates-grid">
                @foreach($relatedCertificates as $relatedCert)
                <div class="certificate-card">
                    <div class="cert-thumbnail">
                        <img src="{{ $relatedCert->course->thumbnail ? asset('storage/' . $relatedCert->course->thumbnail) : asset('images/default-course-thumbnail.jpg') }}" 
                             alt="{{ $relatedCert->course->title }}">
                        <div class="cert-overlay">
                            <i class="fas fa-certificate"></i>
                        </div>
                    </div>
                    <div class="cert-info">
                        <h4>{{ $relatedCert->course->title }}</h4>
                        <p>Completed {{ $relatedCert->issued_at->format('M d, Y') }}</p>
                        <a href="{{ route('certificates.show', $relatedCert) }}" class="btn btn-sm btn-outline-primary">
                            View Certificate
                        </a>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
        @endif
    </div>
</div>
@endsection

@push('styles')
<style>
.certificate-container {
    background: #f8f9fc;
    min-height: 100vh;
    padding: 2rem 0;
}

.certificate-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    background: white;
    padding: 1rem 2rem;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.actions-right {
    display: flex;
    gap: 0.5rem;
}

.certificate-display {
    margin-bottom: 3rem;
    display: flex;
    justify-content: center;
}

.certificate-paper {
    width: 800px;
    height: 600px;
    background: white;
    position: relative;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    border-radius: 10px;
    overflow: hidden;
    background-image: 
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
}

.certificate-header {
    text-align: center;
    padding: 2rem 2rem 1rem;
    border-bottom: 2px solid #667eea;
}

.certificate-logo {
    margin-bottom: 1rem;
}

.logo-image {
    height: 60px;
}

.certificate-title h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.5rem;
    font-family: 'Georgia', serif;
}

.certificate-subtitle {
    font-size: 1.25rem;
    color: #667eea;
    font-weight: 600;
}

.certificate-body {
    padding: 2rem;
    text-align: center;
    flex: 1;
}

.awarded-text {
    font-size: 1.1rem;
    color: #4a5568;
    margin-bottom: 1rem;
    font-style: italic;
}

.recipient-name {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 1rem;
    font-family: 'Georgia', serif;
    border-bottom: 2px solid #667eea;
    display: inline-block;
    padding-bottom: 0.5rem;
}

.completion-text {
    font-size: 1.1rem;
    color: #4a5568;
    margin-bottom: 1rem;
    font-style: italic;
}

.course-title {
    font-size: 1.8rem;
    font-weight: 600;
    color: #667eea;
    margin-bottom: 1.5rem;
    font-family: 'Georgia', serif;
}

.course-details {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.course-info {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.info-label {
    font-size: 0.875rem;
    color: #718096;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.info-value {
    font-size: 1rem;
    color: #2d3748;
    font-weight: 600;
}

.achievement-text {
    font-size: 1rem;
    color: #4a5568;
    font-style: italic;
    margin-bottom: 2rem;
}

.certificate-signatures {
    display: flex;
    justify-content: space-around;
    margin-top: 2rem;
}

.signature-section {
    text-align: center;
}

.signature-line {
    width: 200px;
    height: 60px;
    border-bottom: 2px solid #2d3748;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    padding-bottom: 0.5rem;
}

.signature-image {
    max-height: 40px;
    max-width: 150px;
}

.signature-name {
    font-weight: 600;
    color: #2d3748;
    font-size: 1rem;
}

.signature-title {
    color: #718096;
    font-size: 0.875rem;
}

.certificate-footer {
    background: #f7fafc;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid #e2e8f0;
}

.certificate-id {
    text-align: left;
}

.id-label {
    font-size: 0.75rem;
    color: #718096;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.id-value {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: #2d3748;
}

.verification-info {
    text-align: right;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.verification-text {
    font-size: 0.75rem;
    color: #718096;
}

.verification-url {
    font-family: 'Courier New', monospace;
    color: #667eea;
}

.qr-image {
    width: 40px;
    height: 40px;
}

.certificate-border {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.border-corner {
    position: absolute;
    width: 50px;
    height: 50px;
    border: 3px solid #667eea;
}

.border-corner.top-left {
    top: 10px;
    left: 10px;
    border-right: none;
    border-bottom: none;
}

.border-corner.top-right {
    top: 10px;
    right: 10px;
    border-left: none;
    border-bottom: none;
}

.border-corner.bottom-left {
    bottom: 10px;
    left: 10px;
    border-right: none;
    border-top: none;
}

.border-corner.bottom-right {
    bottom: 10px;
    right: 10px;
    border-left: none;
    border-top: none;
}

.certificate-seal {
    position: absolute;
    bottom: 20px;
    right: 20px;
    width: 80px;
    height: 80px;
}

.seal-outer {
    width: 100%;
    height: 100%;
    border: 3px solid #667eea;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
}

.seal-inner {
    text-align: center;
}

.seal-text {
    font-size: 0.6rem;
    font-weight: 700;
    color: #667eea;
    line-height: 1;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.seal-year {
    font-size: 0.8rem;
    font-weight: 700;
    color: #2d3748;
}

.certificate-details {
    margin-bottom: 3rem;
}

.details-card {
    background: white;
    border-radius: 10px;
    padding: 2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    height: 100%;
}

.details-card h3 {
    color: #2d3748;
    margin-bottom: 1.5rem;
    font-size: 1.25rem;
    font-weight: 600;
    border-bottom: 2px solid #667eea;
    padding-bottom: 0.5rem;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #e2e8f0;
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-label {
    font-weight: 600;
    color: #4a5568;
}

.detail-value {
    color: #2d3748;
    text-align: right;
}

.sharing-section {
    background: white;
    border-radius: 10px;
    padding: 2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    text-align: center;
    margin-bottom: 3rem;
}

.sharing-section h3 {
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.sharing-section p {
    color: #718096;
    margin-bottom: 2rem;
}

.sharing-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.share-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.share-btn.linkedin {
    background: #0077b5;
    color: white;
}

.share-btn.twitter {
    background: #1da1f2;
    color: white;
}

.share-btn.facebook {
    background: #1877f2;
    color: white;
}

.share-btn.copy {
    background: #718096;
    color: white;
}

.share-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.related-certificates {
    background: white;
    border-radius: 10px;
    padding: 2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.related-certificates h3 {
    color: #2d3748;
    margin-bottom: 2rem;
    text-align: center;
}

.certificates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
}

.certificate-card {
    border: 1px solid #e2e8f0;
    border-radius: 10px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.certificate-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.cert-thumbnail {
    position: relative;
    height: 150px;
    overflow: hidden;
}

.cert-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.cert-overlay {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 0.5rem;
    border-radius: 50%;
    font-size: 1.25rem;
}

.cert-info {
    padding: 1rem;
}

.cert-info h4 {
    color: #2d3748;
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

.cert-info p {
    color: #718096;
    font-size: 0.875rem;
    margin-bottom: 1rem;
}

@media (max-width: 768px) {
    .certificate-paper {
        width: 100%;
        height: auto;
        min-height: 500px;
    }
    
    .certificate-actions {
        flex-direction: column;
        gap: 1rem;
    }
    
    .actions-right {
        width: 100%;
        justify-content: center;
    }
    
    .course-details {
        flex-direction: column;
        gap: 1rem;
    }
    
    .certificate-signatures {
        flex-direction: column;
        gap: 2rem;
    }
    
    .certificate-footer {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .verification-info {
        justify-content: center;
    }
    
    .sharing-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .share-btn {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }
}

@media print {
    .certificate-actions,
    .certificate-details,
    .sharing-section,
    .related-certificates {
        display: none;
    }
    
    .certificate-container {
        background: white;
        padding: 0;
    }
    
    .certificate-paper {
        box-shadow: none;
        border: 1px solid #000;
    }
}
</style>
@endpush

@push('scripts')
<script>
function downloadCertificate() {
    window.open('{{ route("certificates.download", $certificate) }}', '_blank');
}

function printCertificate() {
    window.print();
}

function shareCertificate() {
    if (navigator.share) {
        navigator.share({
            title: 'My Certificate - {{ $certificate->course->title }}',
            text: 'I just completed {{ $certificate->course->title }} and earned my certificate!',
            url: '{{ route("certificates.show", $certificate) }}'
        });
    } else {
        copyShareLink();
    }
}

function shareOnLinkedIn() {
    const url = encodeURIComponent('{{ route("certificates.show", $certificate) }}');
    const title = encodeURIComponent('I just completed {{ $certificate->course->title }} and earned my certificate from The Real World!');
    window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${url}&title=${title}`, '_blank');
}

function shareOnTwitter() {
    const url = encodeURIComponent('{{ route("certificates.show", $certificate) }}');
    const text = encodeURIComponent('I just completed {{ $certificate->course->title }} and earned my certificate from @TheRealWorld! 🎓');
    window.open(`https://twitter.com/intent/tweet?url=${url}&text=${text}`, '_blank');
}

function shareOnFacebook() {
    const url = encodeURIComponent('{{ route("certificates.show", $certificate) }}');
    window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}`, '_blank');
}

function copyShareLink() {
    const url = '{{ route("certificates.show", $certificate) }}';
    navigator.clipboard.writeText(url).then(() => {
        alert('Certificate link copied to clipboard!');
    });
}
</script>
@endpush
