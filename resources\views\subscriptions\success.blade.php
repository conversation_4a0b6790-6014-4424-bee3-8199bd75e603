@extends('layouts.app')

@section('title', 'Subscription Successful')

@section('content')
<div class="success-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="success-card">
                    <div class="success-animation">
                        <div class="checkmark-circle">
                            <div class="checkmark"></div>
                        </div>
                    </div>
                    
                    <div class="success-content">
                        <h1 class="success-title">Welcome to The Real World!</h1>
                        <p class="success-subtitle">Your subscription has been activated successfully. You now have access to all premium content.</p>
                        
                        <div class="subscription-details">
                            <div class="detail-card">
                                <div class="detail-icon">
                                    <i class="fas fa-crown"></i>
                                </div>
                                <div class="detail-content">
                                    <h4>{{ $subscription->subscriptionPlan->name }}</h4>
                                    <p>Your premium membership is now active</p>
                                </div>
                            </div>
                            
                            <div class="detail-card">
                                <div class="detail-icon">
                                    <i class="fas fa-calendar"></i>
                                </div>
                                <div class="detail-content">
                                    <h4>Next Billing</h4>
                                    <p>{{ $subscription->current_period_end->format('F d, Y') }}</p>
                                </div>
                            </div>
                            
                            <div class="detail-card">
                                <div class="detail-icon">
                                    <i class="fas fa-dollar-sign"></i>
                                </div>
                                <div class="detail-content">
                                    <h4>${{ number_format($subscription->amount, 2) }}</h4>
                                    <p>Per {{ $subscription->billing_cycle }}</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="benefits-section">
                            <h3>What's included in your subscription:</h3>
                            <div class="benefits-grid">
                                <div class="benefit-item">
                                    <i class="fas fa-book text-primary"></i>
                                    <span>Access to all courses</span>
                                </div>
                                <div class="benefit-item">
                                    <i class="fas fa-video text-primary"></i>
                                    <span>Live calls with mentors</span>
                                </div>
                                <div class="benefit-item">
                                    <i class="fas fa-users text-primary"></i>
                                    <span>Private community access</span>
                                </div>
                                <div class="benefit-item">
                                    <i class="fas fa-certificate text-primary"></i>
                                    <span>Completion certificates</span>
                                </div>
                                <div class="benefit-item">
                                    <i class="fas fa-mobile-alt text-primary"></i>
                                    <span>Mobile app access</span>
                                </div>
                                <div class="benefit-item">
                                    <i class="fas fa-headset text-primary"></i>
                                    <span>Priority support</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="action-buttons">
                            <a href="{{ route('courses.index') }}" class="btn btn-primary btn-lg">
                                <i class="fas fa-play"></i> Start Learning
                            </a>
                            <a href="{{ route('dashboard') }}" class="btn btn-outline-primary btn-lg">
                                <i class="fas fa-tachometer-alt"></i> Go to Dashboard
                            </a>
                        </div>
                        
                        <div class="next-steps">
                            <h4>What's next?</h4>
                            <div class="steps-list">
                                <div class="step-item">
                                    <div class="step-number">1</div>
                                    <div class="step-content">
                                        <h5>Complete your profile</h5>
                                        <p>Add your information to get personalized course recommendations</p>
                                        <a href="{{ route('profile.edit') }}" class="step-link">Complete Profile →</a>
                                    </div>
                                </div>
                                
                                <div class="step-item">
                                    <div class="step-number">2</div>
                                    <div class="step-content">
                                        <h5>Browse courses</h5>
                                        <p>Explore our extensive library of courses and start your learning journey</p>
                                        <a href="{{ route('courses.index') }}" class="step-link">Browse Courses →</a>
                                    </div>
                                </div>
                                
                                <div class="step-item">
                                    <div class="step-number">3</div>
                                    <div class="step-content">
                                        <h5>Join the community</h5>
                                        <p>Connect with other students and mentors in our private community</p>
                                        <a href="{{ route('community.index') }}" class="step-link">Join Community →</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Support Section -->
                <div class="support-section">
                    <div class="support-card">
                        <div class="support-icon">
                            <i class="fas fa-question-circle"></i>
                        </div>
                        <div class="support-content">
                            <h4>Need Help Getting Started?</h4>
                            <p>Our support team is here to help you make the most of your subscription.</p>
                            <div class="support-buttons">
                                <a href="{{ route('contact') }}" class="btn btn-outline-primary">
                                    <i class="fas fa-envelope"></i> Contact Support
                                </a>
                                <a href="{{ route('faq') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-book"></i> View FAQ
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.success-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 2rem 0;
    display: flex;
    align-items: center;
}

.success-card {
    background: white;
    border-radius: 20px;
    padding: 3rem;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.success-animation {
    margin-bottom: 2rem;
}

.checkmark-circle {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: linear-gradient(135deg, #1cc88a 0%, #17a673 100%);
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: scaleIn 0.5s ease-out;
}

.checkmark {
    width: 40px;
    height: 20px;
    border: 4px solid white;
    border-top: none;
    border-right: none;
    transform: rotate(-45deg);
    animation: checkmarkDraw 0.5s ease-out 0.3s both;
}

@keyframes scaleIn {
    0% {
        transform: scale(0);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes checkmarkDraw {
    0% {
        width: 0;
        height: 0;
    }
    100% {
        width: 40px;
        height: 20px;
    }
}

.success-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 1rem;
}

.success-subtitle {
    font-size: 1.25rem;
    color: #718096;
    margin-bottom: 3rem;
    line-height: 1.6;
}

.subscription-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.detail-card {
    background: #f8f9fc;
    border-radius: 15px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    text-align: left;
}

.detail-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    flex-shrink: 0;
}

.detail-icon i {
    font-size: 1.5rem;
    color: white;
}

.detail-content h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.25rem;
}

.detail-content p {
    color: #718096;
    margin: 0;
    font-size: 0.875rem;
}

.benefits-section {
    margin-bottom: 3rem;
}

.benefits-section h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 1.5rem;
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.benefit-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    background: #f8f9fc;
    border-radius: 10px;
    text-align: left;
}

.benefit-item i {
    font-size: 1.25rem;
    margin-right: 0.75rem;
}

.benefit-item span {
    font-weight: 500;
    color: #2d3748;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.next-steps {
    text-align: left;
    background: #f8f9fc;
    border-radius: 15px;
    padding: 2rem;
}

.next-steps h4 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 1.5rem;
    text-align: center;
}

.steps-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.step-item {
    display: flex;
    align-items: flex-start;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    margin-right: 1rem;
    flex-shrink: 0;
}

.step-content h5 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.step-content p {
    color: #718096;
    margin-bottom: 0.5rem;
    line-height: 1.5;
}

.step-link {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.875rem;
}

.step-link:hover {
    color: #764ba2;
    text-decoration: none;
}

.support-section {
    margin-top: 2rem;
}

.support-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    text-align: left;
}

.support-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #f6c23e 0%, #f4b942 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1.5rem;
    flex-shrink: 0;
}

.support-icon i {
    font-size: 2rem;
    color: white;
}

.support-content h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.support-content p {
    color: #718096;
    margin-bottom: 1rem;
    line-height: 1.5;
}

.support-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

@media (max-width: 768px) {
    .success-card {
        padding: 2rem;
    }
    
    .success-title {
        font-size: 2rem;
    }
    
    .subscription-details {
        grid-template-columns: 1fr;
    }
    
    .detail-card {
        flex-direction: column;
        text-align: center;
    }
    
    .detail-icon {
        margin-right: 0;
        margin-bottom: 1rem;
    }
    
    .benefits-grid {
        grid-template-columns: 1fr;
    }
    
    .action-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .action-buttons .btn {
        width: 100%;
        max-width: 300px;
    }
    
    .support-card {
        flex-direction: column;
        text-align: center;
    }
    
    .support-icon {
        margin-right: 0;
        margin-bottom: 1rem;
    }
    
    .support-buttons {
        justify-content: center;
    }
}
</style>
@endpush

@push('scripts')
<script>
// Auto-redirect to dashboard after 30 seconds if user doesn't interact
let redirectTimer = setTimeout(function() {
    if (confirm('Would you like to go to your dashboard now?')) {
        window.location.href = '{{ route("dashboard") }}';
    }
}, 30000);

// Clear timer if user interacts with the page
document.addEventListener('click', function() {
    clearTimeout(redirectTimer);
});

// Track subscription success event
if (typeof gtag !== 'undefined') {
    gtag('event', 'purchase', {
        'transaction_id': '{{ $subscription->id }}',
        'value': {{ $subscription->amount }},
        'currency': 'USD',
        'items': [{
            'item_id': '{{ $subscription->subscriptionPlan->id }}',
            'item_name': '{{ $subscription->subscriptionPlan->name }}',
            'category': 'Subscription',
            'quantity': 1,
            'price': {{ $subscription->amount }}
        }]
    });
}
</script>
@endpush
