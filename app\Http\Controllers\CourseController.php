<?php

namespace App\Http\Controllers;

use App\Models\Course;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CourseController extends Controller
{
    /**
     * Display a listing of courses.
     */
    public function index(Request $request)
    {
        $query = Course::published()->with('mentor');

        // Filter by category
        if ($request->filled('category')) {
            $query->category($request->category);
        }

        // Filter by difficulty
        if ($request->filled('difficulty')) {
            $query->difficulty($request->difficulty);
        }

        // Search by title
        if ($request->filled('search')) {
            $query->where('title', 'like', '%' . $request->search . '%');
        }

        // Sort options
        $sortBy = $request->get('sort', 'featured');
        switch ($sortBy) {
            case 'newest':
                $query->orderBy('created_at', 'desc');
                break;
            case 'oldest':
                $query->orderBy('created_at', 'asc');
                break;
            case 'title':
                $query->orderBy('title', 'asc');
                break;
            case 'featured':
            default:
                $query->orderBy('is_featured', 'desc')->orderBy('sort_order');
                break;
        }

        $courses = $query->paginate(12);

        // Get filter options
        $categories = Course::published()->distinct()->pluck('category')->filter()->sort();
        $difficulties = ['beginner', 'intermediate', 'advanced'];

        return view('courses.index', compact('courses', 'categories', 'difficulties'));
    }

    /**
     * Display the specified course.
     */
    public function show(Course $course)
    {
        // Check if course is published
        if (!$course->is_published) {
            abort(404);
        }

        $course->load(['mentor', 'publishedLessons']);

        $user = Auth::user();
        $canAccess = $user ? $course->canBeAccessedBy($user) : false;
        $completionPercentage = $user ? $course->getCompletionPercentageForUser($user->id) : 0;

        // Get user's progress for each lesson
        $lessonProgress = [];
        if ($user) {
            foreach ($course->publishedLessons as $lesson) {
                $lessonProgress[$lesson->id] = [
                    'completed' => $lesson->isCompletedBy($user->id),
                    'watch_time' => $lesson->getWatchTimeForUser($user->id),
                    'percentage' => $lesson->getCompletionPercentageForUser($user->id),
                ];
            }
        }

        // Get related courses
        $relatedCourses = Course::published()
            ->where('category', $course->category)
            ->where('id', '!=', $course->id)
            ->with('mentor')
            ->take(4)
            ->get();

        return view('courses.show', compact(
            'course',
            'canAccess',
            'completionPercentage',
            'lessonProgress',
            'relatedCourses'
        ));
    }

    /**
     * Enroll user in course.
     */
    public function enroll(Course $course)
    {
        $user = Auth::user();

        if (!$course->canBeAccessedBy($user)) {
            return back()->withErrors(['access' => 'You need an active subscription to access this course.']);
        }

        // Mark first lesson as started if exists
        $firstLesson = $course->publishedLessons()->first();
        if ($firstLesson) {
            $firstLesson->markAsStartedBy($user->id);
        }

        return redirect()->route('courses.show', $course)
            ->with('success', 'Successfully enrolled in the course!');
    }

    /**
     * Show course purchase page.
     */
    public function purchase(Course $course)
    {
        $user = Auth::user();

        // Check if user already has access
        if ($course->canBeAccessedBy($user)) {
            return redirect()->route('courses.show', $course)
                ->with('info', 'You already have access to this course.');
        }

        // Check if course is free
        if ($course->is_free) {
            return $this->enroll($course);
        }

        return view('courses.purchase', compact('course'));
    }

    /**
     * Process course purchase.
     */
    public function processPurchase(Request $request, Course $course)
    {
        $request->validate([
            'payment_method' => 'required|string',
        ]);

        $user = Auth::user();

        // Check if user already has access
        if ($course->canBeAccessedBy($user)) {
            return redirect()->route('courses.show', $course)
                ->with('info', 'You already have access to this course.');
        }

        try {
            // Create payment intent with Stripe
            $paymentIntent = $user->createSetupIntent();

            // For now, we'll simulate a successful payment
            // In production, you'd handle the actual Stripe payment here

            // Create enrollment record
            $enrollment = $user->enrollments()->create([
                'course_id' => $course->id,
                'enrolled_at' => now(),
                'enrollment_source' => 'purchase',
                'is_active' => true,
            ]);

            // Create payment record (you'd need to create this model)
            // Payment::create([
            //     'user_id' => $user->id,
            //     'course_id' => $course->id,
            //     'amount' => $course->price,
            //     'currency' => 'USD',
            //     'status' => 'completed',
            //     'paid_at' => now(),
            // ]);

            return redirect()->route('courses.show', $course)
                ->with('success', 'Course purchased successfully! You now have access.');

        } catch (\Exception $e) {
            return back()->withErrors(['payment' => 'Payment failed. Please try again.']);
        }
    }

    /**
     * Get courses by category.
     */
    public function category($category)
    {
        $courses = Course::published()
            ->category($category)
            ->with('mentor')
            ->ordered()
            ->paginate(12);

        return view('courses.category', compact('courses', 'category'));
    }

    /**
     * Search courses.
     */
    public function search(Request $request)
    {
        $query = $request->get('q');

        if (empty($query)) {
            return redirect()->route('courses.index');
        }

        $courses = Course::published()
            ->where(function ($q) use ($query) {
                $q->where('title', 'like', '%' . $query . '%')
                  ->orWhere('description', 'like', '%' . $query . '%')
                  ->orWhere('category', 'like', '%' . $query . '%');
            })
            ->with('mentor')
            ->paginate(12);

        return view('courses.search', compact('courses', 'query'));
    }
}
