@extends('layouts.admin')

@section('page-title', 'Dashboard')

@section('content')
<!-- Stats Overview -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Users -->
    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Users</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ number_format($totalUsers) }}</dd>
                    </dl>
                </div>
            </div>
        </div>
        <div class="bg-gray-50 px-5 py-3">
            <div class="text-sm">
                <span class="text-green-600 font-medium">+{{ $newUsersToday }}</span>
                <span class="text-gray-500">today</span>
            </div>
        </div>
    </div>

    <!-- Revenue -->
    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Revenue</dt>
                        <dd class="text-lg font-medium text-gray-900">${{ number_format($totalRevenue, 2) }}</dd>
                    </dl>
                </div>
            </div>
        </div>
        <div class="bg-gray-50 px-5 py-3">
            <div class="text-sm">
                <span class="text-green-600 font-medium">${{ number_format($monthlyRevenue, 2) }}</span>
                <span class="text-gray-500">this month</span>
            </div>
        </div>
    </div>

    <!-- Courses -->
    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Courses</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ $totalCourses }}</dd>
                    </dl>
                </div>
            </div>
        </div>
        <div class="bg-gray-50 px-5 py-3">
            <div class="text-sm">
                <span class="text-blue-600 font-medium">{{ $publishedCourses }}</span>
                <span class="text-gray-500">published</span>
            </div>
        </div>
    </div>

    <!-- Live Calls -->
    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Live Calls</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ $totalLiveCalls }}</dd>
                    </dl>
                </div>
            </div>
        </div>
        <div class="bg-gray-50 px-5 py-3">
            <div class="text-sm">
                <span class="text-orange-600 font-medium">{{ $upcomingCalls }}</span>
                <span class="text-gray-500">upcoming</span>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
    <!-- User Growth Chart -->
    <div class="bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">User Growth (Last 30 Days)</h3>
        <div class="h-64">
            <canvas id="userGrowthChart"></canvas>
        </div>
    </div>

    <!-- Revenue Chart -->
    <div class="bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Revenue Growth (Last 30 Days)</h3>
        <div class="h-64">
            <canvas id="revenueChart"></canvas>
        </div>
    </div>
</div>

<!-- System Health -->
<div class="bg-white shadow rounded-lg p-6 mb-8">
    <h3 class="text-lg font-medium text-gray-900 mb-4">System Health</h3>
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        @foreach($systemHealth as $service => $health)
        <div class="flex items-center p-3 rounded-lg {{ $health['status'] === 'healthy' ? 'bg-green-50' : ($health['status'] === 'warning' ? 'bg-yellow-50' : 'bg-red-50') }}">
            <div class="flex-shrink-0 mr-3">
                @if($health['status'] === 'healthy')
                    <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                @elseif($health['status'] === 'warning')
                    <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                @else
                    <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                @endif
            </div>
            <div>
                <p class="text-sm font-medium text-gray-900 capitalize">{{ $service }}</p>
                <p class="text-xs text-gray-600">{{ $health['message'] }}</p>
            </div>
        </div>
        @endforeach
    </div>
</div>

<!-- Recent Activity -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    <!-- Recent Users -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Recent Users</h3>
        </div>
        <div class="divide-y divide-gray-200">
            @foreach($recentUsers as $user)
            <div class="px-6 py-4">
                <div class="flex items-center">
                    @if($user->avatar)
                        <img class="h-8 w-8 rounded-full" src="{{ $user->avatar }}" alt="{{ $user->name }}">
                    @else
                        <div class="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center">
                            <span class="text-xs font-medium text-gray-700">{{ substr($user->name, 0, 1) }}</span>
                        </div>
                    @endif
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-900">{{ $user->name }}</p>
                        <p class="text-xs text-gray-500">{{ $user->created_at->diffForHumans() }}</p>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
        <div class="px-6 py-3 bg-gray-50">
            <a href="{{ route('admin.users.index') }}" class="text-sm font-medium text-blue-600 hover:text-blue-500">
                View all users →
            </a>
        </div>
    </div>

    <!-- Recent Posts -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Recent Community Posts</h3>
        </div>
        <div class="divide-y divide-gray-200">
            @foreach($recentPosts as $post)
            <div class="px-6 py-4">
                <div class="flex items-start">
                    @if($post->user->avatar)
                        <img class="h-6 w-6 rounded-full" src="{{ $post->user->avatar }}" alt="{{ $post->user->name }}">
                    @else
                        <div class="h-6 w-6 rounded-full bg-gray-300 flex items-center justify-center">
                            <span class="text-xs font-medium text-gray-700">{{ substr($post->user->name, 0, 1) }}</span>
                        </div>
                    @endif
                    <div class="ml-3 flex-1">
                        <p class="text-sm font-medium text-gray-900 line-clamp-2">{{ $post->title }}</p>
                        <p class="text-xs text-gray-500">by {{ $post->user->name }} • {{ $post->created_at->diffForHumans() }}</p>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
        <div class="px-6 py-3 bg-gray-50">
            <a href="{{ route('admin.community.index') }}" class="text-sm font-medium text-blue-600 hover:text-blue-500">
                View all posts →
            </a>
        </div>
    </div>

    <!-- Recent Subscriptions -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Recent Subscriptions</h3>
        </div>
        <div class="divide-y divide-gray-200">
            @foreach($recentSubscriptions as $subscription)
            <div class="px-6 py-4">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-900">{{ $subscription->user->name }}</p>
                        <p class="text-xs text-gray-500">{{ $subscription->subscriptionPlan->name }}</p>
                    </div>
                    <div class="text-right">
                        <p class="text-sm font-medium text-green-600">${{ number_format($subscription->amount, 2) }}</p>
                        <p class="text-xs text-gray-500">{{ $subscription->created_at->diffForHumans() }}</p>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
        <div class="px-6 py-3 bg-gray-50">
            <a href="{{ route('admin.subscriptions.index') }}" class="text-sm font-medium text-blue-600 hover:text-blue-500">
                View all subscriptions →
            </a>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// User Growth Chart
const userCtx = document.getElementById('userGrowthChart').getContext('2d');
const userChart = new Chart(userCtx, {
    type: 'line',
    data: {
        labels: {!! json_encode(array_column($userGrowthData, 'date')) !!},
        datasets: [{
            label: 'New Users',
            data: {!! json_encode(array_column($userGrowthData, 'count')) !!},
            borderColor: 'rgb(59, 130, 246)',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            tension: 0.4,
            fill: true
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        },
        plugins: {
            legend: {
                display: false
            }
        }
    }
});

// Revenue Chart
const revenueCtx = document.getElementById('revenueChart').getContext('2d');
const revenueChart = new Chart(revenueCtx, {
    type: 'line',
    data: {
        labels: {!! json_encode(array_column($revenueGrowthData, 'date')) !!},
        datasets: [{
            label: 'Revenue',
            data: {!! json_encode(array_column($revenueGrowthData, 'amount')) !!},
            borderColor: 'rgb(16, 185, 129)',
            backgroundColor: 'rgba(16, 185, 129, 0.1)',
            tension: 0.4,
            fill: true
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return '$' + value.toFixed(2);
                    }
                }
            }
        },
        plugins: {
            legend: {
                display: false
            }
        }
    }
});
</script>
@endpush
