<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\Course;
use App\Models\UserProgress;
use App\Services\CourseService;
use Illuminate\Console\Command;

class GenerateCertificates extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'certificates:generate {--user= : Generate certificates for specific user ID} {--course= : Generate certificates for specific course ID}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate certificates for users who have completed courses';

    protected $courseService;

    /**
     * Create a new command instance.
     */
    public function __construct(CourseService $courseService)
    {
        parent::__construct();
        $this->courseService = $courseService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting certificate generation...');

        $userId = $this->option('user');
        $courseId = $this->option('course');

        // Build query for users who completed courses
        $query = $this->buildCompletionQuery($userId, $courseId);
        $completions = $query->get();

        if ($completions->isEmpty()) {
            $this->info('No completed courses found that need certificates.');
            return 0;
        }

        $this->info("Found {$completions->count()} completed courses to process.");

        $generated = 0;
        $skipped = 0;

        foreach ($completions as $completion) {
            $user = User::find($completion->user_id);
            $course = Course::find($completion->course_id);

            $this->line("Processing: {$user->name} - {$course->title}");

            try {
                $certificate = $this->courseService->completeCourse($user, $course);

                if ($certificate && $certificate->wasRecentlyCreated) {
                    $this->info("✓ Certificate generated: {$certificate->certificate_number}");
                    $generated++;
                } else {
                    $this->warn("⚠ Certificate already exists or course not fully completed.");
                    $skipped++;
                }
            } catch (\Exception $e) {
                $this->error("✗ Failed to generate certificate: {$e->getMessage()}");
                $skipped++;
            }
        }

        $this->newLine();
        $this->info("Certificate generation complete!");
        $this->table(
            ['Status', 'Count'],
            [
                ['Generated', $generated],
                ['Skipped', $skipped],
            ]
        );

        return 0;
    }

    /**
     * Build query for course completions.
     */
    protected function buildCompletionQuery($userId = null, $courseId = null)
    {
        $query = UserProgress::selectRaw('user_id, course_id, COUNT(*) as total_lessons, SUM(is_completed) as completed_lessons')
            ->groupBy('user_id', 'course_id')
            ->havingRaw('total_lessons = completed_lessons')
            ->havingRaw('total_lessons > 0');

        if ($userId) {
            $query->where('user_id', $userId);
        }

        if ($courseId) {
            $query->where('course_id', $courseId);
        }

        // Only include users with active subscriptions or free courses
        $query->whereExists(function ($subQuery) {
            $subQuery->select(\DB::raw(1))
                ->from('courses')
                ->whereColumn('courses.id', 'user_progress.course_id')
                ->where(function ($courseQuery) {
                    $courseQuery->where('courses.is_free', true)
                        ->orWhereExists(function ($subscriptionQuery) {
                            $subscriptionQuery->select(\DB::raw(1))
                                ->from('user_subscriptions')
                                ->whereColumn('user_subscriptions.user_id', 'user_progress.user_id')
                                ->where('user_subscriptions.status', 'active');
                        });
                });
        });

        // Exclude users who already have certificates
        $query->whereNotExists(function ($subQuery) {
            $subQuery->select(\DB::raw(1))
                ->from('certificates')
                ->whereColumn('certificates.user_id', 'user_progress.user_id')
                ->whereColumn('certificates.course_id', 'user_progress.course_id');
        });

        return $query;
    }
}
