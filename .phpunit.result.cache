{"version": 1, "defects": {"Tests\\Unit\\CourseTest::it_can_create_a_course": 8, "Tests\\Unit\\CourseTest::it_belongs_to_a_mentor": 8, "Tests\\Unit\\CourseTest::it_can_have_many_lessons": 8, "Tests\\Unit\\CourseTest::it_can_get_published_lessons_only": 8, "Tests\\Unit\\CourseTest::it_can_calculate_total_duration": 8, "Tests\\Unit\\CourseTest::it_can_get_enrollment_count": 8, "Tests\\Unit\\CourseTest::it_can_get_completion_rate": 8, "Tests\\Unit\\CourseTest::it_can_get_average_rating": 8, "Tests\\Unit\\CourseTest::it_returns_zero_rating_when_no_ratings": 8, "Tests\\Unit\\CourseTest::it_can_check_if_user_is_enrolled": 8, "Tests\\Unit\\CourseTest::it_can_check_if_user_completed_course": 8, "Tests\\Unit\\CourseTest::it_can_get_user_progress_percentage": 8, "Tests\\Unit\\CourseTest::it_returns_zero_progress_for_unenrolled_user": 8, "Tests\\Unit\\CourseTest::it_can_scope_published_courses": 8, "Tests\\Unit\\CourseTest::it_can_scope_featured_courses": 8, "Tests\\Unit\\CourseTest::it_can_scope_by_category": 8, "Tests\\Unit\\CourseTest::it_can_scope_by_difficulty": 8, "Tests\\Unit\\CourseTest::it_can_search_courses": 8, "Tests\\Unit\\CourseTest::it_can_get_next_lesson_for_user": 8, "Tests\\Unit\\CourseTest::it_returns_first_lesson_for_new_user": 8, "Tests\\Unit\\CourseTest::it_can_check_required_subscription_plans": 8, "Tests\\Unit\\CourseTest::it_allows_access_when_no_plans_required": 8, "Tests\\Unit\\UserTest::it_can_create_a_user": 8, "Tests\\Unit\\UserTest::it_hashes_password_when_creating_user": 8, "Tests\\Unit\\UserTest::it_can_assign_roles_to_user": 8, "Tests\\Unit\\UserTest::it_can_check_multiple_roles": 8, "Tests\\Unit\\UserTest::it_can_have_active_subscription": 8, "Tests\\Unit\\UserTest::it_returns_null_for_inactive_subscription": 8, "Tests\\Unit\\UserTest::it_can_enroll_in_courses": 8, "Tests\\Unit\\UserTest::it_cannot_enroll_in_unpublished_course": 8, "Tests\\Unit\\UserTest::it_can_calculate_course_progress": 8, "Tests\\Unit\\UserTest::it_can_get_completed_courses": 8, "Tests\\Unit\\UserTest::it_can_check_subscription_access_to_course": 8, "Tests\\Unit\\UserTest::it_denies_access_without_required_subscription": 8, "Tests\\Unit\\UserTest::it_can_update_last_login": 8, "Tests\\Unit\\UserTest::it_can_be_activated_and_deactivated": 8, "Tests\\Unit\\UserTest::it_generates_full_name_correctly": 8, "Tests\\Unit\\UserTest::it_can_get_avatar_url": 8, "Tests\\Unit\\UserTest::it_returns_default_avatar_when_none_set": 8}, "times": {"Tests\\Unit\\CourseTest::it_can_create_a_course": 0.047, "Tests\\Unit\\CourseTest::it_belongs_to_a_mentor": 0.003, "Tests\\Unit\\CourseTest::it_can_have_many_lessons": 0.001, "Tests\\Unit\\CourseTest::it_can_get_published_lessons_only": 0.001, "Tests\\Unit\\CourseTest::it_can_calculate_total_duration": 0.001, "Tests\\Unit\\CourseTest::it_can_get_enrollment_count": 0.001, "Tests\\Unit\\CourseTest::it_can_get_completion_rate": 0.002, "Tests\\Unit\\CourseTest::it_can_get_average_rating": 0.001, "Tests\\Unit\\CourseTest::it_returns_zero_rating_when_no_ratings": 0.001, "Tests\\Unit\\CourseTest::it_can_check_if_user_is_enrolled": 0.001, "Tests\\Unit\\CourseTest::it_can_check_if_user_completed_course": 0.001, "Tests\\Unit\\CourseTest::it_can_get_user_progress_percentage": 0.001, "Tests\\Unit\\CourseTest::it_returns_zero_progress_for_unenrolled_user": 0.001, "Tests\\Unit\\CourseTest::it_can_scope_published_courses": 0.002, "Tests\\Unit\\CourseTest::it_can_scope_featured_courses": 0.001, "Tests\\Unit\\CourseTest::it_can_scope_by_category": 0.002, "Tests\\Unit\\CourseTest::it_can_scope_by_difficulty": 0.001, "Tests\\Unit\\CourseTest::it_can_search_courses": 0.001, "Tests\\Unit\\CourseTest::it_can_get_next_lesson_for_user": 0.001, "Tests\\Unit\\CourseTest::it_returns_first_lesson_for_new_user": 0.001, "Tests\\Unit\\CourseTest::it_can_check_required_subscription_plans": 0.001, "Tests\\Unit\\CourseTest::it_allows_access_when_no_plans_required": 0.001, "Tests\\Unit\\ExampleTest::test_that_true_is_true": 0.003}}