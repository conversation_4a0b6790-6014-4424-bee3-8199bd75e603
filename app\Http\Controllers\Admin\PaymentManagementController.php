<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Payment;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class PaymentManagementController extends Controller
{
    /**
     * Display a listing of payments.
     */
    public function index(Request $request)
    {
        $query = Payment::with(['user', 'subscription', 'course']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('provider_payment_id', 'like', "%{$search}%")
                  ->orWhereHas('user', function ($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                               ->orWhere('email', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by payment method
        if ($request->filled('payment_method')) {
            $query->where('payment_method', $request->payment_method);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->where('created_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->where('created_at', '<=', $request->date_to . ' 23:59:59');
        }

        $payments = $query->latest()->paginate(20);

        // Get statistics
        $stats = [
            'total_payments' => Payment::count(),
            'completed_payments' => Payment::where('status', 'completed')->count(),
            'pending_payments' => Payment::where('status', 'pending')->count(),
            'failed_payments' => Payment::where('status', 'failed')->count(),
            'total_revenue' => Payment::where('status', 'completed')->sum('amount'),
            'monthly_revenue' => Payment::where('status', 'completed')
                ->whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->sum('amount'),
            'refunded_amount' => Payment::where('status', 'refunded')->sum('refund_amount'),
        ];

        return view('admin.payments.index', compact('payments', 'stats'));
    }

    /**
     * Display the specified payment.
     */
    public function show(Payment $payment)
    {
        $payment->load(['user', 'subscription', 'course']);
        
        return view('admin.payments.show', compact('payment'));
    }

    /**
     * Process a refund for a payment.
     */
    public function refund(Request $request, Payment $payment)
    {
        $request->validate([
            'refund_amount' => 'required|numeric|min:0.01|max:' . $payment->amount,
            'reason' => 'required|string|max:500',
        ]);

        try {
            // Process refund through Stripe
            if ($payment->payment_provider === 'stripe' && $payment->provider_payment_id) {
                \Stripe\Stripe::setApiKey(config('cashier.secret'));
                
                $refund = \Stripe\Refund::create([
                    'payment_intent' => $payment->provider_payment_id,
                    'amount' => $request->refund_amount * 100, // Convert to cents
                    'reason' => 'requested_by_customer',
                    'metadata' => [
                        'admin_reason' => $request->reason,
                        'admin_user' => auth()->user()->name,
                    ],
                ]);

                $payment->update([
                    'status' => 'refunded',
                    'refund_amount' => $request->refund_amount,
                    'refunded_at' => now(),
                    'metadata' => array_merge($payment->metadata ?? [], [
                        'refund_reason' => $request->reason,
                        'refund_processed_by' => auth()->user()->name,
                        'stripe_refund_id' => $refund->id,
                    ]),
                ]);

                return back()->with('success', 'Refund processed successfully.');
            }

            return back()->withErrors(['error' => 'Refund processing not supported for this payment method.']);
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to process refund: ' . $e->getMessage()]);
        }
    }

    /**
     * Export payments to CSV.
     */
    public function export(Request $request)
    {
        $query = Payment::with(['user', 'subscription', 'course']);

        // Apply same filters as index
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }
        if ($request->filled('payment_method')) {
            $query->where('payment_method', $request->payment_method);
        }
        if ($request->filled('date_from')) {
            $query->where('created_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->where('created_at', '<=', $request->date_to . ' 23:59:59');
        }

        $payments = $query->latest()->get();

        $filename = 'payments_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function() use ($payments) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'ID', 'User', 'Email', 'Amount', 'Currency', 'Status',
                'Payment Method', 'Provider', 'Provider ID', 'Type',
                'Created At', 'Paid At', 'Refunded At', 'Refund Amount'
            ]);

            foreach ($payments as $payment) {
                fputcsv($file, [
                    $payment->id,
                    $payment->user->name,
                    $payment->user->email,
                    $payment->amount,
                    $payment->currency,
                    $payment->status,
                    $payment->payment_method,
                    $payment->payment_provider,
                    $payment->provider_payment_id,
                    $payment->subscription_id ? 'Subscription' : 'Course',
                    $payment->created_at->format('Y-m-d H:i:s'),
                    $payment->paid_at ? $payment->paid_at->format('Y-m-d H:i:s') : '',
                    $payment->refunded_at ? $payment->refunded_at->format('Y-m-d H:i:s') : '',
                    $payment->refund_amount ?? '',
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Get payment analytics data.
     */
    public function analytics(Request $request)
    {
        $period = $request->get('period', '30'); // days

        $startDate = now()->subDays($period);

        // Revenue over time
        $revenueData = Payment::where('status', 'completed')
            ->where('created_at', '>=', $startDate)
            ->selectRaw('DATE(created_at) as date, SUM(amount) as revenue')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // Payment methods breakdown
        $paymentMethods = Payment::where('status', 'completed')
            ->where('created_at', '>=', $startDate)
            ->selectRaw('payment_method, COUNT(*) as count, SUM(amount) as revenue')
            ->groupBy('payment_method')
            ->get();

        // Top customers by revenue
        $topCustomers = Payment::where('status', 'completed')
            ->where('created_at', '>=', $startDate)
            ->with('user')
            ->selectRaw('user_id, SUM(amount) as total_spent, COUNT(*) as payment_count')
            ->groupBy('user_id')
            ->orderBy('total_spent', 'desc')
            ->limit(10)
            ->get();

        return view('admin.payments.analytics', compact(
            'revenueData', 'paymentMethods', 'topCustomers', 'period'
        ));
    }
}
