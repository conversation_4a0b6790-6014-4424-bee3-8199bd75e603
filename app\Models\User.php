<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use <PERSON><PERSON>\Cashier\Billable;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable implements MustVerifyEmail
{
    use HasApiTokens, HasFactory, Notifiable, Billable, HasRoles;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'first_name',
        'last_name',
        'email',
        'password',
        'phone',
        'bio',
        'avatar',
        'country',
        'timezone',
        'role', // User role (student, mentor, admin)
        'title', // For mentors
        'specialties', // For mentors
        'social_links', // For mentors
        'portfolio_url', // For mentors
        'is_active',
        'is_verified', // For mentors
        'is_featured', // For mentors
        'average_rating', // For mentors
        'last_login_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'last_login_at' => 'datetime',
        'is_active' => 'boolean',
        'is_verified' => 'boolean',
        'is_featured' => 'boolean',
        'specialties' => 'array',
        'social_links' => 'array',
        'average_rating' => 'decimal:1',
    ];

    /**
     * Get the user's full name.
     */
    public function getFullNameAttribute(): string
    {
        return trim($this->first_name . ' ' . $this->last_name) ?: $this->name;
    }

    /**
     * Get the user's subscription plans.
     */
    public function subscriptions()
    {
        return $this->hasMany(UserSubscription::class);
    }

    /**
     * Get the user's active subscription.
     */
    public function activeSubscription()
    {
        return $this->hasOne(UserSubscription::class)->where('status', 'active');
    }

    /**
     * Get the user's enrollments.
     */
    public function enrollments()
    {
        return $this->hasMany(Enrollment::class);
    }

    /**
     * Get the user's reviews.
     */
    public function reviews()
    {
        return $this->hasMany(Review::class);
    }

    /**
     * Get the mentors this user follows.
     */
    public function followedMentors()
    {
        return $this->belongsToMany(User::class, 'mentor_followers', 'user_id', 'mentor_id')
                    ->withTimestamps();
    }

    /**
     * Get the users following this mentor.
     */
    public function followers()
    {
        return $this->belongsToMany(User::class, 'mentor_followers', 'mentor_id', 'user_id')
                    ->withTimestamps();
    }

    /**
     * Get the courses created by this mentor.
     */
    public function courses()
    {
        return $this->hasMany(Course::class, 'mentor_id');
    }

    /**
     * Get the certificates earned by this user.
     */
    public function certificates()
    {
        return $this->hasMany(Certificate::class);
    }

    /**
     * Get the live calls hosted by this mentor.
     */
    public function hostedLiveCalls()
    {
        return $this->hasMany(LiveCall::class, 'mentor_id');
    }

    /**
     * Get the achievements earned by this user.
     */
    public function achievements()
    {
        return $this->hasMany(UserAchievement::class);
    }

    /**
     * Get the user's course progress.
     */
    public function progress()
    {
        return $this->hasMany(UserProgress::class);
    }

    /**
     * Get courses created by this user (if mentor).
     */
    public function createdCourses()
    {
        return $this->hasMany(Course::class, 'mentor_id');
    }

    /**
     * Check if user has active subscription.
     */
    public function hasActiveSubscription(): bool
    {
        return $this->activeSubscription()->exists();
    }

    /**
     * User subscriptions relationship.
     */
    public function userSubscriptions()
    {
        return $this->hasMany(UserSubscription::class);
    }

    /**
     * Check if user can access a specific feature.
     */
    public function canAccess(string $feature): bool
    {
        $subscription = $this->activeSubscription;
        if (!$subscription) {
            return false;
        }

        $plan = $subscription->subscriptionPlan;
        return match($feature) {
            'live_calls' => $plan->live_calls_access,
            'community' => $plan->community_access,
            'mentor_access' => $plan->mentor_access,
            default => false,
        };
    }

    /**
     * Check if user is an admin.
     */
    public function isAdmin(): bool
    {
        return $this->role === 'admin';
    }

    /**
     * Check if user is a mentor.
     */
    public function isMentor(): bool
    {
        return $this->role === 'mentor';
    }

    /**
     * Check if user is a student.
     */
    public function isStudent(): bool
    {
        return $this->role === 'student' || !$this->role;
    }

    /**
     * Get user's average rating (for mentors).
     */
    public function getAverageRatingAttribute()
    {
        if ($this->isMentor()) {
            return $this->reviews()->avg('rating') ?? 0;
        }
        return 0;
    }

    /**
     * Get user's reputation score.
     */
    public function getReputationAttribute()
    {
        // Calculate reputation based on various factors
        $reputation = 0;

        if ($this->isMentor()) {
            $reputation += $this->courses()->count() * 10; // 10 points per course
            $reputation += $this->reviews()->where('rating', '>=', 4)->count() * 5; // 5 points per good review
            $reputation += $this->followers()->count() * 2; // 2 points per follower
        }

        return $reputation;
    }

    /**
     * Get the community posts created by the user.
     */
    public function communityPosts()
    {
        return $this->hasMany(CommunityPost::class);
    }

    /**
     * Get the community comments created by the user.
     */
    public function communityComments()
    {
        return $this->hasMany(CommunityComment::class);
    }
}
