<?php

namespace App\Notifications;

use App\Models\Course;
use App\Models\Certificate;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class CourseCompletionNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $course;
    protected $certificate;

    /**
     * Create a new notification instance.
     */
    public function __construct(Course $course, Certificate $certificate)
    {
        $this->course = $course;
        $this->certificate = $certificate;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('🎉 Congratulations! Course Completed - ' . $this->course->title)
            ->greeting('Congratulations, ' . $notifiable->first_name . '!')
            ->line('You have successfully completed: **' . $this->course->title . '**')
            ->line('This is a significant achievement that demonstrates your commitment to learning and personal growth.')
            ->line('**Course Details:**')
            ->line('• Instructor: ' . $this->course->mentor->name)
            ->line('• Completion Date: ' . $this->certificate->issued_at->format('M d, Y'))
            ->line('• Certificate Number: ' . $this->certificate->certificate_number)
            ->action('Download Certificate', $this->certificate->verification_url)
            ->line('Your certificate has been added to your profile and can be shared with employers or on social media.')
            ->line('Keep up the excellent work and continue your journey to financial freedom!')
            ->salutation('Proud of your achievement,')
            ->salutation('The Real World Team');
    }

    /**
     * Get the database representation of the notification.
     */
    public function toDatabase(object $notifiable): array
    {
        return [
            'title' => 'Course Completed! 🎉',
            'message' => 'Congratulations! You have completed: ' . $this->course->title,
            'action_url' => $this->certificate->verification_url,
            'action_text' => 'View Certificate',
            'type' => 'course_completion',
            'course_id' => $this->course->id,
            'certificate_id' => $this->certificate->id,
        ];
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return $this->toDatabase($notifiable);
    }
}
