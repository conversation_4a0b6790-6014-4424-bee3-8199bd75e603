<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;

class SettingsController extends Controller
{
    /**
     * Display general settings.
     */
    public function general()
    {
        $settings = [
            'site_name' => setting('site_name', 'The Real World'),
            'site_description' => setting('site_description', 'Learn from the best mentors'),
            'site_logo' => setting('site_logo'),
            'site_favicon' => setting('site_favicon'),
            'contact_email' => setting('contact_email', '<EMAIL>'),
            'support_phone' => setting('support_phone'),
            'timezone' => setting('timezone', 'UTC'),
            'date_format' => setting('date_format', 'Y-m-d'),
            'time_format' => setting('time_format', 'H:i'),
            'maintenance_mode' => setting('maintenance_mode', false),
            'registration_enabled' => setting('registration_enabled', true),
            'email_verification_required' => setting('email_verification_required', true),
        ];

        return view('admin.settings.general', compact('settings'));
    }

    /**
     * Update general settings.
     */
    public function updateGeneral(Request $request)
    {
        $request->validate([
            'site_name' => 'required|string|max:255',
            'site_description' => 'required|string|max:500',
            'contact_email' => 'required|email',
            'support_phone' => 'nullable|string|max:20',
            'timezone' => 'required|string',
            'date_format' => 'required|string',
            'time_format' => 'required|string',
            'site_logo' => 'nullable|image|max:2048',
            'site_favicon' => 'nullable|image|max:512',
        ]);

        $settings = $request->except(['_token', 'site_logo', 'site_favicon']);

        // Handle file uploads
        if ($request->hasFile('site_logo')) {
            $logoPath = $request->file('site_logo')->store('settings', 'public');
            $settings['site_logo'] = $logoPath;
        }

        if ($request->hasFile('site_favicon')) {
            $faviconPath = $request->file('site_favicon')->store('settings', 'public');
            $settings['site_favicon'] = $faviconPath;
        }

        // Convert checkboxes to boolean
        $settings['maintenance_mode'] = $request->has('maintenance_mode');
        $settings['registration_enabled'] = $request->has('registration_enabled');
        $settings['email_verification_required'] = $request->has('email_verification_required');

        foreach ($settings as $key => $value) {
            Setting::updateOrCreate(['key' => $key], ['value' => $value]);
        }

        // Clear settings cache
        Cache::forget('settings');

        return back()->with('success', 'General settings updated successfully.');
    }

    /**
     * Display payment settings.
     */
    public function payment()
    {
        $settings = [
            'stripe_publishable_key' => setting('stripe_publishable_key'),
            'stripe_secret_key' => setting('stripe_secret_key'),
            'stripe_webhook_secret' => setting('stripe_webhook_secret'),
            'paypal_client_id' => setting('paypal_client_id'),
            'paypal_client_secret' => setting('paypal_client_secret'),
            'paypal_mode' => setting('paypal_mode', 'sandbox'),
            'currency' => setting('currency', 'USD'),
            'currency_symbol' => setting('currency_symbol', '$'),
            'tax_rate' => setting('tax_rate', 0),
            'enable_crypto_payments' => setting('enable_crypto_payments', false),
        ];

        return view('admin.settings.payment', compact('settings'));
    }

    /**
     * Update payment settings.
     */
    public function updatePayment(Request $request)
    {
        $request->validate([
            'stripe_publishable_key' => 'nullable|string',
            'stripe_secret_key' => 'nullable|string',
            'stripe_webhook_secret' => 'nullable|string',
            'paypal_client_id' => 'nullable|string',
            'paypal_client_secret' => 'nullable|string',
            'paypal_mode' => 'required|in:sandbox,live',
            'currency' => 'required|string|size:3',
            'currency_symbol' => 'required|string|max:5',
            'tax_rate' => 'required|numeric|min:0|max:100',
        ]);

        $settings = $request->except(['_token']);
        $settings['enable_crypto_payments'] = $request->has('enable_crypto_payments');

        foreach ($settings as $key => $value) {
            Setting::updateOrCreate(['key' => $key], ['value' => $value]);
        }

        Cache::forget('settings');

        return back()->with('success', 'Payment settings updated successfully.');
    }

    /**
     * Display email settings.
     */
    public function email()
    {
        $settings = [
            'mail_driver' => setting('mail_driver', 'smtp'),
            'mail_host' => setting('mail_host'),
            'mail_port' => setting('mail_port', 587),
            'mail_username' => setting('mail_username'),
            'mail_password' => setting('mail_password'),
            'mail_encryption' => setting('mail_encryption', 'tls'),
            'mail_from_address' => setting('mail_from_address'),
            'mail_from_name' => setting('mail_from_name'),
        ];

        return view('admin.settings.email', compact('settings'));
    }

    /**
     * Update email settings.
     */
    public function updateEmail(Request $request)
    {
        $request->validate([
            'mail_driver' => 'required|in:smtp,sendmail,mailgun,ses,postmark',
            'mail_host' => 'required_if:mail_driver,smtp|nullable|string',
            'mail_port' => 'required_if:mail_driver,smtp|nullable|integer',
            'mail_username' => 'nullable|string',
            'mail_password' => 'nullable|string',
            'mail_encryption' => 'nullable|in:tls,ssl',
            'mail_from_address' => 'required|email',
            'mail_from_name' => 'required|string',
        ]);

        $settings = $request->except(['_token']);

        foreach ($settings as $key => $value) {
            Setting::updateOrCreate(['key' => $key], ['value' => $value]);
        }

        Cache::forget('settings');

        return back()->with('success', 'Email settings updated successfully.');
    }

    /**
     * Display system information.
     */
    public function system()
    {
        $systemInfo = [
            'php_version' => PHP_VERSION,
            'laravel_version' => app()->version(),
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'database_version' => $this->getDatabaseVersion(),
            'storage_used' => $this->getStorageUsed(),
            'cache_driver' => config('cache.default'),
            'queue_driver' => config('queue.default'),
            'session_driver' => config('session.driver'),
        ];

        return view('admin.settings.system', compact('systemInfo'));
    }

    /**
     * Clear application cache.
     */
    public function clearCache()
    {
        try {
            Artisan::call('cache:clear');
            Artisan::call('config:clear');
            Artisan::call('route:clear');
            Artisan::call('view:clear');

            return back()->with('success', 'Application cache cleared successfully.');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to clear cache: ' . $e->getMessage()]);
        }
    }

    /**
     * Display application logs.
     */
    public function logs()
    {
        $logFiles = [];
        $logPath = storage_path('logs');

        if (File::exists($logPath)) {
            $files = File::files($logPath);
            foreach ($files as $file) {
                if (pathinfo($file, PATHINFO_EXTENSION) === 'log') {
                    $logFiles[] = [
                        'name' => $file->getFilename(),
                        'size' => $file->getSize(),
                        'modified' => $file->getMTime(),
                    ];
                }
            }
        }

        // Sort by modification time (newest first)
        usort($logFiles, function($a, $b) {
            return $b['modified'] - $a['modified'];
        });

        return view('admin.settings.logs', compact('logFiles'));
    }

    /**
     * View a specific log file.
     */
    public function viewLog($filename)
    {
        $logPath = storage_path('logs/' . $filename);

        if (!File::exists($logPath) || !str_ends_with($filename, '.log')) {
            abort(404);
        }

        $content = File::get($logPath);
        $lines = array_reverse(explode("\n", $content));

        return view('admin.settings.log-viewer', compact('filename', 'lines'));
    }

    /**
     * Delete a log file.
     */
    public function deleteLog($filename)
    {
        $logPath = storage_path('logs/' . $filename);

        if (File::exists($logPath) && str_ends_with($filename, '.log')) {
            File::delete($logPath);
            return back()->with('success', 'Log file deleted successfully.');
        }

        return back()->withErrors(['error' => 'Log file not found.']);
    }

    /**
     * Get database version.
     */
    private function getDatabaseVersion()
    {
        try {
            return \DB::select('SELECT VERSION() as version')[0]->version;
        } catch (\Exception $e) {
            return 'Unknown';
        }
    }

    /**
     * Get storage usage.
     */
    private function getStorageUsed()
    {
        try {
            $bytes = 0;
            $path = storage_path();
            
            foreach (new \RecursiveIteratorIterator(new \RecursiveDirectoryIterator($path)) as $file) {
                $bytes += $file->getSize();
            }
            
            return $this->formatBytes($bytes);
        } catch (\Exception $e) {
            return 'Unknown';
        }
    }

    /**
     * Format bytes to human readable format.
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * Helper function to get setting value.
     */
    private function setting($key, $default = null)
    {
        return Setting::get($key, $default);
    }
}
