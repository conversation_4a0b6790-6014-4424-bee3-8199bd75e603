# The Real World LMS

<p align="center">
  <img src="https://therealworld.com/images/logo.png" width="200" alt="The Real World Logo">
</p>

<p align="center">
  <strong>The most exclusive online university in the world</strong><br>
  Escape The Matrix - Build Wealth - Achieve Financial Freedom
</p>

<p align="center">
  <a href="#features">Features</a> •
  <a href="#installation">Installation</a> •
  <a href="#configuration">Configuration</a> •
  <a href="#usage">Usage</a> •
  <a href="#api">API</a> •
  <a href="#testing">Testing</a> •
  <a href="#deployment">Deployment</a>
</p>

---

## 🎯 About The Real World LMS

The Real World LMS is a comprehensive Learning Management System built with Laravel 10, designed to replicate the functionality and design of the official Real World platform. This system provides a complete educational experience with courses, mentors, subscriptions, payments, and community features.

### 🌟 Key Highlights

- **Complete Course Management** - Create, manage, and deliver educational content
- **Multi-Payment Support** - Stripe integration + Cryptocurrency payments (BTC, ETH, USDT, BNB)
- **Subscription System** - Flexible subscription plans with different access levels
- **Real-time Features** - Live calls, notifications, and community interactions
- **Admin Dashboard** - Comprehensive admin panel for platform management
- **Mobile Responsive** - Fully responsive design for all devices
- **API Ready** - RESTful API for mobile app integration

## ✨ Features

### 🎓 Learning Management
- **Course Creation & Management** - Rich course builder with lessons, videos, and resources
- **Progress Tracking** - Detailed progress analytics for students and instructors
- **Certificates** - Automated certificate generation upon course completion
- **Video Streaming** - Integrated video player with progress tracking
- **Interactive Content** - Quizzes, assignments, and downloadable resources

### 👥 User Management
- **Role-based Access** - Admin, Mentor, and Student roles with specific permissions
- **Profile Management** - Comprehensive user profiles with avatars and bio
- **Authentication** - Secure login with email verification and 2FA support
- **User Analytics** - Detailed user activity and engagement tracking

### 💳 Payment & Subscriptions
- **Stripe Integration** - Secure credit card processing
- **Cryptocurrency Support** - Accept BTC, ETH, USDT, and BNB payments
- **Flexible Plans** - Multiple subscription tiers (Basic, Premium, Elite)
- **Billing Management** - Automated billing, invoicing, and subscription management
- **Coupon System** - Discount codes and promotional offers

### 🏆 Gamification & Engagement
- **Achievement System** - Badges and rewards for course completion
- **Leaderboards** - Student rankings and progress competitions
- **Community Features** - Discussion forums and peer interaction
- **Live Calls** - Scheduled live sessions with mentors
- **Notifications** - Real-time notifications for important events

### 📊 Analytics & Reporting
- **Dashboard Analytics** - Comprehensive admin and user dashboards
- **Course Statistics** - Enrollment, completion, and engagement metrics
- **Revenue Tracking** - Payment analytics and financial reporting
- **User Insights** - Detailed user behavior and learning patterns

### 🔧 Technical Features
- **RESTful API** - Complete API for mobile app integration
- **Real-time Updates** - WebSocket support for live features
- **File Management** - Secure file upload and storage system
- **Search & Filtering** - Advanced search across courses and content
- **SEO Optimized** - Meta tags, sitemaps, and search engine optimization
- **Security** - CSRF protection, XSS prevention, and secure authentication

## 🚀 Installation

### Prerequisites

- PHP 8.1 or higher
- Composer
- Node.js & NPM
- MySQL 8.0 or higher
- Redis (optional, for caching and queues)

### Step 1: Clone the Repository

```bash
git clone https://github.com/yourusername/therealworld-lms.git
cd therealworld-lms
```

### Step 2: Install Dependencies

```bash
# Install PHP dependencies
composer install

# Install Node.js dependencies
npm install
```

### Step 3: Environment Configuration

```bash
# Copy environment file
cp .env.example .env

# Generate application key
php artisan key:generate
```

### Step 4: Database Setup

```bash
# Create database
mysql -u root -p -e "CREATE DATABASE therealworld_lms;"

# Run migrations
php artisan migrate

# Seed the database
php artisan db:seed
```

### Step 5: Storage Setup

```bash
# Create storage link
php artisan storage:link

# Set permissions (Linux/Mac)
chmod -R 775 storage bootstrap/cache
```

### Step 6: Build Assets

```bash
# Build frontend assets
npm run build

# For development
npm run dev
```

### Step 7: Start the Application

```bash
# Start the development server
php artisan serve

# Start queue worker (in separate terminal)
php artisan queue:work

# Start scheduler (in production)
php artisan schedule:work
```

## ⚙️ Configuration

### Environment Variables

Update your `.env` file with the following configurations:

```env
# Application
APP_NAME="The Real World"
APP_URL=http://localhost:8000

# Database
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=therealworld_lms
DB_USERNAME=root
DB_PASSWORD=your_password

# Mail Configuration
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_app_password
MAIL_ENCRYPTION=tls

# Stripe Configuration
STRIPE_KEY=pk_test_your_stripe_public_key
STRIPE_SECRET=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Cryptocurrency Addresses
CRYPTO_BTC_ADDRESS=your_btc_address
CRYPTO_ETH_ADDRESS=your_eth_address
CRYPTO_USDT_ADDRESS=your_usdt_address
CRYPTO_BNB_ADDRESS=your_bnb_address
```

### Payment Configuration

#### Stripe Setup
1. Create a Stripe account at [stripe.com](https://stripe.com)
2. Get your API keys from the Stripe dashboard
3. Set up webhooks for subscription events
4. Update the `.env` file with your Stripe credentials

#### Cryptocurrency Setup
1. Set up cryptocurrency wallets for each supported currency
2. Update the `.env` file with your wallet addresses
3. Configure blockchain monitoring (optional)

## 📚 Usage

### Default Accounts

After seeding the database, you can use these default accounts:

#### Admin Account
- **Email**: <EMAIL>
- **Password**: password123
- **Role**: Super Admin

#### Mentor Accounts
- **Email**: <EMAIL> (E-commerce Expert)
- **Email**: <EMAIL> (Copywriting Expert)
- **Email**: <EMAIL> (Crypto Expert)
- **Password**: password123 (for all mentors)

#### Student Account
- **Email**: <EMAIL>
- **Password**: password123

### Admin Panel

Access the admin panel at `/admin` with admin credentials:

- **Dashboard** - Overview of platform statistics
- **User Management** - Manage users, roles, and permissions
- **Course Management** - Create and manage courses and lessons
- **Subscription Management** - Handle subscription plans and billing
- **Payment Management** - Monitor payments and transactions
- **Analytics** - Detailed platform analytics and reports

### Creating Courses

1. Login as admin or mentor
2. Navigate to Admin Panel > Courses
3. Click "Add New Course"
4. Fill in course details:
   - Title and description
   - Category and difficulty level
   - Thumbnail image
   - Course content
5. Add lessons with videos and resources
6. Publish the course

### Managing Subscriptions

1. Navigate to Admin Panel > Subscriptions
2. Create subscription plans with different features
3. Set pricing for monthly and yearly billing
4. Configure access levels and permissions
5. Monitor subscriber analytics

## 🔌 API Documentation

The Real World LMS provides a comprehensive RESTful API for mobile app integration and third-party services.

### Authentication

All API requests require authentication using Laravel Sanctum tokens:

```bash
# Login to get token
POST /api/auth/login
{
    "email": "<EMAIL>",
    "password": "password"
}

# Use token in subsequent requests
Authorization: Bearer {your-token}
```

### Endpoints

#### Courses
```bash
GET /api/courses                    # List all courses
GET /api/courses/{id}              # Get course details
POST /api/courses/{id}/enroll      # Enroll in course
```

#### Lessons
```bash
GET /api/lessons/{id}              # Get lesson details
POST /api/lessons/{id}/progress    # Update lesson progress
POST /api/lessons/{id}/complete    # Mark lesson as complete
```

#### User Progress
```bash
GET /api/progress                  # Get user's overall progress
GET /api/progress/course/{id}      # Get progress for specific course
```

#### Subscriptions
```bash
GET /api/subscription              # Get current subscription
POST /api/subscriptions/subscribe # Create new subscription
POST /api/subscriptions/cancel    # Cancel subscription
```

### API Rate Limiting

- **Rate Limit**: 60 requests per minute per user
- **Pagination**: 50 items per page (configurable)
- **Response Format**: JSON with consistent structure

## 🧪 Testing

### Running Tests

```bash
# Run all tests
php artisan test

# Run specific test suite
php artisan test --testsuite=Feature

# Run with coverage
php artisan test --coverage

# Run specific test
php artisan test tests/Feature/CourseEnrollmentTest.php
```

### Test Database

Tests use a separate SQLite database for isolation:

```bash
# Create test database
touch database/testing.sqlite

# Run migrations for testing
php artisan migrate --env=testing
```

### Writing Tests

Example test structure:

```php
<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Course;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class CourseTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_can_enroll_in_course()
    {
        $user = User::factory()->create();
        $course = Course::factory()->create(['is_free' => true]);

        $response = $this->actingAs($user)
            ->post("/courses/{$course->id}/enroll");

        $response->assertStatus(200);
        $this->assertTrue($user->isEnrolledIn($course));
    }
}
```

## 🚀 Deployment

### Production Setup

#### 1. Server Requirements
- PHP 8.1+ with required extensions
- MySQL 8.0+
- Redis
- SSL certificate
- Cron jobs support

#### 2. Environment Configuration

```bash
# Set production environment
APP_ENV=production
APP_DEBUG=false

# Configure database
DB_CONNECTION=mysql
DB_HOST=your_db_host
DB_DATABASE=your_db_name
DB_USERNAME=your_db_user
DB_PASSWORD=your_secure_password

# Configure cache and sessions
CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis

# Configure mail
MAIL_MAILER=smtp
# ... your production mail settings
```

#### 3. Deployment Commands

```bash
# Install dependencies
composer install --optimize-autoloader --no-dev

# Cache configuration
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Run migrations
php artisan migrate --force

# Build assets
npm run build

# Set permissions
chmod -R 755 storage bootstrap/cache
```

#### 4. Cron Jobs

Add to your server's crontab:

```bash
* * * * * cd /path-to-your-project && php artisan schedule:run >> /dev/null 2>&1
```

#### 5. Queue Workers

Set up supervisor for queue workers:

```ini
[program:therealworld-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /path-to-your-project/artisan queue:work redis --sleep=3 --tries=3
autostart=true
autorestart=true
user=www-data
numprocs=8
redirect_stderr=true
stdout_logfile=/path-to-your-project/storage/logs/worker.log
```

### Hostinger Deployment

For deployment on Hostinger Business hosting:

1. **Upload Files**: Upload all files to the public_html directory
2. **Database**: Create MySQL database through Hostinger panel
3. **Environment**: Configure `.env` file with Hostinger database credentials
4. **SSL**: Enable SSL certificate through Hostinger panel
5. **Cron Jobs**: Set up cron jobs through Hostinger control panel

## 🔧 Maintenance

### Regular Tasks

```bash
# Process crypto payments (every 10 minutes)
php artisan payments:process-crypto

# Generate certificates (daily)
php artisan certificates:generate

# Clean up old notifications (monthly)
php artisan notifications:cleanup

# Backup database (daily)
php artisan backup:run
```

### Monitoring

- **Error Logging**: Check `storage/logs/laravel.log`
- **Performance**: Monitor slow queries and response times
- **Security**: Regular security audits and updates
- **Backups**: Automated daily backups with 30-day retention

## 🤝 Contributing

We welcome contributions to The Real World LMS! Please follow these guidelines:

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines

- Follow PSR-12 coding standards
- Write comprehensive tests for new features
- Update documentation for any changes
- Use meaningful commit messages
- Ensure all tests pass before submitting PR

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:

- **Email**: <EMAIL>
- **Documentation**: [docs.therealworld.com](https://docs.therealworld.com)
- **Issues**: [GitHub Issues](https://github.com/yourusername/therealworld-lms/issues)
- **Discord**: [Join our Discord](https://discord.gg/therealworld)

## 🙏 Acknowledgments

- Laravel Framework and community
- The Real World platform for inspiration
- All contributors and testers
- Open source packages used in this project

---

<p align="center">
  Made with ❤️ for The Real World community<br>
  <strong>Escape The Matrix. Build Wealth. Achieve Financial Freedom.</strong>
</p>
