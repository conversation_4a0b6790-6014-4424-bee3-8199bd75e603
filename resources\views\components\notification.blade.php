@props([
    'type' => 'info',
    'title' => null,
    'dismissible' => true,
    'autoHide' => true,
    'duration' => 5000
])

@php
$typeClasses = [
    'success' => 'bg-green-50 border-green-400 text-green-700',
    'error' => 'bg-red-50 border-red-400 text-red-700',
    'warning' => 'bg-yellow-50 border-yellow-400 text-yellow-700',
    'info' => 'bg-blue-50 border-blue-400 text-blue-700',
];

$iconPaths = [
    'success' => 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z',
    'error' => 'M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z',
    'warning' => 'M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z',
    'info' => 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z',
];

$iconColors = [
    'success' => 'text-green-400',
    'error' => 'text-red-400',
    'warning' => 'text-yellow-400',
    'info' => 'text-blue-400',
];

$classes = 'notification rounded-md p-4 border-l-4 ' . $typeClasses[$type];
@endphp

<div 
    x-data="{ 
        show: true,
        autoHide: @js($autoHide),
        duration: @js($duration),
        init() {
            if (this.autoHide) {
                setTimeout(() => {
                    this.hide();
                }, this.duration);
            }
        },
        hide() {
            this.show = false;
            setTimeout(() => {
                this.$el.remove();
            }, 300);
        }
    }"
    x-show="show"
    x-transition:enter="transition ease-out duration-300"
    x-transition:enter-start="opacity-0 transform translate-x-full"
    x-transition:enter-end="opacity-100 transform translate-x-0"
    x-transition:leave="transition ease-in duration-300"
    x-transition:leave-start="opacity-100 transform translate-x-0"
    x-transition:leave-end="opacity-0 transform translate-x-full"
    class="{{ $classes }}"
    {{ $attributes }}
>
    <div class="flex">
        <div class="flex-shrink-0">
            <svg class="h-5 w-5 {{ $iconColors[$type] }}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="{{ $iconPaths[$type] }}" />
            </svg>
        </div>
        <div class="ml-3 flex-1">
            @if($title)
                <h3 class="text-sm font-medium">{{ $title }}</h3>
                <div class="mt-2 text-sm">
                    {{ $slot }}
                </div>
            @else
                <p class="text-sm">{{ $slot }}</p>
            @endif
        </div>
        @if($dismissible)
            <div class="ml-auto pl-3">
                <div class="-mx-1.5 -my-1.5">
                    <button
                        @click="hide()"
                        type="button"
                        class="inline-flex rounded-md p-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200 {{ $type === 'success' ? 'text-green-500 hover:bg-green-100 focus:ring-green-600' : ($type === 'error' ? 'text-red-500 hover:bg-red-100 focus:ring-red-600' : ($type === 'warning' ? 'text-yellow-500 hover:bg-yellow-100 focus:ring-yellow-600' : 'text-blue-500 hover:bg-blue-100 focus:ring-blue-600')) }}"
                    >
                        <span class="sr-only">Dismiss</span>
                        <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                    </button>
                </div>
            </div>
        @endif
    </div>
</div>
