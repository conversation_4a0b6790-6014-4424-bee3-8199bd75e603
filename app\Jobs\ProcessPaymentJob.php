<?php

namespace App\Jobs;

use App\Models\Payment;
use App\Models\User;
use App\Models\UserSubscription;
use App\Notifications\PaymentConfirmationNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessPaymentJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $paymentData;
    protected $user;

    /**
     * Create a new job instance.
     */
    public function __construct(array $paymentData, User $user)
    {
        $this->paymentData = $paymentData;
        $this->user = $user;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            // Create payment record
            $payment = Payment::create([
                'user_id' => $this->user->id,
                'subscription_plan_id' => $this->paymentData['subscription_plan_id'],
                'amount' => $this->paymentData['amount'],
                'currency' => $this->paymentData['currency'],
                'payment_method' => $this->paymentData['payment_method'],
                'transaction_id' => $this->paymentData['transaction_id'],
                'status' => 'completed',
                'payment_date' => now(),
                'metadata' => json_encode($this->paymentData['metadata'] ?? []),
            ]);

            // Create or update subscription
            $subscription = UserSubscription::updateOrCreate(
                ['user_id' => $this->user->id],
                [
                    'subscription_plan_id' => $this->paymentData['subscription_plan_id'],
                    'status' => 'active',
                    'current_period_start' => now(),
                    'current_period_end' => $this->calculatePeriodEnd($this->paymentData['billing_cycle']),
                    'billing_cycle' => $this->paymentData['billing_cycle'],
                    'amount' => $this->paymentData['amount'],
                    'currency' => $this->paymentData['currency'],
                ]
            );

            // Send confirmation notification
            $this->user->notify(new PaymentConfirmationNotification($payment, $subscription));

            Log::info('Payment processed successfully', [
                'user_id' => $this->user->id,
                'payment_id' => $payment->id,
                'amount' => $this->paymentData['amount'],
            ]);

        } catch (\Exception $e) {
            Log::error('Payment processing failed', [
                'user_id' => $this->user->id,
                'error' => $e->getMessage(),
                'payment_data' => $this->paymentData,
            ]);

            // Update payment status to failed
            if (isset($payment)) {
                $payment->update(['status' => 'failed']);
            }

            throw $e;
        }
    }

    /**
     * Calculate subscription period end date.
     */
    private function calculatePeriodEnd(string $billingCycle): \Carbon\Carbon
    {
        return match($billingCycle) {
            'monthly' => now()->addMonth(),
            'yearly' => now()->addYear(),
            default => now()->addMonth(),
        };
    }
}
