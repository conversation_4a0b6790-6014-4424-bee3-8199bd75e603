<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Production Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains production-specific configuration settings that
    | optimize the application for performance, security, and reliability.
    |
    */

    'optimizations' => [
        'opcache' => [
            'enabled' => env('OPCACHE_ENABLE', true),
            'memory_consumption' => 256,
            'interned_strings_buffer' => 16,
            'max_accelerated_files' => 20000,
            'revalidate_freq' => 0,
            'validate_timestamps' => false,
        ],

        'session' => [
            'driver' => 'redis',
            'lifetime' => 120,
            'expire_on_close' => false,
            'encrypt' => true,
            'files' => storage_path('framework/sessions'),
            'connection' => 'session',
            'table' => 'sessions',
            'store' => null,
            'lottery' => [2, 100],
            'cookie' => env('SESSION_COOKIE', 'laravel_session'),
            'path' => '/',
            'domain' => env('SESSION_DOMAIN', null),
            'secure' => env('SESSION_SECURE_COOKIE', true),
            'http_only' => true,
            'same_site' => 'strict',
        ],

        'cache' => [
            'default' => 'redis',
            'stores' => [
                'redis' => [
                    'driver' => 'redis',
                    'connection' => 'cache',
                    'lock_connection' => 'default',
                ],
            ],
            'prefix' => env('CACHE_PREFIX', 'trw_cache'),
        ],

        'queue' => [
            'default' => 'redis',
            'connections' => [
                'redis' => [
                    'driver' => 'redis',
                    'connection' => 'default',
                    'queue' => env('REDIS_QUEUE', 'default'),
                    'retry_after' => 90,
                    'block_for' => null,
                    'after_commit' => false,
                ],
            ],
        ],
    ],

    'security' => [
        'headers' => [
            'content_security_policy' => [
                "default-src 'self'",
                "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://unpkg.com https://js.stripe.com",
                "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.jsdelivr.net",
                "font-src 'self' https://fonts.gstatic.com",
                "img-src 'self' data: https: blob:",
                "connect-src 'self' https: wss:",
                "media-src 'self'",
                "object-src 'none'",
                "frame-src 'self' https://js.stripe.com https://hooks.stripe.com",
                "base-uri 'self'",
                "form-action 'self'",
                "frame-ancestors 'none'",
                "upgrade-insecure-requests"
            ],
            'strict_transport_security' => 'max-age=31536000; includeSubDomains; preload',
            'x_content_type_options' => 'nosniff',
            'x_frame_options' => 'DENY',
            'x_xss_protection' => '1; mode=block',
            'referrer_policy' => 'strict-origin-when-cross-origin',
            'permissions_policy' => 'geolocation=(), microphone=(), camera=()',
        ],

        'rate_limiting' => [
            'login' => [
                'max_attempts' => 5,
                'decay_minutes' => 15,
            ],
            'api' => [
                'max_attempts' => 60,
                'decay_minutes' => 1,
            ],
            'global' => [
                'max_attempts' => 1000,
                'decay_minutes' => 1,
            ],
        ],

        'two_factor' => [
            'enabled' => env('GOOGLE_2FA_ENABLED', true),
            'window' => 4,
            'qr_code_size' => 200,
        ],
    ],

    'performance' => [
        'database' => [
            'connections' => [
                'mysql' => [
                    'options' => [
                        PDO::ATTR_EMULATE_PREPARES => false,
                        PDO::ATTR_STRINGIFY_FETCHES => false,
                        PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true,
                        PDO::MYSQL_ATTR_SSL_VERIFY_SERVER_CERT => false,
                    ],
                    'pool' => [
                        'min_connections' => 5,
                        'max_connections' => 20,
                    ],
                ],
            ],
        ],

        'redis' => [
            'client' => env('REDIS_CLIENT', 'phpredis'),
            'options' => [
                'cluster' => env('REDIS_CLUSTER', 'redis'),
                'prefix' => env('REDIS_PREFIX', 'trw_database_'),
            ],
            'connections' => [
                'default' => [
                    'url' => env('REDIS_URL'),
                    'host' => env('REDIS_HOST', '127.0.0.1'),
                    'password' => env('REDIS_PASSWORD', null),
                    'port' => env('REDIS_PORT', '6379'),
                    'database' => env('REDIS_DB', '0'),
                    'read_write_timeout' => 60,
                    'persistent' => true,
                ],
                'cache' => [
                    'url' => env('REDIS_URL'),
                    'host' => env('REDIS_HOST', '127.0.0.1'),
                    'password' => env('REDIS_PASSWORD', null),
                    'port' => env('REDIS_PORT', '6379'),
                    'database' => env('REDIS_CACHE_DB', '1'),
                    'read_write_timeout' => 60,
                    'persistent' => true,
                ],
                'session' => [
                    'url' => env('REDIS_URL'),
                    'host' => env('REDIS_HOST', '127.0.0.1'),
                    'password' => env('REDIS_PASSWORD', null),
                    'port' => env('REDIS_PORT', '6379'),
                    'database' => env('REDIS_SESSION_DB', '2'),
                    'read_write_timeout' => 60,
                    'persistent' => true,
                ],
            ],
        ],

        'file_storage' => [
            'default' => 's3',
            'disks' => [
                's3' => [
                    'driver' => 's3',
                    'key' => env('AWS_ACCESS_KEY_ID'),
                    'secret' => env('AWS_SECRET_ACCESS_KEY'),
                    'region' => env('AWS_DEFAULT_REGION'),
                    'bucket' => env('AWS_BUCKET'),
                    'url' => env('AWS_URL'),
                    'endpoint' => env('AWS_ENDPOINT'),
                    'use_path_style_endpoint' => env('AWS_USE_PATH_STYLE_ENDPOINT', false),
                    'options' => [
                        'CacheControl' => 'max-age=31536000',
                        'Expires' => gmdate('D, d M Y H:i:s T', strtotime('+1 year')),
                    ],
                ],
            ],
        ],
    ],

    'monitoring' => [
        'logging' => [
            'channels' => [
                'stack' => [
                    'driver' => 'stack',
                    'channels' => ['daily', 'sentry'],
                    'ignore_exceptions' => false,
                ],
                'daily' => [
                    'driver' => 'daily',
                    'path' => storage_path('logs/laravel.log'),
                    'level' => env('LOG_LEVEL', 'error'),
                    'days' => 14,
                    'replace_placeholders' => true,
                ],
                'sentry' => [
                    'driver' => 'sentry',
                    'level' => 'error',
                ],
            ],
        ],

        'health_checks' => [
            'database' => true,
            'redis' => true,
            'storage' => true,
            'queue' => true,
        ],

        'metrics' => [
            'enabled' => true,
            'endpoints' => [
                'response_time' => true,
                'memory_usage' => true,
                'database_queries' => true,
                'cache_hits' => true,
            ],
        ],
    ],

    'backup' => [
        'enabled' => true,
        'schedule' => '0 2 * * *', // Daily at 2 AM
        'destinations' => [
            's3' => [
                'disk' => 's3',
                'path' => 'backups',
                'retention' => 30, // days
            ],
        ],
        'include' => [
            'database' => true,
            'files' => [
                'storage/app',
                'public/uploads',
            ],
        ],
        'notifications' => [
            'mail' => [
                'to' => env('BACKUP_NOTIFICATION_MAIL_TO'),
                'on_success' => false,
                'on_failure' => true,
            ],
        ],
    ],

    'maintenance' => [
        'allowed_ips' => [
            // Add your IP addresses here
        ],
        'secret' => env('MAINTENANCE_SECRET'),
        'template' => 'maintenance',
    ],

    'cdn' => [
        'enabled' => env('CDN_ENABLED', false),
        'url' => env('CDN_URL'),
        'assets' => [
            'css' => true,
            'js' => true,
            'images' => true,
            'fonts' => true,
        ],
    ],
];
