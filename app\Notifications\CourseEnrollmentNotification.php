<?php

namespace App\Notifications;

use App\Models\Course;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class CourseEnrollmentNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $course;

    /**
     * Create a new notification instance.
     */
    public function __construct(Course $course)
    {
        $this->course = $course;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('Course Enrollment Confirmed - ' . $this->course->title)
            ->greeting('Hello ' . $notifiable->first_name . '!')
            ->line('You have successfully enrolled in: ' . $this->course->title)
            ->line('Course Description: ' . $this->course->description)
            ->line('Instructor: ' . $this->course->mentor->name)
            ->line('Total Lessons: ' . $this->course->lessons->count())
            ->action('Start Learning', route('courses.show', $this->course))
            ->line('Get ready to level up your skills and achieve your goals!')
            ->salutation('Happy Learning,')
            ->salutation('The Real World Team');
    }

    /**
     * Get the database representation of the notification.
     */
    public function toDatabase(object $notifiable): array
    {
        return [
            'title' => 'Course Enrollment Confirmed',
            'message' => 'You have successfully enrolled in: ' . $this->course->title,
            'action_url' => route('courses.show', $this->course),
            'action_text' => 'Start Learning',
            'type' => 'course_enrollment',
            'course_id' => $this->course->id,
        ];
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return $this->toDatabase($notifiable);
    }
}
