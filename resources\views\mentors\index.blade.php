@extends('layouts.app')

@section('title', 'Meet Our Mentors')

@section('content')
<div class="mentors-container">
    <!-- Mentors Hero -->
    <div class="mentors-hero">
        <div class="container">
            <div class="row justify-content-center text-center">
                <div class="col-lg-8">
                    <h1 class="hero-title">Meet Your Mentors</h1>
                    <p class="hero-subtitle">
                        Learn from successful entrepreneurs and industry experts who have built multi-million dollar businesses
                    </p>
                    <div class="hero-stats">
                        <div class="stat-item">
                            <div class="stat-number">{{ $stats['total_mentors'] }}</div>
                            <div class="stat-label">Expert Mentors</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">${{ number_format($stats['combined_revenue']) }}M+</div>
                            <div class="stat-label">Combined Revenue</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">{{ $stats['total_students'] }}K+</div>
                            <div class="stat-label">Students Taught</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="mentors-content">
        <div class="container">
            <!-- Filter Tabs -->
            <div class="mentors-filters">
                <div class="filter-tabs">
                    <button class="filter-tab {{ !request('category') ? 'active' : '' }}" 
                            onclick="filterMentors('')">
                        All Mentors
                    </button>
                    @foreach($categories as $category)
                    <button class="filter-tab {{ request('category') === $category->slug ? 'active' : '' }}" 
                            onclick="filterMentors('{{ $category->slug }}')">
                        {{ $category->name }}
                    </button>
                    @endforeach
                </div>
                
                <div class="search-filter">
                    <form action="{{ route('mentors.index') }}" method="GET" class="search-form">
                        <input type="hidden" name="category" value="{{ request('category') }}">
                        <div class="input-group">
                            <input type="text" name="search" class="form-control" 
                                   placeholder="Search mentors..." value="{{ request('search') }}">
                            <div class="input-group-append">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Mentors Grid -->
            <div class="mentors-grid">
                @foreach($mentors as $mentor)
                <div class="mentor-card">
                    <div class="mentor-image">
                        <img src="{{ $mentor->avatar ? asset('storage/' . $mentor->avatar) : asset('images/default-avatar.png') }}" 
                             alt="{{ $mentor->name }}">
                        <div class="mentor-overlay">
                            <div class="mentor-social">
                                @if($mentor->social_links['twitter'])
                                <a href="{{ $mentor->social_links['twitter'] }}" target="_blank" class="social-link">
                                    <i class="fab fa-twitter"></i>
                                </a>
                                @endif
                                @if($mentor->social_links['instagram'])
                                <a href="{{ $mentor->social_links['instagram'] }}" target="_blank" class="social-link">
                                    <i class="fab fa-instagram"></i>
                                </a>
                                @endif
                                @if($mentor->social_links['linkedin'])
                                <a href="{{ $mentor->social_links['linkedin'] }}" target="_blank" class="social-link">
                                    <i class="fab fa-linkedin"></i>
                                </a>
                                @endif
                            </div>
                        </div>
                        @if($mentor->is_featured)
                        <div class="featured-badge">
                            <i class="fas fa-star"></i> Featured
                        </div>
                        @endif
                    </div>
                    
                    <div class="mentor-content">
                        <div class="mentor-header">
                            <h3 class="mentor-name">{{ $mentor->name }}</h3>
                            <p class="mentor-title">{{ $mentor->title }}</p>
                            <div class="mentor-specialties">
                                @foreach($mentor->specialties as $specialty)
                                <span class="specialty-tag">{{ $specialty }}</span>
                                @endforeach
                            </div>
                        </div>
                        
                        <div class="mentor-bio">
                            <p>{{ Str::limit($mentor->bio, 150) }}</p>
                        </div>
                        
                        <div class="mentor-achievements">
                            <div class="achievement-item">
                                <div class="achievement-icon">
                                    <i class="fas fa-dollar-sign"></i>
                                </div>
                                <div class="achievement-text">
                                    <span class="achievement-value">${{ number_format($mentor->revenue) }}M+</span>
                                    <span class="achievement-label">Revenue Generated</span>
                                </div>
                            </div>
                            
                            <div class="achievement-item">
                                <div class="achievement-icon">
                                    <i class="fas fa-graduation-cap"></i>
                                </div>
                                <div class="achievement-text">
                                    <span class="achievement-value">{{ number_format($mentor->students_count) }}+</span>
                                    <span class="achievement-label">Students Taught</span>
                                </div>
                            </div>
                            
                            <div class="achievement-item">
                                <div class="achievement-icon">
                                    <i class="fas fa-book"></i>
                                </div>
                                <div class="achievement-text">
                                    <span class="achievement-value">{{ $mentor->courses_count }}</span>
                                    <span class="achievement-label">Courses</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mentor-rating">
                            <div class="rating-stars">
                                @for($i = 1; $i <= 5; $i++)
                                    <i class="fas fa-star {{ $i <= $mentor->average_rating ? 'active' : '' }}"></i>
                                @endfor
                            </div>
                            <span class="rating-text">{{ number_format($mentor->average_rating, 1) }}</span>
                            <span class="rating-count">({{ $mentor->reviews_count }} reviews)</span>
                        </div>
                        
                        <div class="mentor-actions">
                            <a href="{{ route('mentors.show', $mentor) }}" class="btn btn-primary btn-block">
                                <i class="fas fa-user"></i> View Profile
                            </a>
                            <div class="action-buttons">
                                <a href="{{ route('courses.index', ['mentor' => $mentor->id]) }}" class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-book"></i> Courses
                                </a>
                                @auth
                                <button class="btn btn-outline-secondary btn-sm" onclick="followMentor({{ $mentor->id }})">
                                    <i class="fas fa-{{ $mentor->is_followed ? 'heart' : 'heart' }}"></i>
                                    {{ $mentor->is_followed ? 'Following' : 'Follow' }}
                                </button>
                                @endauth
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
            
            <!-- Pagination -->
            @if($mentors->hasPages())
            <div class="pagination-wrapper">
                {{ $mentors->appends(request()->query())->links() }}
            </div>
            @endif
        </div>
    </div>

    <!-- Become a Mentor CTA -->
    <div class="become-mentor-section">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8 text-center">
                    <div class="cta-card">
                        <div class="cta-icon">
                            <i class="fas fa-chalkboard-teacher"></i>
                        </div>
                        <h2>Become a Mentor</h2>
                        <p>Share your expertise and help others achieve success. Join our elite group of mentors and make a real impact.</p>
                        <div class="cta-benefits">
                            <div class="benefit-item">
                                <i class="fas fa-check"></i>
                                <span>Earn revenue from your courses</span>
                            </div>
                            <div class="benefit-item">
                                <i class="fas fa-check"></i>
                                <span>Build your personal brand</span>
                            </div>
                            <div class="benefit-item">
                                <i class="fas fa-check"></i>
                                <span>Impact thousands of students</span>
                            </div>
                        </div>
                        <a href="{{ route('mentors.apply') }}" class="btn btn-success btn-lg">
                            <i class="fas fa-paper-plane"></i> Apply to Become a Mentor
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.mentors-container {
    background: #f8f9fc;
    min-height: 100vh;
}

.mentors-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4rem 0;
    text-align: center;
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.hero-subtitle {
    font-size: 1.25rem;
    opacity: 0.9;
    margin-bottom: 3rem;
    line-height: 1.6;
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: #ffd700;
}

.stat-label {
    font-size: 1rem;
    opacity: 0.9;
}

.mentors-content {
    padding: 3rem 0;
}

.mentors-filters {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 3rem;
    background: white;
    padding: 1.5rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.filter-tabs {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.filter-tab {
    background: #f7fafc;
    border: 1px solid #e2e8f0;
    color: #4a5568;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-tab:hover,
.filter-tab.active {
    background: #667eea;
    border-color: #667eea;
    color: white;
}

.search-filter {
    min-width: 300px;
}

.mentors-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.mentor-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.mentor-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.mentor-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.mentor-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.mentor-card:hover .mentor-image img {
    transform: scale(1.05);
}

.mentor-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.mentor-card:hover .mentor-overlay {
    opacity: 1;
}

.mentor-social {
    display: flex;
    gap: 1rem;
}

.social-link {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-link:hover {
    background: rgba(255, 255, 255, 0.3);
    color: white;
    text-decoration: none;
    transform: scale(1.1);
}

.featured-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: linear-gradient(135deg, #f6c23e 0%, #f4b942 100%);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.875rem;
}

.mentor-content {
    padding: 2rem;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.mentor-header {
    text-align: center;
    margin-bottom: 1.5rem;
}

.mentor-name {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.mentor-title {
    color: #667eea;
    font-weight: 600;
    margin-bottom: 1rem;
}

.mentor-specialties {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.specialty-tag {
    background: #e2e8f0;
    color: #4a5568;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 500;
}

.mentor-bio {
    margin-bottom: 1.5rem;
    flex: 1;
}

.mentor-bio p {
    color: #718096;
    line-height: 1.6;
}

.mentor-achievements {
    margin-bottom: 1.5rem;
}

.achievement-item {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.achievement-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    flex-shrink: 0;
}

.achievement-text {
    display: flex;
    flex-direction: column;
}

.achievement-value {
    font-weight: 700;
    color: #2d3748;
    font-size: 1.1rem;
}

.achievement-label {
    color: #718096;
    font-size: 0.875rem;
}

.mentor-rating {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.rating-stars {
    display: flex;
    gap: 0.25rem;
}

.rating-stars i {
    color: #e2e8f0;
    font-size: 1rem;
}

.rating-stars i.active {
    color: #ffd700;
}

.rating-text {
    font-weight: 600;
    color: #2d3748;
}

.rating-count {
    color: #718096;
    font-size: 0.875rem;
}

.mentor-actions {
    margin-top: auto;
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.become-mentor-section {
    background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
    color: white;
    padding: 4rem 0;
}

.cta-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 3rem;
    backdrop-filter: blur(10px);
}

.cta-icon {
    font-size: 4rem;
    margin-bottom: 2rem;
    color: #ffd700;
}

.cta-card h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.cta-card p {
    font-size: 1.25rem;
    opacity: 0.9;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.cta-benefits {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.benefit-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.benefit-item i {
    color: #48bb78;
    font-size: 1.25rem;
}

@media (max-width: 768px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-stats {
        flex-direction: column;
        gap: 2rem;
    }
    
    .mentors-filters {
        flex-direction: column;
        gap: 1rem;
    }
    
    .search-filter {
        min-width: auto;
        width: 100%;
    }
    
    .mentors-grid {
        grid-template-columns: 1fr;
    }
    
    .cta-benefits {
        flex-direction: column;
        gap: 1rem;
    }
    
    .cta-card {
        padding: 2rem;
    }
    
    .cta-card h2 {
        font-size: 2rem;
    }
}
</style>
@endpush

@push('scripts')
<script>
function filterMentors(category) {
    const url = new URL(window.location);
    if (category) {
        url.searchParams.set('category', category);
    } else {
        url.searchParams.delete('category');
    }
    window.location.href = url.toString();
}

function followMentor(mentorId) {
    fetch(`/mentors/${mentorId}/follow`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const button = event.target.closest('button');
            const icon = button.querySelector('i');
            const text = button.childNodes[button.childNodes.length - 1];
            
            if (data.following) {
                icon.className = 'fas fa-heart';
                text.textContent = ' Following';
                button.classList.add('btn-success');
                button.classList.remove('btn-outline-secondary');
            } else {
                icon.className = 'fas fa-heart';
                text.textContent = ' Follow';
                button.classList.remove('btn-success');
                button.classList.add('btn-outline-secondary');
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Please login to follow mentors');
    });
}

// Smooth scroll animations
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.style.opacity = '1';
            entry.target.style.transform = 'translateY(0)';
        }
    });
}, observerOptions);

document.addEventListener('DOMContentLoaded', function() {
    const mentorCards = document.querySelectorAll('.mentor-card');
    mentorCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = `opacity 0.6s ease ${index * 0.1}s, transform 0.6s ease ${index * 0.1}s`;
        observer.observe(card);
    });
});
</script>
@endpush
