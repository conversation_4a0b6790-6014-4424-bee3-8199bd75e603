<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Course;
use App\Models\Lesson;
use App\Models\UserProgress;
use Illuminate\Database\Seeder;

class UserProgressSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $users = User::whereHas('roles', function($q) {
            $q->where('name', 'user');
        })->whereHas('userSubscriptions', function($q) {
            $q->where('status', 'active');
        })->get();

        $courses = Course::with('lessons')->get();

        foreach ($users as $user) {
            // Each user enrolls in 2-5 random courses
            $userCourses = $courses->random(rand(2, 5));

            foreach ($userCourses as $course) {
                $this->createProgressForUserCourse($user, $course);
            }
        }

        $this->command->info('User progress seeded successfully!');
    }

    private function createProgressForUserCourse(User $user, Course $course)
    {
        $lessons = $course->lessons;
        $completionRate = rand(0, 100);

        // Determine how many lessons to complete based on completion rate
        $lessonsToComplete = intval($lessons->count() * ($completionRate / 100));

        foreach ($lessons as $index => $lesson) {
            $isCompleted = $index < $lessonsToComplete;
            $startedAt = now()->subDays(rand(1, 30));

            UserProgress::firstOrCreate(
                [
                    'user_id' => $user->id,
                    'lesson_id' => $lesson->id,
                ],
                [
                    'course_id' => $course->id,
                    'is_completed' => $isCompleted,
                    'completed_at' => $isCompleted ? $startedAt->addMinutes($lesson->duration_minutes + rand(5, 30)) : null,
                    'started_at' => $startedAt,
                    'watch_time_seconds' => $isCompleted ? ($lesson->duration_minutes + rand(5, 30)) * 60 : rand(5, $lesson->duration_minutes) * 60,
                    'completion_percentage' => $isCompleted ? 100 : rand(10, 90),
                ]
            );
        }

        // Note: We don't create course-level progress as the table requires lesson_id
    }
}
