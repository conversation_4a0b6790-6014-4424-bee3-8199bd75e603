@extends('layouts.app')

@section('content')
<div class="py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2">
                <!-- Course Header -->
                <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                    <div class="flex items-center mb-4">
                        <span class="bg-blue-100 text-blue-800 text-sm px-3 py-1 rounded-full mr-3">{{ $course->category }}</span>
                        <span class="text-sm text-gray-500 capitalize">{{ $course->difficulty }}</span>
                        @if($course->is_featured)
                            <span class="bg-yellow-100 text-yellow-800 text-sm px-3 py-1 rounded-full ml-3">Featured</span>
                        @endif
                    </div>
                    
                    <h1 class="text-3xl font-bold text-gray-900 mb-4">{{ $course->title }}</h1>
                    
                    <div class="flex items-center mb-4">
                        @if($course->mentor->avatar)
                            <img src="{{ $course->mentor->avatar }}" alt="{{ $course->mentor->name }}" class="w-10 h-10 rounded-full mr-3">
                        @else
                            <div class="w-10 h-10 bg-gray-300 rounded-full mr-3 flex items-center justify-center">
                                <span class="text-sm text-gray-600">{{ substr($course->mentor->name, 0, 1) }}</span>
                            </div>
                        @endif
                        <div>
                            <p class="font-medium text-gray-900">{{ $course->mentor->name }}</p>
                            <p class="text-sm text-gray-600">Course Mentor</p>
                        </div>
                    </div>
                    
                    <div class="flex items-center space-x-6 text-sm text-gray-600 mb-6">
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            {{ $course->duration_hours }} hours
                        </div>
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                            </svg>
                            {{ $course->publishedLessons->count() }} lessons
                        </div>
                        @auth
                            @if($completionPercentage > 0)
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 mr-1 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    {{ number_format($completionPercentage, 1) }}% complete
                                </div>
                            @endif
                        @endauth
                    </div>
                    
                    <p class="text-gray-700 mb-6">{{ $course->description }}</p>
                    
                    <!-- Access Status -->
                    @auth
                        @if($canAccess)
                            <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    <span class="text-green-800 font-medium">You have access to this course</span>
                                </div>
                            </div>
                        @else
                            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-yellow-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                    </svg>
                                    <span class="text-yellow-800 font-medium">Subscription required to access this course</span>
                                </div>
                                <p class="text-yellow-700 text-sm mt-2">
                                    This course is available with: 
                                    @if($course->required_plans)
                                        {{ implode(', ', array_map('ucfirst', $course->required_plans)) }} plans
                                    @else
                                        any active subscription
                                    @endif
                                </p>
                            </div>
                        @endif
                    @else
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-blue-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span class="text-blue-800 font-medium">Sign in to access this course</span>
                            </div>
                        </div>
                    @endauth
                </div>
                
                <!-- Course Syllabus -->
                @if($course->syllabus)
                <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                    <h2 class="text-xl font-bold text-gray-900 mb-4">What You'll Learn</h2>
                    <div class="prose prose-sm max-w-none">
                        {!! nl2br(e($course->syllabus)) !!}
                    </div>
                </div>
                @endif
                
                <!-- Course Lessons -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-bold text-gray-900 mb-4">Course Content</h2>
                    
                    <div class="space-y-3">
                        @foreach($course->publishedLessons as $index => $lesson)
                        <div class="border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center flex-1">
                                    <div class="flex-shrink-0 w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center mr-3">
                                        @auth
                                            @if(isset($lessonProgress[$lesson->id]) && $lessonProgress[$lesson->id]['completed'])
                                                <svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                </svg>
                                            @else
                                                <span class="text-sm text-gray-600">{{ $index + 1 }}</span>
                                            @endif
                                        @else
                                            <span class="text-sm text-gray-600">{{ $index + 1 }}</span>
                                        @endauth
                                    </div>
                                    
                                    <div class="flex-1">
                                        <h3 class="font-medium text-gray-900">{{ $lesson->title }}</h3>
                                        <p class="text-sm text-gray-600">{{ $lesson->description }}</p>
                                        <div class="flex items-center mt-1 text-xs text-gray-500">
                                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                            {{ $lesson->formatted_duration }}
                                            @if($lesson->is_free)
                                                <span class="ml-2 bg-green-100 text-green-800 px-2 py-0.5 rounded text-xs">Free</span>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="flex-shrink-0">
                                    @auth
                                        @if($lesson->is_free || $canAccess)
                                            <a href="{{ route('lessons.show', [$course, $lesson]) }}" 
                                               class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm font-medium transition duration-300">
                                                @if(isset($lessonProgress[$lesson->id]) && $lessonProgress[$lesson->id]['completed'])
                                                    Review
                                                @else
                                                    Start
                                                @endif
                                            </a>
                                        @else
                                            <span class="text-gray-400 text-sm">🔒 Locked</span>
                                        @endif
                                    @else
                                        @if($lesson->is_free)
                                            <a href="{{ route('lessons.show', [$course, $lesson]) }}" 
                                               class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm font-medium transition duration-300">
                                                Preview
                                            </a>
                                        @else
                                            <span class="text-gray-400 text-sm">🔒 Locked</span>
                                        @endif
                                    @endauth
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
            
            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Course Preview -->
                <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
                    @if($course->video_preview)
                        <div class="aspect-video">
                            <iframe src="{{ $course->video_preview }}" class="w-full h-full" frameborder="0" allowfullscreen></iframe>
                        </div>
                    @elseif($course->thumbnail)
                        <img src="{{ $course->thumbnail }}" alt="{{ $course->title }}" class="w-full h-48 object-cover">
                    @else
                        <div class="w-full h-48 bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                            <span class="text-white text-4xl font-bold">{{ substr($course->title, 0, 1) }}</span>
                        </div>
                    @endif
                    
                    <div class="p-6">
                        @if($course->price > 0)
                            <div class="text-2xl font-bold text-green-600 mb-4">${{ number_format($course->price, 2) }}</div>
                        @endif
                        
                        @auth
                            @if($canAccess)
                                @if($completionPercentage > 0)
                                    <div class="mb-4">
                                        <div class="flex justify-between text-sm text-gray-600 mb-1">
                                            <span>Progress</span>
                                            <span>{{ number_format($completionPercentage, 1) }}%</span>
                                        </div>
                                        <div class="w-full bg-gray-200 rounded-full h-2">
                                            <div class="bg-blue-600 h-2 rounded-full" style="width: {{ $completionPercentage }}%"></div>
                                        </div>
                                    </div>
                                    <a href="{{ route('lessons.show', [$course, $course->publishedLessons->first()]) }}" 
                                       class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-4 rounded-lg text-center block transition duration-300">
                                        Continue Learning
                                    </a>
                                @else
                                    <form method="POST" action="{{ route('courses.enroll', $course) }}">
                                        @csrf
                                        <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-4 rounded-lg transition duration-300">
                                            Start Course
                                        </button>
                                    </form>
                                @endif
                            @else
                                <a href="{{ route('subscriptions.index') }}" 
                                   class="w-full bg-yellow-400 hover:bg-yellow-500 text-black font-bold py-3 px-4 rounded-lg text-center block transition duration-300">
                                    Get Access
                                </a>
                            @endif
                        @else
                            <a href="{{ route('register') }}" 
                               class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-4 rounded-lg text-center block transition duration-300">
                                Sign Up to Access
                            </a>
                        @endauth
                    </div>
                </div>
                
                <!-- Related Courses -->
                @if($relatedCourses->count() > 0)
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">Related Courses</h3>
                    <div class="space-y-4">
                        @foreach($relatedCourses as $relatedCourse)
                        <div class="flex items-center">
                            @if($relatedCourse->thumbnail)
                                <img src="{{ $relatedCourse->thumbnail }}" alt="{{ $relatedCourse->title }}" class="w-12 h-12 object-cover rounded mr-3">
                            @else
                                <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded mr-3 flex items-center justify-center">
                                    <span class="text-white text-sm font-bold">{{ substr($relatedCourse->title, 0, 1) }}</span>
                                </div>
                            @endif
                            <div class="flex-1">
                                <h4 class="font-medium text-sm">{{ $relatedCourse->title }}</h4>
                                <p class="text-xs text-gray-600">{{ $relatedCourse->mentor->name }}</p>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
