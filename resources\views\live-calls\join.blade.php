@extends('layouts.app')

@section('title', 'Join Live Call - ' . $liveCall->title)

@section('content')
<div class="live-call-container">
    <!-- Pre-call Setup -->
    <div class="pre-call-setup" id="preCallSetup">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="setup-card">
                        <div class="setup-header">
                            <div class="call-info">
                                <h1 class="call-title">{{ $liveCall->title }}</h1>
                                <div class="call-meta">
                                    <span class="call-mentor">
                                        <img src="{{ $liveCall->mentor->avatar ? asset('storage/' . $liveCall->mentor->avatar) : asset('images/default-avatar.png') }}"
                                             alt="{{ $liveCall->mentor->name }}" class="mentor-avatar">
                                        {{ $liveCall->mentor->name }}
                                    </span>
                                    <span class="call-time">
                                        <i class="fas fa-clock"></i>
                                        {{ $liveCall->scheduled_at->format('M d, Y \a\t g:i A') }}
                                    </span>
                                    <span class="call-duration">
                                        <i class="fas fa-hourglass-half"></i>
                                        {{ $liveCall->duration }} minutes
                                    </span>
                                </div>
                                <p class="call-description">{{ $liveCall->description }}</p>
                            </div>
                        </div>

                        <!-- Device Check -->
                        <div class="device-check">
                            <h3>Device Setup</h3>
                            <div class="device-tests">
                                <div class="device-test" id="cameraTest">
                                    <div class="test-icon">
                                        <i class="fas fa-video"></i>
                                    </div>
                                    <div class="test-content">
                                        <h4>Camera</h4>
                                        <p class="test-status">Testing...</p>
                                        <div class="camera-preview" id="cameraPreview">
                                            <video id="localVideo" autoplay muted></video>
                                        </div>
                                    </div>
                                    <div class="test-controls">
                                        <button class="btn btn-sm btn-outline-secondary" onclick="toggleCamera()">
                                            <i class="fas fa-video" id="cameraIcon"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="device-test" id="micTest">
                                    <div class="test-icon">
                                        <i class="fas fa-microphone"></i>
                                    </div>
                                    <div class="test-content">
                                        <h4>Microphone</h4>
                                        <p class="test-status">Testing...</p>
                                        <div class="audio-level">
                                            <div class="level-bar" id="audioLevelBar"></div>
                                        </div>
                                    </div>
                                    <div class="test-controls">
                                        <button class="btn btn-sm btn-outline-secondary" onclick="toggleMicrophone()">
                                            <i class="fas fa-microphone" id="micIcon"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="device-test" id="speakerTest">
                                    <div class="test-icon">
                                        <i class="fas fa-volume-up"></i>
                                    </div>
                                    <div class="test-content">
                                        <h4>Speakers</h4>
                                        <p class="test-status">Ready</p>
                                        <button class="btn btn-sm btn-primary" onclick="testSpeakers()">
                                            <i class="fas fa-play"></i> Test Sound
                                        </button>
                                    </div>
                                    <div class="test-controls">
                                        <div class="volume-control">
                                            <input type="range" id="volumeSlider" min="0" max="100" value="50">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Connection Status -->
                        <div class="connection-status">
                            <h3>Connection Status</h3>
                            <div class="status-indicators">
                                <div class="status-item">
                                    <div class="status-icon" id="internetStatus">
                                        <i class="fas fa-wifi"></i>
                                    </div>
                                    <div class="status-info">
                                        <span class="status-label">Internet Connection</span>
                                        <span class="status-value" id="internetSpeed">Testing...</span>
                                    </div>
                                </div>
                                <div class="status-item">
                                    <div class="status-icon" id="serverStatus">
                                        <i class="fas fa-server"></i>
                                    </div>
                                    <div class="status-info">
                                        <span class="status-label">Server Connection</span>
                                        <span class="status-value" id="serverLatency">Connecting...</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Join Options -->
                        <div class="join-options">
                            <div class="join-settings">
                                <div class="setting-item">
                                    <label class="setting-label">
                                        <input type="checkbox" id="joinWithVideo" checked>
                                        <span class="checkmark"></span>
                                        Join with camera on
                                    </label>
                                </div>
                                <div class="setting-item">
                                    <label class="setting-label">
                                        <input type="checkbox" id="joinWithAudio" checked>
                                        <span class="checkmark"></span>
                                        Join with microphone on
                                    </label>
                                </div>
                            </div>

                            <div class="join-actions">
                                <button class="btn btn-success btn-lg" id="joinCallBtn" onclick="joinCall()" disabled>
                                    <i class="fas fa-video"></i>
                                    <span id="joinBtnText">Checking devices...</span>
                                </button>
                                <a href="{{ route('live-calls.show', $liveCall) }}" class="btn btn-outline-secondary btn-lg">
                                    <i class="fas fa-arrow-left"></i> Back to Call Details
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Live Call Interface -->
    <div class="live-call-interface" id="liveCallInterface" style="display: none;">
        <div class="call-header">
            <div class="call-info-mini">
                <h2>{{ $liveCall->title }}</h2>
                <span class="call-timer" id="callTimer">00:00</span>
            </div>
            <div class="call-controls-header">
                <button class="btn btn-sm btn-outline-light" onclick="toggleParticipants()">
                    <i class="fas fa-users"></i>
                    <span id="participantCount">0</span>
                </button>
                <button class="btn btn-sm btn-outline-light" onclick="toggleChat()">
                    <i class="fas fa-comments"></i>
                    <span class="chat-badge" id="chatBadge">0</span>
                </button>
            </div>
        </div>

        <div class="call-main">
            <div class="video-grid" id="videoGrid">
                <!-- Main speaker video -->
                <div class="main-video">
                    <video id="mainVideo" autoplay></video>
                    <div class="video-overlay">
                        <span class="speaker-name" id="mainSpeakerName">{{ $liveCall->mentor->name }}</span>
                    </div>
                </div>

                <!-- Participant videos -->
                <div class="participant-videos" id="participantVideos">
                    <!-- Participant video tiles will be added here -->
                </div>
            </div>

            <!-- Chat Panel -->
            <div class="chat-panel" id="chatPanel">
                <div class="chat-header">
                    <h4>Chat</h4>
                    <button class="btn btn-sm btn-outline-secondary" onclick="toggleChat()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="chat-messages" id="chatMessages">
                    <!-- Chat messages will appear here -->
                </div>
                <div class="chat-input">
                    <form onsubmit="sendMessage(event)">
                        <div class="input-group">
                            <input type="text" id="messageInput" class="form-control" placeholder="Type a message..." maxlength="500">
                            <div class="input-group-append">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Participants Panel -->
            <div class="participants-panel" id="participantsPanel">
                <div class="participants-header">
                    <h4>Participants (<span id="participantCountFull">0</span>)</h4>
                    <button class="btn btn-sm btn-outline-secondary" onclick="toggleParticipants()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="participants-list" id="participantsList">
                    <!-- Participants will be listed here -->
                </div>
            </div>
        </div>

        <div class="call-controls">
            <div class="control-group">
                <button class="control-btn" id="muteBtn" onclick="toggleMute()">
                    <i class="fas fa-microphone" id="muteIcon"></i>
                </button>
                <button class="control-btn" id="videoBtn" onclick="toggleVideo()">
                    <i class="fas fa-video" id="videoIcon"></i>
                </button>
                <button class="control-btn" onclick="shareScreen()">
                    <i class="fas fa-desktop"></i>
                </button>
            </div>

            <div class="control-group">
                <button class="control-btn danger" onclick="leaveCall()">
                    <i class="fas fa-phone-slash"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Leave Call Modal -->
<div class="modal fade" id="leaveCallModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Leave Call</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to leave this live call?</p>
                <div class="feedback-section">
                    <h6>How was your experience? (Optional)</h6>
                    <div class="rating-stars">
                        <i class="fas fa-star" data-rating="1"></i>
                        <i class="fas fa-star" data-rating="2"></i>
                        <i class="fas fa-star" data-rating="3"></i>
                        <i class="fas fa-star" data-rating="4"></i>
                        <i class="fas fa-star" data-rating="5"></i>
                    </div>
                    <textarea class="form-control mt-2" placeholder="Any feedback about the call?" rows="3"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Stay in Call</button>
                <button type="button" class="btn btn-danger" onclick="confirmLeaveCall()">Leave Call</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.live-call-container {
    min-height: 100vh;
    background: #1a1a1a;
    color: white;
}

.pre-call-setup {
    padding: 2rem 0;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.setup-card {
    background: #2d3748;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.setup-header {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.call-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: white;
}

.call-meta {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.call-mentor {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.mentor-avatar {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    object-fit: cover;
}

.call-time,
.call-duration {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #a0aec0;
}

.call-description {
    color: #e2e8f0;
    line-height: 1.6;
    max-width: 600px;
    margin: 0 auto;
}

.device-check {
    margin-bottom: 2rem;
}

.device-check h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: white;
}

.device-tests {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.device-test {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    padding: 1rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.test-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: #4299e1;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    flex-shrink: 0;
}

.test-content {
    flex: 1;
}

.test-content h4 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
    color: white;
}

.test-status {
    color: #a0aec0;
    font-size: 0.875rem;
    margin: 0;
}

.camera-preview {
    width: 200px;
    height: 120px;
    background: #000;
    border-radius: 8px;
    overflow: hidden;
    margin-top: 0.5rem;
    position: relative;
}

.camera-preview video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.audio-level {
    width: 200px;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    margin-top: 0.5rem;
    overflow: hidden;
}

.level-bar {
    height: 100%;
    background: #48bb78;
    width: 0%;
    transition: width 0.1s ease;
}

.test-controls {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.volume-control {
    width: 100px;
}

.volume-control input[type="range"] {
    width: 100%;
    height: 4px;
    border-radius: 2px;
    background: rgba(255, 255, 255, 0.2);
    outline: none;
}

.connection-status {
    margin-bottom: 2rem;
}

.connection-status h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: white;
}

.status-indicators {
    display: flex;
    gap: 2rem;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex: 1;
}

.status-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #48bb78;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}

.status-icon.warning {
    background: #ed8936;
}

.status-icon.error {
    background: #e53e3e;
}

.status-info {
    display: flex;
    flex-direction: column;
}

.status-label {
    font-weight: 600;
    color: white;
    font-size: 0.875rem;
}

.status-value {
    color: #a0aec0;
    font-size: 0.75rem;
}

.join-options {
    text-align: center;
}

.join-settings {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 2rem;
}

.setting-item {
    display: flex;
    align-items: center;
}

.setting-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    color: #e2e8f0;
    font-size: 0.875rem;
}

.setting-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #4299e1;
    border-radius: 4px;
    margin-right: 0.5rem;
    position: relative;
    transition: all 0.3s ease;
}

.setting-label input[type="checkbox"]:checked + .checkmark {
    background: #4299e1;
}

.setting-label input[type="checkbox"]:checked + .checkmark:after {
    content: "✓";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 0.75rem;
    font-weight: bold;
}

.join-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.live-call-interface {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: #1a1a1a;
}

.call-header {
    background: rgba(0, 0, 0, 0.8);
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.call-info-mini h2 {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
    color: white;
}

.call-timer {
    color: #a0aec0;
    font-size: 0.875rem;
}

.call-controls-header {
    display: flex;
    gap: 1rem;
}

.call-controls-header .btn {
    position: relative;
}

.chat-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #e53e3e;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.call-main {
    flex: 1;
    display: flex;
    position: relative;
}

.video-grid {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 1rem;
}

.main-video {
    flex: 1;
    background: #000;
    border-radius: 10px;
    overflow: hidden;
    position: relative;
    margin-bottom: 1rem;
}

.main-video video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.video-overlay {
    position: absolute;
    bottom: 1rem;
    left: 1rem;
    background: rgba(0, 0, 0, 0.7);
    padding: 0.5rem 1rem;
    border-radius: 20px;
}

.speaker-name {
    color: white;
    font-weight: 600;
    font-size: 0.875rem;
}

.participant-videos {
    display: flex;
    gap: 0.5rem;
    height: 120px;
    overflow-x: auto;
}

.participant-video {
    width: 160px;
    height: 120px;
    background: #000;
    border-radius: 8px;
    overflow: hidden;
    position: relative;
    flex-shrink: 0;
}

.participant-video video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.chat-panel,
.participants-panel {
    width: 300px;
    background: #2d3748;
    border-left: 1px solid rgba(255, 255, 255, 0.1);
    display: none;
    flex-direction: column;
}

.chat-panel.active,
.participants-panel.active {
    display: flex;
}

.chat-header,
.participants-header {
    padding: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chat-header h4,
.participants-header h4 {
    margin: 0;
    color: white;
    font-size: 1rem;
}

.chat-messages {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
}

.chat-message {
    margin-bottom: 1rem;
}

.message-author {
    font-weight: 600;
    color: #4299e1;
    font-size: 0.875rem;
}

.message-content {
    color: #e2e8f0;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.message-time {
    color: #a0aec0;
    font-size: 0.75rem;
    margin-top: 0.25rem;
}

.chat-input {
    padding: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.participants-list {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
}

.participant-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem 0;
}

.participant-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
}

.participant-info {
    flex: 1;
}

.participant-name {
    color: white;
    font-weight: 500;
    font-size: 0.875rem;
}

.participant-status {
    color: #a0aec0;
    font-size: 0.75rem;
}

.call-controls {
    background: rgba(0, 0, 0, 0.8);
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.control-group {
    display: flex;
    gap: 1rem;
}

.control-btn {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: none;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 1.25rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.control-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.control-btn.active {
    background: #4299e1;
}

.control-btn.danger {
    background: #e53e3e;
}

.control-btn.danger:hover {
    background: #c53030;
}

.rating-stars {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.rating-stars i {
    font-size: 1.5rem;
    color: #cbd5e0;
    cursor: pointer;
    transition: color 0.3s ease;
}

.rating-stars i:hover,
.rating-stars i.active {
    color: #f6e05e;
}

@media (max-width: 768px) {
    .call-meta {
        flex-direction: column;
        gap: 1rem;
    }

    .join-settings {
        flex-direction: column;
        gap: 1rem;
    }

    .join-actions {
        flex-direction: column;
    }

    .status-indicators {
        flex-direction: column;
        gap: 1rem;
    }

    .device-test {
        flex-direction: column;
        text-align: center;
    }

    .chat-panel,
    .participants-panel {
        width: 100%;
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        z-index: 1000;
    }

    .call-controls-header {
        gap: 0.5rem;
    }

    .control-group {
        gap: 0.5rem;
    }

    .control-btn {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
}
</style>
@endpush

@push('scripts')
<script>
let localStream = null;
let peerConnection = null;
let socket = null;
let isMuted = false;
let isVideoOff = false;
let callStartTime = null;
let callTimer = null;

// Device setup and testing
document.addEventListener('DOMContentLoaded', function() {
    initializeDeviceTests();
    checkConnectionStatus();
    setupWebSocket();
});

async function initializeDeviceTests() {
    try {
        // Test camera and microphone
        localStream = await navigator.mediaDevices.getUserMedia({
            video: true,
            audio: true
        });

        const localVideo = document.getElementById('localVideo');
        localVideo.srcObject = localStream;

        updateDeviceStatus('cameraTest', 'success', 'Camera ready');
        updateDeviceStatus('micTest', 'success', 'Microphone ready');

        // Setup audio level monitoring
        setupAudioLevelMonitoring();

        // Enable join button
        document.getElementById('joinCallBtn').disabled = false;
        document.getElementById('joinBtnText').textContent = 'Join Call';

    } catch (error) {
        console.error('Device access error:', error);
        handleDeviceError(error);
    }
}

function updateDeviceStatus(testId, status, message) {
    const testElement = document.getElementById(testId);
    const statusElement = testElement.querySelector('.test-status');
    const iconElement = testElement.querySelector('.test-icon');

    statusElement.textContent = message;

    if (status === 'success') {
        iconElement.style.background = '#48bb78';
    } else if (status === 'warning') {
        iconElement.style.background = '#ed8936';
    } else if (status === 'error') {
        iconElement.style.background = '#e53e3e';
    }
}

function handleDeviceError(error) {
    if (error.name === 'NotAllowedError') {
        updateDeviceStatus('cameraTest', 'error', 'Camera access denied');
        updateDeviceStatus('micTest', 'error', 'Microphone access denied');
        document.getElementById('joinBtnText').textContent = 'Grant permissions to join';
    } else if (error.name === 'NotFoundError') {
        updateDeviceStatus('cameraTest', 'error', 'No camera found');
        updateDeviceStatus('micTest', 'error', 'No microphone found');
    } else {
        updateDeviceStatus('cameraTest', 'error', 'Device error');
        updateDeviceStatus('micTest', 'error', 'Device error');
    }
}

function setupAudioLevelMonitoring() {
    const audioContext = new AudioContext();
    const analyser = audioContext.createAnalyser();
    const microphone = audioContext.createMediaStreamSource(localStream);
    const dataArray = new Uint8Array(analyser.frequencyBinCount);

    microphone.connect(analyser);
    analyser.fftSize = 256;

    function updateAudioLevel() {
        analyser.getByteFrequencyData(dataArray);
        const average = dataArray.reduce((a, b) => a + b) / dataArray.length;
        const percentage = (average / 255) * 100;

        document.getElementById('audioLevelBar').style.width = percentage + '%';
        requestAnimationFrame(updateAudioLevel);
    }

    updateAudioLevel();
}

function checkConnectionStatus() {
    // Simulate connection speed test
    const startTime = Date.now();
    fetch('/api/ping')
        .then(response => {
            const latency = Date.now() - startTime;
            updateConnectionStatus('internetStatus', 'success', `${latency}ms`);
            updateConnectionStatus('serverStatus', 'success', 'Connected');
        })
        .catch(error => {
            updateConnectionStatus('internetStatus', 'error', 'Connection failed');
            updateConnectionStatus('serverStatus', 'error', 'Server unavailable');
        });
}

function updateConnectionStatus(statusId, status, message) {
    const statusElement = document.getElementById(statusId);
    const valueElement = statusElement.parentElement.querySelector('.status-value');

    valueElement.textContent = message;

    if (status === 'success') {
        statusElement.style.background = '#48bb78';
    } else if (status === 'warning') {
        statusElement.style.background = '#ed8936';
    } else if (status === 'error') {
        statusElement.style.background = '#e53e3e';
    }
}

function toggleCamera() {
    if (localStream) {
        const videoTrack = localStream.getVideoTracks()[0];
        if (videoTrack) {
            videoTrack.enabled = !videoTrack.enabled;
            const icon = document.getElementById('cameraIcon');
            if (videoTrack.enabled) {
                icon.className = 'fas fa-video';
            } else {
                icon.className = 'fas fa-video-slash';
            }
        }
    }
}

function toggleMicrophone() {
    if (localStream) {
        const audioTrack = localStream.getAudioTracks()[0];
        if (audioTrack) {
            audioTrack.enabled = !audioTrack.enabled;
            const icon = document.getElementById('micIcon');
            if (audioTrack.enabled) {
                icon.className = 'fas fa-microphone';
            } else {
                icon.className = 'fas fa-microphone-slash';
            }
        }
    }
}

function testSpeakers() {
    const audio = new Audio('/sounds/test-sound.mp3');
    const volume = document.getElementById('volumeSlider').value / 100;
    audio.volume = volume;
    audio.play().catch(error => {
        console.error('Speaker test failed:', error);
        alert('Unable to test speakers. Please check your audio settings.');
    });
}

function setupWebSocket() {
    socket = new WebSocket(`ws://localhost:8080/live-call/{{ $liveCall->id }}`);

    socket.onopen = function(event) {
        console.log('WebSocket connected');
    };

    socket.onmessage = function(event) {
        const data = JSON.parse(event.data);
        handleWebSocketMessage(data);
    };

    socket.onclose = function(event) {
        console.log('WebSocket disconnected');
        // Attempt to reconnect
        setTimeout(setupWebSocket, 3000);
    };

    socket.onerror = function(error) {
        console.error('WebSocket error:', error);
    };
}

function handleWebSocketMessage(data) {
    switch (data.type) {
        case 'participant-joined':
            addParticipant(data.participant);
            break;
        case 'participant-left':
            removeParticipant(data.participantId);
            break;
        case 'chat-message':
            addChatMessage(data.message);
            break;
        case 'call-ended':
            handleCallEnded();
            break;
    }
}

async function joinCall() {
    try {
        // Hide setup interface and show call interface
        document.getElementById('preCallSetup').style.display = 'none';
        document.getElementById('liveCallInterface').style.display = 'flex';

        // Start call timer
        startCallTimer();

        // Setup peer connection
        await setupPeerConnection();

        // Join the call via WebSocket
        socket.send(JSON.stringify({
            type: 'join-call',
            callId: {{ $liveCall->id }},
            userId: {{ auth()->id() }},
            userName: '{{ auth()->user()->name }}',
            joinWithVideo: document.getElementById('joinWithVideo').checked,
            joinWithAudio: document.getElementById('joinWithAudio').checked
        }));

    } catch (error) {
        console.error('Failed to join call:', error);
        alert('Failed to join the call. Please try again.');
    }
}

async function setupPeerConnection() {
    peerConnection = new RTCPeerConnection({
        iceServers: [
            { urls: 'stun:stun.l.google.com:19302' }
        ]
    });

    // Add local stream to peer connection
    localStream.getTracks().forEach(track => {
        peerConnection.addTrack(track, localStream);
    });

    // Handle remote stream
    peerConnection.ontrack = function(event) {
        const remoteVideo = document.getElementById('mainVideo');
        remoteVideo.srcObject = event.streams[0];
    };

    // Handle ICE candidates
    peerConnection.onicecandidate = function(event) {
        if (event.candidate) {
            socket.send(JSON.stringify({
                type: 'ice-candidate',
                candidate: event.candidate
            }));
        }
    };
}

function startCallTimer() {
    callStartTime = Date.now();
    callTimer = setInterval(updateCallTimer, 1000);
}

function updateCallTimer() {
    const elapsed = Date.now() - callStartTime;
    const minutes = Math.floor(elapsed / 60000);
    const seconds = Math.floor((elapsed % 60000) / 1000);

    document.getElementById('callTimer').textContent =
        `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
}

function toggleMute() {
    if (localStream) {
        const audioTrack = localStream.getAudioTracks()[0];
        if (audioTrack) {
            audioTrack.enabled = !audioTrack.enabled;
            isMuted = !audioTrack.enabled;

            const btn = document.getElementById('muteBtn');
            const icon = document.getElementById('muteIcon');

            if (isMuted) {
                btn.classList.add('active');
                icon.className = 'fas fa-microphone-slash';
            } else {
                btn.classList.remove('active');
                icon.className = 'fas fa-microphone';
            }
        }
    }
}

function toggleVideo() {
    if (localStream) {
        const videoTrack = localStream.getVideoTracks()[0];
        if (videoTrack) {
            videoTrack.enabled = !videoTrack.enabled;
            isVideoOff = !videoTrack.enabled;

            const btn = document.getElementById('videoBtn');
            const icon = document.getElementById('videoIcon');

            if (isVideoOff) {
                btn.classList.add('active');
                icon.className = 'fas fa-video-slash';
            } else {
                btn.classList.remove('active');
                icon.className = 'fas fa-video';
            }
        }
    }
}

async function shareScreen() {
    try {
        const screenStream = await navigator.mediaDevices.getDisplayMedia({ video: true });

        // Replace video track with screen share
        const videoTrack = screenStream.getVideoTracks()[0];
        const sender = peerConnection.getSenders().find(s =>
            s.track && s.track.kind === 'video'
        );

        if (sender) {
            await sender.replaceTrack(videoTrack);
        }

        // Handle screen share end
        videoTrack.onended = function() {
            // Switch back to camera
            const cameraTrack = localStream.getVideoTracks()[0];
            if (sender && cameraTrack) {
                sender.replaceTrack(cameraTrack);
            }
        };

    } catch (error) {
        console.error('Screen share failed:', error);
        alert('Screen sharing is not supported or was denied.');
    }
}

function toggleChat() {
    const chatPanel = document.getElementById('chatPanel');
    chatPanel.classList.toggle('active');

    // Close participants panel if open
    document.getElementById('participantsPanel').classList.remove('active');
}

function toggleParticipants() {
    const participantsPanel = document.getElementById('participantsPanel');
    participantsPanel.classList.toggle('active');

    // Close chat panel if open
    document.getElementById('chatPanel').classList.remove('active');
}

function sendMessage(event) {
    event.preventDefault();

    const messageInput = document.getElementById('messageInput');
    const message = messageInput.value.trim();

    if (message) {
        socket.send(JSON.stringify({
            type: 'chat-message',
            message: message,
            userId: {{ auth()->id() }},
            userName: '{{ auth()->user()->name }}'
        }));

        messageInput.value = '';
    }
}

function addChatMessage(message) {
    const chatMessages = document.getElementById('chatMessages');
    const messageElement = document.createElement('div');
    messageElement.className = 'chat-message';

    messageElement.innerHTML = `
        <div class="message-author">${message.userName}</div>
        <div class="message-content">${message.content}</div>
        <div class="message-time">${new Date(message.timestamp).toLocaleTimeString()}</div>
    `;

    chatMessages.appendChild(messageElement);
    chatMessages.scrollTop = chatMessages.scrollHeight;

    // Update chat badge
    const chatBadge = document.getElementById('chatBadge');
    const currentCount = parseInt(chatBadge.textContent) || 0;
    chatBadge.textContent = currentCount + 1;
}

function addParticipant(participant) {
    const participantsList = document.getElementById('participantsList');
    const participantElement = document.createElement('div');
    participantElement.className = 'participant-item';
    participantElement.id = `participant-${participant.id}`;

    participantElement.innerHTML = `
        <img src="${participant.avatar}" alt="${participant.name}" class="participant-avatar">
        <div class="participant-info">
            <div class="participant-name">${participant.name}</div>
            <div class="participant-status">Online</div>
        </div>
    `;

    participantsList.appendChild(participantElement);

    // Update participant count
    updateParticipantCount();
}

function removeParticipant(participantId) {
    const participantElement = document.getElementById(`participant-${participantId}`);
    if (participantElement) {
        participantElement.remove();
        updateParticipantCount();
    }
}

function updateParticipantCount() {
    const count = document.querySelectorAll('.participant-item').length;
    document.getElementById('participantCount').textContent = count;
    document.getElementById('participantCountFull').textContent = count;
}

function leaveCall() {
    $('#leaveCallModal').modal('show');
}

function confirmLeaveCall() {
    // Stop call timer
    if (callTimer) {
        clearInterval(callTimer);
    }

    // Close peer connection
    if (peerConnection) {
        peerConnection.close();
    }

    // Stop local stream
    if (localStream) {
        localStream.getTracks().forEach(track => track.stop());
    }

    // Close WebSocket
    if (socket) {
        socket.close();
    }

    // Redirect to call details page
    window.location.href = '{{ route("live-calls.show", $liveCall) }}';
}

function handleCallEnded() {
    alert('The call has ended.');
    confirmLeaveCall();
}

// Rating functionality
document.querySelectorAll('.rating-stars i').forEach((star, index) => {
    star.addEventListener('click', function() {
        const rating = index + 1;

        // Update star display
        document.querySelectorAll('.rating-stars i').forEach((s, i) => {
            if (i < rating) {
                s.classList.add('active');
            } else {
                s.classList.remove('active');
            }
        });

        // Store rating
        this.closest('.feedback-section').dataset.rating = rating;
    });
});

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    if (localStream) {
        localStream.getTracks().forEach(track => track.stop());
    }
    if (peerConnection) {
        peerConnection.close();
    }
    if (socket) {
        socket.close();
    }
});
</script>
@endpush