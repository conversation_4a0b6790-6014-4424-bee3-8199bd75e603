-- Manual migration script for The Real World LMS
-- Run this if <PERSON><PERSON> migrations are not working

-- Add mentor fields to users table
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS role ENUM('student', 'mentor', 'admin') DEFAULT 'student' AFTER timezone,
ADD COLUMN IF NOT EXISTS title VARCHAR(255) NULL AFTER role,
ADD COLUMN IF NOT EXISTS specialties JSON NULL AFTER title,
ADD COLUMN IF NOT EXISTS social_links JSON NULL AFTER specialties,
ADD COLUMN IF NOT EXISTS portfolio_url VARCHAR(255) NULL AFTER social_links,
ADD COLUMN IF NOT EXISTS is_verified BOOLEAN DEFAULT FALSE AFTER is_active,
ADD COLUMN IF NOT EXISTS is_featured BOOLEAN DEFAULT FALSE AFTER is_verified,
ADD COLUMN IF NOT EXISTS average_rating DECIMAL(3,1) DEFAULT 0 AFTER is_featured;

-- Add indexes to users table
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_is_verified ON users(is_verified);
CREATE INDEX IF NOT EXISTS idx_users_is_featured ON users(is_featured);
CREATE INDEX IF NOT EXISTS idx_users_average_rating ON users(average_rating);

-- Add fields to courses table
ALTER TABLE courses 
ADD COLUMN IF NOT EXISTS category_id BIGINT UNSIGNED NULL AFTER mentor_id,
ADD COLUMN IF NOT EXISTS difficulty_level ENUM('beginner', 'intermediate', 'advanced') DEFAULT 'beginner' AFTER category_id,
ADD COLUMN IF NOT EXISTS total_duration INT DEFAULT 0 AFTER difficulty_level,
ADD COLUMN IF NOT EXISTS is_free BOOLEAN DEFAULT FALSE AFTER price,
ADD COLUMN IF NOT EXISTS average_rating DECIMAL(3,1) DEFAULT 0 AFTER is_free;

-- Add indexes to courses table
CREATE INDEX IF NOT EXISTS idx_courses_category_id ON courses(category_id);
CREATE INDEX IF NOT EXISTS idx_courses_difficulty_level ON courses(difficulty_level);
CREATE INDEX IF NOT EXISTS idx_courses_is_free ON courses(is_free);
CREATE INDEX IF NOT EXISTS idx_courses_average_rating ON courses(average_rating);

-- Add fields to certificates table
ALTER TABLE certificates 
ADD COLUMN IF NOT EXISTS certificate_id VARCHAR(255) UNIQUE AFTER id,
ADD COLUMN IF NOT EXISTS grade VARCHAR(255) NULL AFTER issued_at,
ADD COLUMN IF NOT EXISTS completion_time VARCHAR(255) NULL AFTER grade,
ADD COLUMN IF NOT EXISTS is_public BOOLEAN DEFAULT TRUE AFTER is_valid,
ADD COLUMN IF NOT EXISTS qr_code_url VARCHAR(255) NULL AFTER verification_url,
ADD COLUMN IF NOT EXISTS verification_hash VARCHAR(255) NULL AFTER qr_code_url,
ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP NULL;

-- Add indexes to certificates table
CREATE INDEX IF NOT EXISTS idx_certificates_certificate_id ON certificates(certificate_id);
CREATE INDEX IF NOT EXISTS idx_certificates_is_public ON certificates(is_public);
CREATE INDEX IF NOT EXISTS idx_certificates_verification_hash ON certificates(verification_hash);

-- Create enrollments table
CREATE TABLE IF NOT EXISTS enrollments (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    course_id BIGINT UNSIGNED NOT NULL,
    enrolled_at TIMESTAMP NULL,
    started_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    last_accessed_at TIMESTAMP NULL,
    progress_percentage DECIMAL(5,2) DEFAULT 0,
    completion_certificate_id BIGINT UNSIGNED NULL,
    payment_id BIGINT UNSIGNED NULL,
    enrollment_source VARCHAR(255) DEFAULT 'direct',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    deleted_at TIMESTAMP NULL,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    FOREIGN KEY (completion_certificate_id) REFERENCES certificates(id) ON DELETE SET NULL,
    
    UNIQUE KEY unique_user_course (user_id, course_id),
    INDEX idx_enrollments_user_id (user_id),
    INDEX idx_enrollments_course_id (course_id),
    INDEX idx_enrollments_last_accessed_at (last_accessed_at),
    INDEX idx_enrollments_completed_at (completed_at),
    INDEX idx_enrollments_enrollment_source (enrollment_source)
);

-- Create reviews table
CREATE TABLE IF NOT EXISTS reviews (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    course_id BIGINT UNSIGNED NOT NULL,
    mentor_id BIGINT UNSIGNED NULL,
    rating TINYINT UNSIGNED NOT NULL,
    title VARCHAR(255) NULL,
    comment TEXT NOT NULL,
    is_verified_purchase BOOLEAN DEFAULT FALSE,
    is_approved BOOLEAN DEFAULT FALSE,
    is_featured BOOLEAN DEFAULT FALSE,
    helpful_votes INT DEFAULT 0,
    unhelpful_votes INT DEFAULT 0,
    admin_response TEXT NULL,
    responded_at TIMESTAMP NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    deleted_at TIMESTAMP NULL,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    FOREIGN KEY (mentor_id) REFERENCES users(id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_user_course_review (user_id, course_id),
    INDEX idx_reviews_course_approved (course_id, is_approved),
    INDEX idx_reviews_mentor_approved (mentor_id, is_approved),
    INDEX idx_reviews_rating (rating),
    INDEX idx_reviews_is_featured (is_featured),
    INDEX idx_reviews_helpful_votes (helpful_votes)
);

-- Create review_votes table
CREATE TABLE IF NOT EXISTS review_votes (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    review_id BIGINT UNSIGNED NOT NULL,
    user_id BIGINT UNSIGNED NOT NULL,
    is_helpful BOOLEAN NOT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    FOREIGN KEY (review_id) REFERENCES reviews(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_review_user_vote (review_id, user_id),
    INDEX idx_review_votes_review_helpful (review_id, is_helpful)
);

-- Create mentor_followers table
CREATE TABLE IF NOT EXISTS mentor_followers (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    mentor_id BIGINT UNSIGNED NOT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (mentor_id) REFERENCES users(id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_user_mentor_follow (user_id, mentor_id),
    INDEX idx_mentor_followers_user_id (user_id),
    INDEX idx_mentor_followers_mentor_id (mentor_id)
);

-- Create user_achievements table
CREATE TABLE IF NOT EXISTS user_achievements (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    date DATE NOT NULL,
    icon VARCHAR(255) NULL,
    category VARCHAR(255) NULL,
    is_featured BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    INDEX idx_user_achievements_user_id (user_id),
    INDEX idx_user_achievements_date (date),
    INDEX idx_user_achievements_category (category),
    INDEX idx_user_achievements_is_featured (is_featured)
);

-- Insert some sample data for testing
INSERT IGNORE INTO users (id, name, email, password, role, title, is_verified, is_featured, average_rating, created_at, updated_at) VALUES
(1, 'Andrew Tate', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'mentor', 'Entrepreneur & Business Coach', TRUE, TRUE, 4.8, NOW(), NOW()),
(2, 'Tristan Tate', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'mentor', 'Investment Expert', TRUE, TRUE, 4.7, NOW(), NOW()),
(3, 'John Student', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'student', NULL, FALSE, FALSE, 0, NOW(), NOW());

-- Insert sample courses
INSERT IGNORE INTO courses (id, title, slug, description, mentor_id, category_id, difficulty_level, total_duration, price, is_free, is_published, is_featured, average_rating, created_at, updated_at) VALUES
(1, 'Hustlers University', 'hustlers-university', 'Learn the fundamentals of making money online', 1, 1, 'beginner', 40, 49.99, FALSE, TRUE, TRUE, 4.8, NOW(), NOW()),
(2, 'Investment Mastery', 'investment-mastery', 'Master the art of investing and wealth building', 2, 1, 'intermediate', 60, 99.99, FALSE, TRUE, TRUE, 4.7, NOW(), NOW());

-- Insert sample enrollments
INSERT IGNORE INTO enrollments (user_id, course_id, enrolled_at, started_at, progress_percentage, is_active, created_at, updated_at) VALUES
(3, 1, NOW(), NOW(), 25.5, TRUE, NOW(), NOW()),
(3, 2, NOW(), NULL, 0, TRUE, NOW(), NOW());

-- Insert sample reviews
INSERT IGNORE INTO reviews (user_id, course_id, mentor_id, rating, title, comment, is_verified_purchase, is_approved, created_at, updated_at) VALUES
(3, 1, 1, 5, 'Excellent Course!', 'This course changed my life. Highly recommended!', TRUE, TRUE, NOW(), NOW());
