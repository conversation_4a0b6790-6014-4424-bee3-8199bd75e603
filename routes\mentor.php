<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Mentor\MentorDashboardController;
use App\Http\Controllers\Mentor\MentorCourseController;
use App\Http\Controllers\Mentor\MentorLessonController;
use App\Http\Controllers\Mentor\MentorStudentController;
use App\Http\Controllers\Mentor\MentorAnalyticsController;
use App\Http\Controllers\Mentor\MentorLiveCallController;
use App\Http\Controllers\Mentor\MentorCommunityController;
use App\Http\Controllers\Mentor\MentorProfileController;

/*
|--------------------------------------------------------------------------
| Mentor Routes
|--------------------------------------------------------------------------
|
| Here are the routes for mentor-specific functionality. These routes
| require authentication and mentor role permissions.
|
*/

// Mentor Routes (requires mentor role)
Route::middleware(['auth', 'verified', 'role:mentor'])->prefix('mentor')->name('mentor.')->group(function () {
    
    // Dashboard
    Route::get('/', [MentorDashboardController::class, 'index'])->name('dashboard');
    Route::get('/stats', [MentorDashboardController::class, 'stats'])->name('stats');
    Route::get('/overview', [MentorDashboardController::class, 'overview'])->name('overview');
    
    // Profile Management
    Route::get('/profile', [MentorProfileController::class, 'index'])->name('profile.index');
    Route::get('/profile/edit', [MentorProfileController::class, 'edit'])->name('profile.edit');
    Route::put('/profile', [MentorProfileController::class, 'update'])->name('profile.update');
    Route::post('/profile/avatar', [MentorProfileController::class, 'updateAvatar'])->name('profile.avatar');
    Route::delete('/profile/avatar', [MentorProfileController::class, 'deleteAvatar'])->name('profile.avatar.delete');
    
    // Course Management
    Route::resource('courses', MentorCourseController::class);
    Route::post('courses/{course}/publish', [MentorCourseController::class, 'publish'])->name('courses.publish');
    Route::post('courses/{course}/unpublish', [MentorCourseController::class, 'unpublish'])->name('courses.unpublish');
    Route::post('courses/{course}/duplicate', [MentorCourseController::class, 'duplicate'])->name('courses.duplicate');
    Route::get('courses/{course}/analytics', [MentorCourseController::class, 'analytics'])->name('courses.analytics');
    Route::get('courses/{course}/students', [MentorCourseController::class, 'students'])->name('courses.students');
    Route::get('courses/{course}/reviews', [MentorCourseController::class, 'reviews'])->name('courses.reviews');
    Route::post('courses/{course}/reviews/{review}/reply', [MentorCourseController::class, 'replyToReview'])->name('courses.reviews.reply');
    
    // Lesson Management
    Route::resource('courses.lessons', MentorLessonController::class)->except(['index']);
    Route::get('lessons', [MentorLessonController::class, 'index'])->name('lessons.index');
    Route::post('lessons/{lesson}/publish', [MentorLessonController::class, 'publish'])->name('lessons.publish');
    Route::post('lessons/{lesson}/unpublish', [MentorLessonController::class, 'unpublish'])->name('lessons.unpublish');
    Route::post('lessons/reorder', [MentorLessonController::class, 'reorder'])->name('lessons.reorder');
    Route::get('lessons/{lesson}/analytics', [MentorLessonController::class, 'analytics'])->name('lessons.analytics');
    Route::post('lessons/{lesson}/upload-video', [MentorLessonController::class, 'uploadVideo'])->name('lessons.upload-video');
    Route::delete('lessons/{lesson}/video', [MentorLessonController::class, 'deleteVideo'])->name('lessons.delete-video');
    Route::post('lessons/{lesson}/upload-resource', [MentorLessonController::class, 'uploadResource'])->name('lessons.upload-resource');
    Route::delete('lessons/{lesson}/resources/{resource}', [MentorLessonController::class, 'deleteResource'])->name('lessons.delete-resource');
    
    // Student Management
    Route::get('students', [MentorStudentController::class, 'index'])->name('students.index');
    Route::get('students/{user}', [MentorStudentController::class, 'show'])->name('students.show');
    Route::get('students/{user}/progress', [MentorStudentController::class, 'progress'])->name('students.progress');
    Route::post('students/{user}/message', [MentorStudentController::class, 'sendMessage'])->name('students.message');
    Route::post('students/{user}/certificate', [MentorStudentController::class, 'issueCertificate'])->name('students.certificate');
    Route::get('students/export', [MentorStudentController::class, 'export'])->name('students.export');
    
    // Live Call Management
    Route::resource('live-calls', MentorLiveCallController::class);
    Route::post('live-calls/{liveCall}/start', [MentorLiveCallController::class, 'start'])->name('live-calls.start');
    Route::post('live-calls/{liveCall}/end', [MentorLiveCallController::class, 'end'])->name('live-calls.end');
    Route::get('live-calls/{liveCall}/attendees', [MentorLiveCallController::class, 'attendees'])->name('live-calls.attendees');
    Route::post('live-calls/{liveCall}/send-reminder', [MentorLiveCallController::class, 'sendReminder'])->name('live-calls.send-reminder');
    Route::get('live-calls/{liveCall}/recording', [MentorLiveCallController::class, 'recording'])->name('live-calls.recording');
    Route::post('live-calls/{liveCall}/upload-recording', [MentorLiveCallController::class, 'uploadRecording'])->name('live-calls.upload-recording');
    
    // Community Management
    Route::get('community', [MentorCommunityController::class, 'index'])->name('community.index');
    Route::get('community/posts', [MentorCommunityController::class, 'posts'])->name('community.posts');
    Route::get('community/posts/{post}', [MentorCommunityController::class, 'showPost'])->name('community.posts.show');
    Route::post('community/posts/{post}/pin', [MentorCommunityController::class, 'pinPost'])->name('community.posts.pin');
    Route::post('community/posts/{post}/unpin', [MentorCommunityController::class, 'unpinPost'])->name('community.posts.unpin');
    Route::post('community/posts/{post}/feature', [MentorCommunityController::class, 'featurePost'])->name('community.posts.feature');
    Route::delete('community/posts/{post}', [MentorCommunityController::class, 'deletePost'])->name('community.posts.delete');
    Route::get('community/reports', [MentorCommunityController::class, 'reports'])->name('community.reports');
    Route::post('community/reports/{report}/resolve', [MentorCommunityController::class, 'resolveReport'])->name('community.reports.resolve');
    
    // Analytics
    Route::get('analytics', [MentorAnalyticsController::class, 'index'])->name('analytics.index');
    Route::get('analytics/courses', [MentorAnalyticsController::class, 'courses'])->name('analytics.courses');
    Route::get('analytics/students', [MentorAnalyticsController::class, 'students'])->name('analytics.students');
    Route::get('analytics/engagement', [MentorAnalyticsController::class, 'engagement'])->name('analytics.engagement');
    Route::get('analytics/revenue', [MentorAnalyticsController::class, 'revenue'])->name('analytics.revenue');
    Route::get('analytics/live-calls', [MentorAnalyticsController::class, 'liveCalls'])->name('analytics.live-calls');
    Route::get('analytics/export', [MentorAnalyticsController::class, 'export'])->name('analytics.export');
    
    // Messages & Communication
    Route::get('messages', [MentorDashboardController::class, 'messages'])->name('messages.index');
    Route::get('messages/{conversation}', [MentorDashboardController::class, 'showConversation'])->name('messages.show');
    Route::post('messages/{conversation}/reply', [MentorDashboardController::class, 'replyToMessage'])->name('messages.reply');
    Route::post('messages/broadcast', [MentorDashboardController::class, 'broadcastMessage'])->name('messages.broadcast');
    
    // Earnings & Payouts
    Route::get('earnings', [MentorAnalyticsController::class, 'earnings'])->name('earnings.index');
    Route::get('earnings/history', [MentorAnalyticsController::class, 'earningsHistory'])->name('earnings.history');
    Route::post('earnings/request-payout', [MentorAnalyticsController::class, 'requestPayout'])->name('earnings.request-payout');
    Route::get('earnings/tax-documents', [MentorAnalyticsController::class, 'taxDocuments'])->name('earnings.tax-documents');
    
    // Settings
    Route::get('settings', [MentorProfileController::class, 'settings'])->name('settings.index');
    Route::put('settings/notifications', [MentorProfileController::class, 'updateNotificationSettings'])->name('settings.notifications');
    Route::put('settings/availability', [MentorProfileController::class, 'updateAvailability'])->name('settings.availability');
    Route::put('settings/payment', [MentorProfileController::class, 'updatePaymentSettings'])->name('settings.payment');
    
    // Bulk Actions
    Route::post('courses/bulk-action', [MentorCourseController::class, 'bulkAction'])->name('courses.bulk-action');
    Route::post('lessons/bulk-action', [MentorLessonController::class, 'bulkAction'])->name('lessons.bulk-action');
    Route::post('students/bulk-message', [MentorStudentController::class, 'bulkMessage'])->name('students.bulk-message');
    
    // File Management
    Route::post('upload/image', [MentorDashboardController::class, 'uploadImage'])->name('upload.image');
    Route::post('upload/video', [MentorDashboardController::class, 'uploadVideo'])->name('upload.video');
    Route::post('upload/document', [MentorDashboardController::class, 'uploadDocument'])->name('upload.document');
    Route::delete('files/{file}', [MentorDashboardController::class, 'deleteFile'])->name('files.delete');
    
    // Quick Actions
    Route::post('quick-actions/create-course', [MentorCourseController::class, 'quickCreate'])->name('quick-actions.create-course');
    Route::post('quick-actions/schedule-call', [MentorLiveCallController::class, 'quickSchedule'])->name('quick-actions.schedule-call');
    Route::post('quick-actions/send-announcement', [MentorCommunityController::class, 'quickAnnouncement'])->name('quick-actions.send-announcement');
    
    // API Endpoints for AJAX requests
    Route::get('api/dashboard-stats', [MentorDashboardController::class, 'apiStats'])->name('api.dashboard-stats');
    Route::get('api/recent-activity', [MentorDashboardController::class, 'apiRecentActivity'])->name('api.recent-activity');
    Route::get('api/course/{course}/progress', [MentorCourseController::class, 'apiCourseProgress'])->name('api.course-progress');
    Route::get('api/student/{user}/progress', [MentorStudentController::class, 'apiStudentProgress'])->name('api.student-progress');
    
    // Export Routes
    Route::get('export/courses', [MentorCourseController::class, 'exportCourses'])->name('export.courses');
    Route::get('export/students', [MentorStudentController::class, 'exportStudents'])->name('export.students');
    Route::get('export/analytics', [MentorAnalyticsController::class, 'exportAnalytics'])->name('export.analytics');
    Route::get('export/earnings', [MentorAnalyticsController::class, 'exportEarnings'])->name('export.earnings');
});

// Mentor API Routes (for mobile app)
Route::middleware(['auth:sanctum', 'role:mentor'])->prefix('api/mentor')->name('api.mentor.')->group(function () {
    Route::get('dashboard', [MentorDashboardController::class, 'apiDashboard']);
    Route::get('courses', [MentorCourseController::class, 'apiIndex']);
    Route::get('courses/{course}', [MentorCourseController::class, 'apiShow']);
    Route::get('students', [MentorStudentController::class, 'apiIndex']);
    Route::get('analytics', [MentorAnalyticsController::class, 'apiIndex']);
    Route::get('live-calls', [MentorLiveCallController::class, 'apiIndex']);
    Route::get('earnings', [MentorAnalyticsController::class, 'apiEarnings']);
});
