@extends('layouts.app')

@section('title', 'Contact Us')

@section('content')
<div class="contact-container">
    <!-- Contact Hero -->
    <div class="contact-hero">
        <div class="container">
            <div class="row justify-content-center text-center">
                <div class="col-lg-8">
                    <h1 class="hero-title">Get in Touch</h1>
                    <p class="hero-subtitle">
                        Have questions? We're here to help you succeed. Reach out to our support team and we'll get back to you within 24 hours.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <div class="contact-content">
        <div class="container">
            <div class="row">
                <!-- Contact Form -->
                <div class="col-lg-8">
                    <div class="contact-form-section">
                        <div class="form-header">
                            <h2>Send us a Message</h2>
                            <p>Fill out the form below and we'll respond as soon as possible</p>
                        </div>
                        
                        <form id="contactForm" class="contact-form" onsubmit="submitContactForm(event)">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="firstName">First Name *</label>
                                        <input type="text" id="firstName" name="first_name" class="form-control" required>
                                        <div class="form-feedback"></div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="lastName">Last Name *</label>
                                        <input type="text" id="lastName" name="last_name" class="form-control" required>
                                        <div class="form-feedback"></div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="email">Email Address *</label>
                                        <input type="email" id="email" name="email" class="form-control" required>
                                        <div class="form-feedback"></div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="phone">Phone Number</label>
                                        <input type="tel" id="phone" name="phone" class="form-control">
                                        <div class="form-feedback"></div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="subject">Subject *</label>
                                <select id="subject" name="subject" class="form-control" required>
                                    <option value="">Select a subject</option>
                                    <option value="general">General Inquiry</option>
                                    <option value="technical">Technical Support</option>
                                    <option value="billing">Billing & Payments</option>
                                    <option value="course">Course Related</option>
                                    <option value="account">Account Issues</option>
                                    <option value="partnership">Partnership Opportunities</option>
                                    <option value="feedback">Feedback & Suggestions</option>
                                    <option value="other">Other</option>
                                </select>
                                <div class="form-feedback"></div>
                            </div>
                            
                            <div class="form-group">
                                <label for="priority">Priority Level</label>
                                <select id="priority" name="priority" class="form-control">
                                    <option value="low">Low - General question</option>
                                    <option value="medium" selected>Medium - Need assistance</option>
                                    <option value="high">High - Urgent issue</option>
                                    <option value="critical">Critical - System down</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="message">Message *</label>
                                <textarea id="message" name="message" class="form-control" rows="6" 
                                          placeholder="Please describe your question or issue in detail..." required></textarea>
                                <div class="form-feedback"></div>
                                <small class="form-text text-muted">
                                    <span id="messageCount">0</span>/1000 characters
                                </small>
                            </div>
                            
                            <div class="form-group">
                                <label for="attachment">Attachment (optional)</label>
                                <div class="file-upload-area" onclick="document.getElementById('attachment').click()">
                                    <div class="upload-icon">
                                        <i class="fas fa-cloud-upload-alt"></i>
                                    </div>
                                    <div class="upload-text">
                                        <span class="upload-title">Click to upload files</span>
                                        <span class="upload-subtitle">or drag and drop</span>
                                    </div>
                                    <input type="file" id="attachment" name="attachment" 
                                           accept=".jpg,.jpeg,.png,.pdf,.doc,.docx" style="display: none;">
                                </div>
                                <div class="file-info" id="fileInfo" style="display: none;">
                                    <div class="file-details">
                                        <i class="fas fa-file"></i>
                                        <span class="file-name"></span>
                                        <span class="file-size"></span>
                                    </div>
                                    <button type="button" class="remove-file" onclick="removeFile()">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                                <small class="form-text text-muted">
                                    Supported formats: JPG, PNG, PDF, DOC, DOCX (Max 10MB)
                                </small>
                            </div>
                            
                            @auth
                            <div class="form-group">
                                <div class="form-check">
                                    <input type="checkbox" id="copyToEmail" name="copy_to_email" class="form-check-input" checked>
                                    <label for="copyToEmail" class="form-check-label">
                                        Send me a copy of this message
                                    </label>
                                </div>
                            </div>
                            @endauth
                            
                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary btn-lg" id="submitBtn">
                                    <i class="fas fa-paper-plane"></i>
                                    <span class="btn-text">Send Message</span>
                                    <div class="btn-spinner" style="display: none;">
                                        <i class="fas fa-spinner fa-spin"></i>
                                    </div>
                                </button>
                                <button type="reset" class="btn btn-outline-secondary btn-lg">
                                    <i class="fas fa-undo"></i>
                                    Reset Form
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Contact Info Sidebar -->
                <div class="col-lg-4">
                    <div class="contact-sidebar">
                        <!-- Contact Methods -->
                        <div class="contact-card">
                            <h3>Contact Information</h3>
                            
                            <div class="contact-method">
                                <div class="method-icon">
                                    <i class="fas fa-envelope"></i>
                                </div>
                                <div class="method-info">
                                    <h4>Email Support</h4>
                                    <p><EMAIL></p>
                                    <small>Response within 24 hours</small>
                                </div>
                            </div>
                            
                            <div class="contact-method">
                                <div class="method-icon">
                                    <i class="fas fa-comments"></i>
                                </div>
                                <div class="method-info">
                                    <h4>Live Chat</h4>
                                    <p>Available 24/7</p>
                                    <button class="btn btn-sm btn-primary" onclick="openLiveChat()">
                                        Start Chat
                                    </button>
                                </div>
                            </div>
                            
                            <div class="contact-method">
                                <div class="method-icon">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="method-info">
                                    <h4>Community Forum</h4>
                                    <p>Get help from other members</p>
                                    <a href="{{ route('community.index') }}" class="btn btn-sm btn-outline-primary">
                                        Visit Forum
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- FAQ Quick Links -->
                        <div class="contact-card">
                            <h3>Quick Help</h3>
                            <div class="quick-help-list">
                                <a href="{{ route('faq') }}#billing" class="help-link">
                                    <i class="fas fa-credit-card"></i>
                                    Billing & Payments
                                </a>
                                <a href="{{ route('faq') }}#technical" class="help-link">
                                    <i class="fas fa-cog"></i>
                                    Technical Issues
                                </a>
                                <a href="{{ route('faq') }}#account" class="help-link">
                                    <i class="fas fa-user"></i>
                                    Account Management
                                </a>
                                <a href="{{ route('faq') }}#courses" class="help-link">
                                    <i class="fas fa-book"></i>
                                    Course Access
                                </a>
                            </div>
                            <a href="{{ route('faq') }}" class="btn btn-outline-primary btn-sm btn-block">
                                View All FAQs
                            </a>
                        </div>

                        <!-- Business Hours -->
                        <div class="contact-card">
                            <h3>Support Hours</h3>
                            <div class="business-hours">
                                <div class="hours-item">
                                    <span class="day">Monday - Friday</span>
                                    <span class="time">9:00 AM - 6:00 PM EST</span>
                                </div>
                                <div class="hours-item">
                                    <span class="day">Saturday</span>
                                    <span class="time">10:00 AM - 4:00 PM EST</span>
                                </div>
                                <div class="hours-item">
                                    <span class="day">Sunday</span>
                                    <span class="time">Closed</span>
                                </div>
                            </div>
                            <div class="current-status">
                                <div class="status-indicator online"></div>
                                <span class="status-text">We're currently online</span>
                            </div>
                        </div>

                        <!-- Social Media -->
                        <div class="contact-card">
                            <h3>Follow Us</h3>
                            <div class="social-links">
                                <a href="#" class="social-link twitter">
                                    <i class="fab fa-twitter"></i>
                                    <span>@TheRealWorld</span>
                                </a>
                                <a href="#" class="social-link instagram">
                                    <i class="fab fa-instagram"></i>
                                    <span>@therealworld</span>
                                </a>
                                <a href="#" class="social-link youtube">
                                    <i class="fab fa-youtube"></i>
                                    <span>The Real World</span>
                                </a>
                                <a href="#" class="social-link linkedin">
                                    <i class="fab fa-linkedin"></i>
                                    <span>The Real World</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Success Modal -->
<div class="modal fade" id="successModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-body text-center">
                <div class="success-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h3>Message Sent Successfully!</h3>
                <p>Thank you for contacting us. We've received your message and will respond within 24 hours.</p>
                <div class="ticket-info">
                    <strong>Ticket ID: <span id="ticketId">#12345</span></strong>
                    <p>Save this ID for future reference</p>
                </div>
                <button type="button" class="btn btn-primary" data-dismiss="modal">
                    Close
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.contact-container {
    background: #f8f9fc;
    min-height: 100vh;
}

.contact-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4rem 0;
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.hero-subtitle {
    font-size: 1.25rem;
    opacity: 0.9;
    line-height: 1.6;
}

.contact-content {
    padding: 3rem 0;
}

.contact-form-section {
    background: white;
    border-radius: 20px;
    padding: 3rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.form-header {
    text-align: center;
    margin-bottom: 3rem;
}

.form-header h2 {
    color: #2d3748;
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.form-header p {
    color: #718096;
    font-size: 1.125rem;
}

.contact-form .form-group {
    margin-bottom: 2rem;
}

.contact-form label {
    color: #2d3748;
    font-weight: 600;
    margin-bottom: 0.5rem;
    display: block;
}

.contact-form .form-control {
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    padding: 1rem;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.contact-form .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-feedback {
    margin-top: 0.5rem;
    font-size: 0.875rem;
}

.form-feedback.valid {
    color: #48bb78;
}

.form-feedback.invalid {
    color: #e53e3e;
}

.file-upload-area {
    border: 2px dashed #cbd5e0;
    border-radius: 10px;
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f7fafc;
}

.file-upload-area:hover {
    border-color: #667eea;
    background: #edf2f7;
}

.upload-icon {
    font-size: 3rem;
    color: #a0aec0;
    margin-bottom: 1rem;
}

.upload-title {
    display: block;
    color: #2d3748;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.upload-subtitle {
    color: #718096;
    font-size: 0.875rem;
}

.file-info {
    background: #f7fafc;
    border: 1px solid #e2e8f0;
    border-radius: 10px;
    padding: 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 1rem;
}

.file-details {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.file-details i {
    color: #667eea;
    font-size: 1.25rem;
}

.file-name {
    font-weight: 600;
    color: #2d3748;
}

.file-size {
    color: #718096;
    font-size: 0.875rem;
}

.remove-file {
    background: #e53e3e;
    color: white;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.remove-file:hover {
    background: #c53030;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 3rem;
}

.btn-spinner {
    margin-left: 0.5rem;
}

.contact-sidebar {
    position: sticky;
    top: 2rem;
}

.contact-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    margin-bottom: 2rem;
}

.contact-card h3 {
    color: #2d3748;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    text-align: center;
}

.contact-method {
    display: flex;
    align-items: flex-start;
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid #e2e8f0;
}

.contact-method:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.method-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    flex-shrink: 0;
    font-size: 1.25rem;
}

.method-info h4 {
    color: #2d3748;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.method-info p {
    color: #4a5568;
    margin-bottom: 0.5rem;
}

.method-info small {
    color: #718096;
    font-size: 0.75rem;
}

.quick-help-list {
    margin-bottom: 1.5rem;
}

.help-link {
    display: flex;
    align-items: center;
    color: #4a5568;
    text-decoration: none;
    padding: 0.75rem 0;
    border-bottom: 1px solid #e2e8f0;
    transition: color 0.3s ease;
}

.help-link:hover {
    color: #667eea;
    text-decoration: none;
}

.help-link i {
    margin-right: 0.75rem;
    width: 20px;
    color: #667eea;
}

.help-link:last-child {
    border-bottom: none;
}

.business-hours {
    margin-bottom: 1.5rem;
}

.hours-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #e2e8f0;
}

.hours-item:last-child {
    border-bottom: none;
}

.day {
    color: #2d3748;
    font-weight: 500;
}

.time {
    color: #4a5568;
    font-size: 0.875rem;
}

.current-status {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
    background: #f0fff4;
    border-radius: 8px;
}

.status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-indicator.online {
    background: #48bb78;
}

.status-indicator.offline {
    background: #e53e3e;
}

.status-text {
    color: #2d3748;
    font-weight: 500;
    font-size: 0.875rem;
}

.social-links {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.social-link {
    display: flex;
    align-items: center;
    color: #4a5568;
    text-decoration: none;
    padding: 0.75rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.social-link:hover {
    background: #f7fafc;
    color: #667eea;
    text-decoration: none;
}

.social-link i {
    margin-right: 0.75rem;
    width: 20px;
    font-size: 1.25rem;
}

.social-link.twitter:hover {
    color: #1da1f2;
}

.social-link.instagram:hover {
    color: #e4405f;
}

.social-link.youtube:hover {
    color: #ff0000;
}

.social-link.linkedin:hover {
    color: #0077b5;
}

.success-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: #48bb78;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    font-size: 2rem;
}

.ticket-info {
    background: #f7fafc;
    border-radius: 8px;
    padding: 1rem;
    margin: 1.5rem 0;
}

@media (max-width: 768px) {
    .hero-title {
        font-size: 2rem;
    }

    .contact-form-section {
        padding: 2rem;
    }

    .form-actions {
        flex-direction: column;
        align-items: center;
    }

    .form-actions .btn {
        width: 100%;
        max-width: 300px;
    }

    .contact-sidebar {
        margin-top: 2rem;
    }

    .contact-method {
        flex-direction: column;
        text-align: center;
    }

    .method-icon {
        margin: 0 auto 1rem;
    }
}

.form-control.is-valid {
    border-color: #48bb78;
}

.form-control.is-invalid {
    border-color: #e53e3e;
}

#submitBtn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}
</style>
@endpush

@push('scripts')
<script>
// Form validation
const form = document.getElementById('contactForm');
const inputs = form.querySelectorAll('input, select, textarea');

// Real-time validation
inputs.forEach(input => {
    input.addEventListener('blur', validateField);
    input.addEventListener('input', clearValidation);
});

function validateField(event) {
    const field = event.target;
    const value = field.value.trim();
    const feedback = field.parentElement.querySelector('.form-feedback');

    let isValid = true;
    let message = '';

    // Required field validation
    if (field.hasAttribute('required') && !value) {
        isValid = false;
        message = 'This field is required';
    }

    // Email validation
    if (field.type === 'email' && value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
            isValid = false;
            message = 'Please enter a valid email address';
        }
    }

    // Phone validation
    if (field.type === 'tel' && value) {
        const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
        if (!phoneRegex.test(value.replace(/[\s\-\(\)]/g, ''))) {
            isValid = false;
            message = 'Please enter a valid phone number';
        }
    }

    // Message length validation
    if (field.id === 'message' && value.length > 1000) {
        isValid = false;
        message = 'Message must be less than 1000 characters';
    }

    // Update field appearance
    if (isValid) {
        field.classList.remove('is-invalid');
        field.classList.add('is-valid');
        feedback.textContent = '';
        feedback.className = 'form-feedback valid';
    } else {
        field.classList.remove('is-valid');
        field.classList.add('is-invalid');
        feedback.textContent = message;
        feedback.className = 'form-feedback invalid';
    }

    return isValid;
}

function clearValidation(event) {
    const field = event.target;
    field.classList.remove('is-valid', 'is-invalid');
    const feedback = field.parentElement.querySelector('.form-feedback');
    feedback.textContent = '';
    feedback.className = 'form-feedback';
}

// Message character counter
document.getElementById('message').addEventListener('input', function() {
    const count = this.value.length;
    document.getElementById('messageCount').textContent = count;

    if (count > 1000) {
        this.classList.add('is-invalid');
    } else {
        this.classList.remove('is-invalid');
    }
});

// File upload handling
const fileInput = document.getElementById('attachment');
const fileInfo = document.getElementById('fileInfo');

fileInput.addEventListener('change', function() {
    const file = this.files[0];
    if (file) {
        // Validate file size (10MB max)
        if (file.size > 10 * 1024 * 1024) {
            alert('File size must be less than 10MB');
            this.value = '';
            return;
        }

        // Validate file type
        const allowedTypes = ['image/jpeg', 'image/png', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
        if (!allowedTypes.includes(file.type)) {
            alert('Please select a valid file type (JPG, PNG, PDF, DOC, DOCX)');
            this.value = '';
            return;
        }

        // Show file info
        fileInfo.style.display = 'flex';
        fileInfo.querySelector('.file-name').textContent = file.name;
        fileInfo.querySelector('.file-size').textContent = formatFileSize(file.size);
    }
});

function removeFile() {
    document.getElementById('attachment').value = '';
    document.getElementById('fileInfo').style.display = 'none';
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Form submission
function submitContactForm(event) {
    event.preventDefault();

    // Validate all fields
    let isFormValid = true;
    inputs.forEach(input => {
        if (!validateField({ target: input })) {
            isFormValid = false;
        }
    });

    if (!isFormValid) {
        alert('Please fix the errors in the form before submitting');
        return;
    }

    // Show loading state
    const submitBtn = document.getElementById('submitBtn');
    const btnText = submitBtn.querySelector('.btn-text');
    const btnSpinner = submitBtn.querySelector('.btn-spinner');

    submitBtn.disabled = true;
    btnText.style.display = 'none';
    btnSpinner.style.display = 'inline-block';

    // Prepare form data
    const formData = new FormData(form);

    // Submit form
    fetch('/contact', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success modal
            document.getElementById('ticketId').textContent = data.ticket_id;
            $('#successModal').modal('show');

            // Reset form
            form.reset();
            inputs.forEach(input => {
                input.classList.remove('is-valid', 'is-invalid');
                const feedback = input.parentElement.querySelector('.form-feedback');
                if (feedback) {
                    feedback.textContent = '';
                    feedback.className = 'form-feedback';
                }
            });

            // Hide file info
            fileInfo.style.display = 'none';

            // Reset message counter
            document.getElementById('messageCount').textContent = '0';
        } else {
            alert('There was an error sending your message. Please try again.');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('There was an error sending your message. Please try again.');
    })
    .finally(() => {
        // Reset button state
        submitBtn.disabled = false;
        btnText.style.display = 'inline';
        btnSpinner.style.display = 'none';
    });
}

// Live chat functionality
function openLiveChat() {
    // This would integrate with your live chat system
    alert('Live chat feature coming soon! Please use the contact form for now.');
}

// Auto-fill form for logged-in users
@auth
document.addEventListener('DOMContentLoaded', function() {
    // Pre-fill user information if available
    const userEmail = '{{ auth()->user()->email }}';
    const userName = '{{ auth()->user()->name }}';

    if (userEmail) {
        document.getElementById('email').value = userEmail;
    }

    if (userName) {
        const nameParts = userName.split(' ');
        document.getElementById('firstName').value = nameParts[0] || '';
        document.getElementById('lastName').value = nameParts.slice(1).join(' ') || '';
    }
});
@endauth

// Update business hours status
function updateBusinessHoursStatus() {
    const now = new Date();
    const day = now.getDay(); // 0 = Sunday, 1 = Monday, etc.
    const hour = now.getHours();

    const statusIndicator = document.querySelector('.status-indicator');
    const statusText = document.querySelector('.status-text');

    let isOnline = false;

    if (day >= 1 && day <= 5) { // Monday to Friday
        isOnline = hour >= 9 && hour < 18; // 9 AM to 6 PM
    } else if (day === 6) { // Saturday
        isOnline = hour >= 10 && hour < 16; // 10 AM to 4 PM
    }

    if (isOnline) {
        statusIndicator.className = 'status-indicator online';
        statusText.textContent = "We're currently online";
    } else {
        statusIndicator.className = 'status-indicator offline';
        statusText.textContent = "We're currently offline";
    }
}

// Update status on page load
document.addEventListener('DOMContentLoaded', updateBusinessHoursStatus);

// Drag and drop file upload
const uploadArea = document.querySelector('.file-upload-area');

['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
    uploadArea.addEventListener(eventName, preventDefaults, false);
});

function preventDefaults(e) {
    e.preventDefault();
    e.stopPropagation();
}

['dragenter', 'dragover'].forEach(eventName => {
    uploadArea.addEventListener(eventName, highlight, false);
});

['dragleave', 'drop'].forEach(eventName => {
    uploadArea.addEventListener(eventName, unhighlight, false);
});

function highlight(e) {
    uploadArea.style.borderColor = '#667eea';
    uploadArea.style.background = '#edf2f7';
}

function unhighlight(e) {
    uploadArea.style.borderColor = '#cbd5e0';
    uploadArea.style.background = '#f7fafc';
}

uploadArea.addEventListener('drop', handleDrop, false);

function handleDrop(e) {
    const dt = e.dataTransfer;
    const files = dt.files;

    if (files.length > 0) {
        fileInput.files = files;
        fileInput.dispatchEvent(new Event('change'));
    }
}
</script>
@endpush
