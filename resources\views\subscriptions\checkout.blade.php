@extends('layouts.app')

@section('content')
<div class="py-12">
    <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6">
                <h1 class="text-2xl font-bold text-gray-900 mb-6">Complete Your Subscription</h1>
                
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Order Summary -->
                    <div class="order-2 lg:order-1">
                        <h2 class="text-lg font-semibold text-gray-900 mb-4">Order Summary</h2>
                        
                        <div class="bg-gray-50 rounded-lg p-6">
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="text-xl font-bold">{{ $plan->name }} Plan</h3>
                                @if($plan->is_featured)
                                    <span class="bg-blue-500 text-white text-xs px-2 py-1 rounded-full">Most Popular</span>
                                @endif
                            </div>
                            
                            <p class="text-gray-600 mb-4">{{ $plan->description }}</p>
                            
                            <div class="border-t pt-4 mb-4">
                                <div class="flex justify-between items-center mb-2">
                                    <span class="text-gray-600">Billing Cycle:</span>
                                    <span class="font-medium capitalize">{{ $billingCycle }}</span>
                                </div>
                                <div class="flex justify-between items-center mb-2">
                                    <span class="text-gray-600">Price:</span>
                                    <span class="font-medium">${{ number_format($plan->getPrice($billingCycle), 2) }}</span>
                                </div>
                                @if($billingCycle === 'yearly')
                                <div class="flex justify-between items-center mb-2 text-green-600">
                                    <span>You Save:</span>
                                    <span class="font-medium">${{ number_format(($plan->monthly_price * 12) - $plan->yearly_price, 2) }}</span>
                                </div>
                                @endif
                            </div>
                            
                            <div class="border-t pt-4">
                                <div class="flex justify-between items-center text-lg font-bold">
                                    <span>Total:</span>
                                    <span>${{ number_format($plan->getPrice($billingCycle), 2) }}</span>
                                </div>
                                <p class="text-sm text-gray-600 mt-1">
                                    Billed {{ $billingCycle }}. Cancel anytime.
                                </p>
                            </div>
                            
                            <!-- Features List -->
                            <div class="mt-6">
                                <h4 class="font-semibold mb-3">What's Included:</h4>
                                <ul class="space-y-2">
                                    @foreach($plan->features as $feature)
                                    <li class="flex items-center text-sm">
                                        <svg class="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                        {{ $feature }}
                                    </li>
                                    @endforeach
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Payment Form -->
                    <div class="order-1 lg:order-2">
                        <h2 class="text-lg font-semibold text-gray-900 mb-4">Payment Information</h2>
                        
                        <form id="payment-form" method="POST" action="{{ route('subscriptions.subscribe') }}">
                            @csrf
                            <input type="hidden" name="plan_id" value="{{ $plan->id }}">
                            <input type="hidden" name="billing_cycle" value="{{ $billingCycle }}">
                            <input type="hidden" name="payment_method" id="payment-method-input">
                            
                            <!-- Card Element -->
                            <div class="mb-6">
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    Card Information
                                </label>
                                <div id="card-element" class="p-3 border border-gray-300 rounded-md">
                                    <!-- Stripe Elements will create form elements here -->
                                </div>
                                <div id="card-errors" role="alert" class="text-red-600 text-sm mt-2"></div>
                            </div>
                            
                            <!-- Billing Information -->
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                                <div>
                                    <label for="billing_name" class="block text-sm font-medium text-gray-700 mb-1">
                                        Full Name
                                    </label>
                                    <input type="text" id="billing_name" name="billing_name" 
                                           value="{{ Auth::user()->full_name }}" required
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                </div>
                                
                                <div>
                                    <label for="billing_email" class="block text-sm font-medium text-gray-700 mb-1">
                                        Email
                                    </label>
                                    <input type="email" id="billing_email" name="billing_email" 
                                           value="{{ Auth::user()->email }}" required
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                </div>
                            </div>
                            
                            <!-- Terms and Conditions -->
                            <div class="mb-6">
                                <label class="flex items-center">
                                    <input type="checkbox" required class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                    <span class="ml-2 text-sm text-gray-600">
                                        I agree to the <a href="{{ route('terms') }}" class="text-blue-600 hover:text-blue-800">Terms of Service</a>
                                        and <a href="{{ route('privacy') }}" class="text-blue-600 hover:text-blue-800">Privacy Policy</a>
                                    </span>
                                </label>
                            </div>
                            
                            <!-- Submit Button -->
                            <button type="submit" id="submit-button" 
                                    class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-4 rounded-lg transition duration-300 disabled:opacity-50 disabled:cursor-not-allowed">
                                <span id="button-text">Subscribe Now</span>
                                <span id="spinner" class="hidden">
                                    <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    Processing...
                                </span>
                            </button>
                        </form>
                        
                        <!-- Security Notice -->
                        <div class="mt-6 text-center">
                            <div class="flex items-center justify-center text-sm text-gray-600">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                </svg>
                                Secured by Stripe. Your payment information is encrypted and secure.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://js.stripe.com/v3/"></script>
<script>
// Initialize Stripe
const stripe = Stripe('{{ config("cashier.key") }}');
const elements = stripe.elements();

// Create card element
const cardElement = elements.create('card', {
    style: {
        base: {
            fontSize: '16px',
            color: '#424770',
            '::placeholder': {
                color: '#aab7c4',
            },
        },
    },
});

cardElement.mount('#card-element');

// Handle form submission
const form = document.getElementById('payment-form');
const submitButton = document.getElementById('submit-button');
const buttonText = document.getElementById('button-text');
const spinner = document.getElementById('spinner');

form.addEventListener('submit', async (event) => {
    event.preventDefault();
    
    submitButton.disabled = true;
    buttonText.classList.add('hidden');
    spinner.classList.remove('hidden');
    
    const {token, error} = await stripe.createToken(cardElement);
    
    if (error) {
        // Show error to customer
        const errorElement = document.getElementById('card-errors');
        errorElement.textContent = error.message;
        
        submitButton.disabled = false;
        buttonText.classList.remove('hidden');
        spinner.classList.add('hidden');
    } else {
        // Submit the form with the token
        document.getElementById('payment-method-input').value = token.id;
        form.submit();
    }
});

// Handle real-time validation errors from the card Element
cardElement.on('change', ({error}) => {
    const displayError = document.getElementById('card-errors');
    if (error) {
        displayError.textContent = error.message;
    } else {
        displayError.textContent = '';
    }
});
</script>
@endsection
