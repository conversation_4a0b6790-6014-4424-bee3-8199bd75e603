@extends('layouts.admin')

@section('title', '- Category Management')

@section('content')
<div class="p-6">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Category Management</h1>
                <p class="mt-1 text-sm text-gray-600">Organize courses with categories and subcategories</p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('admin.categories.create') }}" 
                   class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition duration-150">
                    Add New Category
                </a>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14-7H5a2 2 0 00-2 2v14a2 2 0 002 2h14a2 2 0 002-2V6a2 2 0 00-2-2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Categories</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ number_format($stats['total_categories']) }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Active</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ number_format($stats['active_categories']) }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">With Courses</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ number_format($stats['categories_with_courses']) }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Categories Table -->
    <div class="bg-white shadow overflow-hidden sm:rounded-md">
        <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h3 class="text-lg font-medium text-gray-900">Categories</h3>
            <div class="flex space-x-2">
                <button onclick="toggleBulkActions()" class="text-sm text-gray-600 hover:text-gray-900">
                    Bulk Actions
                </button>
            </div>
        </div>
        
        @if($categories->count() > 0)
            <!-- Bulk Actions Form -->
            <form id="bulkForm" method="POST" action="{{ route('admin.categories.bulk-action') }}" class="hidden">
                @csrf
                <div class="px-6 py-3 bg-gray-50 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <select name="action" class="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="">Select Action</option>
                                <option value="activate">Activate</option>
                                <option value="deactivate">Deactivate</option>
                                <option value="feature">Feature</option>
                                <option value="unfeature">Unfeature</option>
                                <option value="delete">Delete</option>
                            </select>
                            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-1 px-3 rounded text-sm">
                                Apply
                            </button>
                        </div>
                        <button type="button" onclick="toggleBulkActions()" class="text-sm text-gray-600 hover:text-gray-900">
                            Cancel
                        </button>
                    </div>
                </div>
            </form>

            <!-- Categories List -->
            <div class="divide-y divide-gray-200" id="categoriesList">
                @foreach($categories as $category)
                    <div class="px-6 py-4 flex items-center justify-between category-item" data-id="{{ $category->id }}">
                        <div class="flex items-center">
                            <div class="bulk-checkbox hidden mr-4">
                                <input type="checkbox" name="category_ids[]" value="{{ $category->id }}" 
                                       class="category-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            </div>
                            
                            <div class="flex items-center">
                                @if($category->image)
                                    <img src="{{ asset('storage/' . $category->image) }}" 
                                         alt="{{ $category->name }}" 
                                         class="h-12 w-12 rounded-lg object-cover">
                                @else
                                    <div class="h-12 w-12 rounded-lg flex items-center justify-center"
                                         style="background-color: {{ $category->color ?? '#6B7280' }}">
                                        @if($category->icon)
                                            <i class="{{ $category->icon }} text-white text-lg"></i>
                                        @else
                                            <span class="text-white font-medium">{{ substr($category->name, 0, 2) }}</span>
                                        @endif
                                    </div>
                                @endif
                                
                                <div class="ml-4">
                                    <div class="flex items-center">
                                        <h4 class="text-sm font-medium text-gray-900">{{ $category->name }}</h4>
                                        @if($category->is_featured)
                                            <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                Featured
                                            </span>
                                        @endif
                                        @if(!$category->is_active)
                                            <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                Inactive
                                            </span>
                                        @endif
                                    </div>
                                    <p class="text-sm text-gray-500">{{ $category->description }}</p>
                                    <div class="flex items-center mt-1 text-xs text-gray-400">
                                        <span>{{ $category->courses_count }} courses</span>
                                        <span class="mx-2">•</span>
                                        <span>Order: {{ $category->sort_order }}</span>
                                        @if($category->parent)
                                            <span class="mx-2">•</span>
                                            <span>Parent: {{ $category->parent->name }}</span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="flex items-center space-x-2">
                            <a href="{{ route('admin.categories.show', $category) }}" 
                               class="text-blue-600 hover:text-blue-900 text-sm font-medium">
                                View
                            </a>
                            <a href="{{ route('admin.categories.edit', $category) }}" 
                               class="text-indigo-600 hover:text-indigo-900 text-sm font-medium">
                                Edit
                            </a>
                            
                            <form method="POST" action="{{ route('admin.categories.toggle-active', $category) }}" class="inline">
                                @csrf
                                <button type="submit" class="text-yellow-600 hover:text-yellow-900 text-sm font-medium">
                                    {{ $category->is_active ? 'Deactivate' : 'Activate' }}
                                </button>
                            </form>
                            
                            <form method="POST" action="{{ route('admin.categories.toggle-featured', $category) }}" class="inline">
                                @csrf
                                <button type="submit" class="text-purple-600 hover:text-purple-900 text-sm font-medium">
                                    {{ $category->is_featured ? 'Unfeature' : 'Feature' }}
                                </button>
                            </form>
                            
                            @if($category->courses_count == 0 && $category->children->count() == 0)
                                <form method="POST" action="{{ route('admin.categories.destroy', $category) }}" class="inline">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" 
                                            onclick="return confirm('Are you sure you want to delete this category?')"
                                            class="text-red-600 hover:text-red-900 text-sm font-medium">
                                        Delete
                                    </button>
                                </form>
                            @endif
                        </div>
                    </div>

                    <!-- Show subcategories if any -->
                    @if($category->children->count() > 0)
                        @foreach($category->children as $child)
                            <div class="px-6 py-3 ml-8 bg-gray-50 flex items-center justify-between category-item" data-id="{{ $child->id }}">
                                <div class="flex items-center">
                                    <div class="bulk-checkbox hidden mr-4">
                                        <input type="checkbox" name="category_ids[]" value="{{ $child->id }}" 
                                               class="category-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                    </div>
                                    
                                    <div class="flex items-center">
                                        @if($child->image)
                                            <img src="{{ asset('storage/' . $child->image) }}" 
                                                 alt="{{ $child->name }}" 
                                                 class="h-8 w-8 rounded object-cover">
                                        @else
                                            <div class="h-8 w-8 rounded flex items-center justify-center"
                                                 style="background-color: {{ $child->color ?? '#9CA3AF' }}">
                                                @if($child->icon)
                                                    <i class="{{ $child->icon }} text-white text-sm"></i>
                                                @else
                                                    <span class="text-white text-xs font-medium">{{ substr($child->name, 0, 2) }}</span>
                                                @endif
                                            </div>
                                        @endif
                                        
                                        <div class="ml-3">
                                            <div class="flex items-center">
                                                <h5 class="text-sm font-medium text-gray-900">{{ $child->name }}</h5>
                                                @if($child->is_featured)
                                                    <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                        Featured
                                                    </span>
                                                @endif
                                                @if(!$child->is_active)
                                                    <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                        Inactive
                                                    </span>
                                                @endif
                                            </div>
                                            <p class="text-xs text-gray-500">{{ $child->description }}</p>
                                            <div class="flex items-center mt-1 text-xs text-gray-400">
                                                <span>{{ $child->courses_count }} courses</span>
                                                <span class="mx-2">•</span>
                                                <span>Order: {{ $child->sort_order }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="flex items-center space-x-2">
                                    <a href="{{ route('admin.categories.show', $child) }}" 
                                       class="text-blue-600 hover:text-blue-900 text-xs font-medium">
                                        View
                                    </a>
                                    <a href="{{ route('admin.categories.edit', $child) }}" 
                                       class="text-indigo-600 hover:text-indigo-900 text-xs font-medium">
                                        Edit
                                    </a>
                                    
                                    @if($child->courses_count == 0)
                                        <form method="POST" action="{{ route('admin.categories.destroy', $child) }}" class="inline">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" 
                                                    onclick="return confirm('Are you sure you want to delete this subcategory?')"
                                                    class="text-red-600 hover:text-red-900 text-xs font-medium">
                                                Delete
                                            </button>
                                        </form>
                                    @endif
                                </div>
                            </div>
                        @endforeach
                    @endif
                @endforeach
            </div>
        @else
            <div class="px-6 py-12 text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14-7H5a2 2 0 00-2 2v14a2 2 0 002 2h14a2 2 0 002-2V6a2 2 0 00-2-2z"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No categories found</h3>
                <p class="mt-1 text-sm text-gray-500">Get started by creating your first category.</p>
                <div class="mt-6">
                    <a href="{{ route('admin.categories.create') }}" 
                       class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                        Add New Category
                    </a>
                </div>
            </div>
        @endif
    </div>
</div>

<script>
function toggleBulkActions() {
    const bulkForm = document.getElementById('bulkForm');
    const checkboxes = document.querySelectorAll('.bulk-checkbox');
    
    if (bulkForm.classList.contains('hidden')) {
        bulkForm.classList.remove('hidden');
        checkboxes.forEach(cb => cb.classList.remove('hidden'));
    } else {
        bulkForm.classList.add('hidden');
        checkboxes.forEach(cb => cb.classList.add('hidden'));
        // Uncheck all checkboxes
        document.querySelectorAll('.category-checkbox').forEach(cb => cb.checked = false);
    }
}

// Add form to bulk actions
document.getElementById('bulkForm').addEventListener('submit', function(e) {
    const checkedBoxes = document.querySelectorAll('.category-checkbox:checked');
    if (checkedBoxes.length === 0) {
        e.preventDefault();
        alert('Please select at least one category.');
        return;
    }
    
    // Add checkboxes to form
    checkedBoxes.forEach(cb => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'category_ids[]';
        input.value = cb.value;
        this.appendChild(input);
    });
});
</script>
@endsection
