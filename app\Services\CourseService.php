<?php

namespace App\Services;

use App\Models\Course;
use App\Models\User;
use App\Models\UserProgress;
use App\Models\Certificate;
use App\Notifications\CourseEnrollmentNotification;
use App\Notifications\CourseCompletionNotification;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class CourseService
{
    /**
     * Create a new course.
     */
    public function createCourse(array $data): Course
    {
        // Handle thumbnail upload
        if (isset($data['thumbnail'])) {
            $data['thumbnail'] = $this->uploadThumbnail($data['thumbnail']);
        }

        // Generate slug if not provided
        if (empty($data['slug'])) {
            $data['slug'] = $this->generateUniqueSlug($data['title']);
        }

        // Set default values
        $data['is_published'] = $data['is_published'] ?? false;
        $data['is_featured'] = $data['is_featured'] ?? false;
        $data['is_free'] = $data['is_free'] ?? false;
        $data['sort_order'] = $data['sort_order'] ?? 0;

        return Course::create($data);
    }

    /**
     * Update an existing course.
     */
    public function updateCourse(Course $course, array $data): Course
    {
        // Handle thumbnail upload
        if (isset($data['thumbnail'])) {
            // Delete old thumbnail
            if ($course->thumbnail) {
                Storage::disk('public')->delete($course->thumbnail);
            }
            $data['thumbnail'] = $this->uploadThumbnail($data['thumbnail']);
        }

        // Update slug if title changed
        if (isset($data['title']) && $data['title'] !== $course->title) {
            if (empty($data['slug'])) {
                $data['slug'] = $this->generateUniqueSlug($data['title'], $course->id);
            }
        }

        $course->update($data);
        return $course->fresh();
    }

    /**
     * Enroll user in a course.
     */
    public function enrollUser(User $user, Course $course): bool
    {
        // Check if user already enrolled
        if ($this->isUserEnrolled($user, $course)) {
            return false;
        }

        // Check if course requires subscription
        if (!$course->is_free && !$user->hasActiveSubscription()) {
            return false;
        }

        // Create initial progress records for all lessons
        foreach ($course->lessons as $lesson) {
            UserProgress::firstOrCreate([
                'user_id' => $user->id,
                'course_id' => $course->id,
                'lesson_id' => $lesson->id,
            ], [
                'is_completed' => false,
                'completion_percentage' => 0,
                'watch_time_seconds' => 0,
            ]);
        }

        // Send enrollment notification
        $user->notify(new CourseEnrollmentNotification($course));

        return true;
    }

    /**
     * Check if user is enrolled in a course.
     */
    public function isUserEnrolled(User $user, Course $course): bool
    {
        return UserProgress::where('user_id', $user->id)
            ->where('course_id', $course->id)
            ->exists();
    }

    /**
     * Get user's progress in a course.
     */
    public function getUserCourseProgress(User $user, Course $course): array
    {
        $progress = UserProgress::where('user_id', $user->id)
            ->where('course_id', $course->id)
            ->with('lesson')
            ->get();

        $totalLessons = $course->lessons()->where('is_published', true)->count();
        $completedLessons = $progress->where('is_completed', true)->count();
        $progressPercentage = $totalLessons > 0 ? ($completedLessons / $totalLessons) * 100 : 0;

        return [
            'total_lessons' => $totalLessons,
            'completed_lessons' => $completedLessons,
            'progress_percentage' => round($progressPercentage, 2),
            'lessons' => $progress,
            'is_completed' => $progressPercentage >= 100,
        ];
    }

    /**
     * Mark course as completed and issue certificate.
     */
    public function completeCourse(User $user, Course $course): ?Certificate
    {
        $progress = $this->getUserCourseProgress($user, $course);
        
        if (!$progress['is_completed']) {
            return null;
        }

        // Check if certificate already exists
        $existingCertificate = Certificate::where('user_id', $user->id)
            ->where('course_id', $course->id)
            ->first();

        if ($existingCertificate) {
            return $existingCertificate;
        }

        // Create certificate
        $certificate = Certificate::create([
            'user_id' => $user->id,
            'course_id' => $course->id,
            'certificate_number' => $this->generateCertificateNumber(),
            'issued_at' => now(),
            'verification_url' => route('certificates.verify', ['certificate' => 'PLACEHOLDER']),
        ]);

        // Update verification URL with actual certificate ID
        $certificate->update([
            'verification_url' => route('certificates.verify', $certificate),
        ]);

        // Send completion notification
        $user->notify(new CourseCompletionNotification($course, $certificate));

        return $certificate;
    }

    /**
     * Get course statistics.
     */
    public function getCourseStatistics(Course $course): array
    {
        $enrolledUsers = UserProgress::where('course_id', $course->id)
            ->distinct('user_id')
            ->count();

        $completedUsers = UserProgress::where('course_id', $course->id)
            ->where('is_completed', true)
            ->distinct('user_id')
            ->count();

        $averageProgress = UserProgress::where('course_id', $course->id)
            ->avg('completion_percentage') ?? 0;

        $totalWatchTime = UserProgress::where('course_id', $course->id)
            ->sum('watch_time_seconds');

        $ratings = $course->courseRatings;
        $averageRating = $ratings->avg('rating') ?? 0;

        return [
            'enrolled_users' => $enrolledUsers,
            'completed_users' => $completedUsers,
            'completion_rate' => $enrolledUsers > 0 ? ($completedUsers / $enrolledUsers) * 100 : 0,
            'average_progress' => round($averageProgress, 2),
            'total_watch_time_hours' => round($totalWatchTime / 3600, 2),
            'total_ratings' => $ratings->count(),
            'average_rating' => round($averageRating, 1),
            'rating_distribution' => [
                5 => $ratings->where('rating', 5)->count(),
                4 => $ratings->where('rating', 4)->count(),
                3 => $ratings->where('rating', 3)->count(),
                2 => $ratings->where('rating', 2)->count(),
                1 => $ratings->where('rating', 1)->count(),
            ],
        ];
    }

    /**
     * Get recommended courses for a user.
     */
    public function getRecommendedCourses(User $user, int $limit = 6): \Illuminate\Database\Eloquent\Collection
    {
        // Get user's enrolled courses
        $enrolledCourseIds = UserProgress::where('user_id', $user->id)
            ->distinct('course_id')
            ->pluck('course_id');

        // Get user's completed categories
        $completedCategories = Course::whereIn('id', $enrolledCourseIds)
            ->pluck('category_id')
            ->unique();

        // Recommend courses from same categories or popular courses
        return Course::where('is_published', true)
            ->whereNotIn('id', $enrolledCourseIds)
            ->where(function ($query) use ($completedCategories) {
                $query->whereIn('category_id', $completedCategories)
                    ->orWhere('is_featured', true);
            })
            ->withCount(['userProgress as enrollment_count' => function ($query) {
                $query->distinct('user_id');
            }])
            ->orderByDesc('enrollment_count')
            ->orderByDesc('is_featured')
            ->limit($limit)
            ->get();
    }

    /**
     * Upload course thumbnail.
     */
    protected function uploadThumbnail($file): string
    {
        return $file->store('course-thumbnails', 'public');
    }

    /**
     * Generate unique slug for course.
     */
    protected function generateUniqueSlug(string $title, ?int $excludeId = null): string
    {
        $slug = Str::slug($title);
        $originalSlug = $slug;
        $counter = 1;

        while (true) {
            $query = Course::where('slug', $slug);
            if ($excludeId) {
                $query->where('id', '!=', $excludeId);
            }

            if (!$query->exists()) {
                break;
            }

            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Generate unique certificate number.
     */
    protected function generateCertificateNumber(): string
    {
        do {
            $number = 'TRW-' . strtoupper(Str::random(8));
        } while (Certificate::where('certificate_number', $number)->exists());

        return $number;
    }

    /**
     * Delete course and related data.
     */
    public function deleteCourse(Course $course): bool
    {
        try {
            // Delete thumbnail
            if ($course->thumbnail) {
                Storage::disk('public')->delete($course->thumbnail);
            }

            // Delete related data
            UserProgress::where('course_id', $course->id)->delete();
            Certificate::where('course_id', $course->id)->delete();
            
            // Delete course
            $course->delete();

            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Duplicate a course.
     */
    public function duplicateCourse(Course $course): Course
    {
        $newCourse = $course->replicate();
        $newCourse->title = $course->title . ' (Copy)';
        $newCourse->slug = $this->generateUniqueSlug($newCourse->title);
        $newCourse->is_published = false;
        $newCourse->is_featured = false;
        $newCourse->save();

        // Duplicate lessons
        foreach ($course->lessons as $lesson) {
            $newLesson = $lesson->replicate();
            $newLesson->course_id = $newCourse->id;
            $newLesson->save();
        }

        return $newCourse;
    }
}
