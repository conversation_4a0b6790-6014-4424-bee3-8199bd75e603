@extends('layouts.dashboard')

@section('content')
<div class="py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Welcome Section -->
        <div class="bg-gradient-to-r from-blue-600 to-purple-700 text-white rounded-lg p-6 mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold mb-2">
                        Welcome back, {{ $user->full_name }}! 👋
                    </h1>
                    <p class="text-blue-100">
                        Ready to continue your journey to success?
                    </p>
                </div>
                <div class="text-right">
                    @if($currentStreak > 0)
                        <div class="bg-white bg-opacity-20 rounded-lg p-3">
                            <div class="text-2xl font-bold">🔥 {{ $currentStreak }}</div>
                            <div class="text-sm text-blue-100">Day Streak</div>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Stats Overview -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="bg-blue-100 rounded-full p-3 mr-4">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="text-2xl font-bold text-gray-900">{{ $enrolledCourses->count() }}</p>
                        <p class="text-gray-600 text-sm">Courses Enrolled</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="bg-green-100 rounded-full p-3 mr-4">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="text-2xl font-bold text-gray-900">{{ $totalLessonsCompleted }}</p>
                        <p class="text-gray-600 text-sm">Lessons Completed</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="bg-purple-100 rounded-full p-3 mr-4">
                        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="text-2xl font-bold text-gray-900">{{ round($totalWatchTime / 3600, 1) }}h</p>
                        <p class="text-gray-600 text-sm">Watch Time</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="bg-yellow-100 rounded-full p-3 mr-4">
                        <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="text-2xl font-bold text-gray-900">{{ $currentStreak }}</p>
                        <p class="text-gray-600 text-sm">Day Streak</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-8">
                <!-- Subscription Status -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Subscription Status</h3>
                    
                    @if($subscription)
                        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="flex items-center">
                                        <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                        <span class="text-green-800 font-medium">{{ $subscription->subscriptionPlan->name }} Plan</span>
                                    </div>
                                    <p class="text-green-700 text-sm mt-1">
                                        Active until {{ $subscription->current_period_end->format('M d, Y') }}
                                    </p>
                                </div>
                                <a href="{{ route('subscriptions.index') }}" class="text-green-600 hover:text-green-800 text-sm font-medium">
                                    Manage →
                                </a>
                            </div>
                        </div>
                    @else
                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="flex items-center">
                                        <svg class="w-5 h-5 text-yellow-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                        </svg>
                                        <span class="text-yellow-800 font-medium">No Active Subscription</span>
                                    </div>
                                    <p class="text-yellow-700 text-sm mt-1">
                                        Subscribe to access premium content
                                    </p>
                                </div>
                                <a href="{{ route('subscriptions.index') }}" class="bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium py-2 px-4 rounded">
                                    View Plans
                                </a>
                            </div>
                        </div>
                    @endif
                </div>

                <!-- Recent Courses -->
                @if($recentCourses->count() > 0)
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">Continue Learning</h3>
                        <a href="{{ route('dashboard.courses') }}" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                            View All →
                        </a>
                    </div>
                    
                    <div class="space-y-4">
                        @foreach($recentCourses->take(3) as $course)
                        <div class="flex items-center p-4 bg-gray-50 rounded-lg">
                            @if($course->thumbnail)
                                <img src="{{ $course->thumbnail }}" alt="{{ $course->title }}" class="w-16 h-16 object-cover rounded mr-4">
                            @else
                                <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded mr-4 flex items-center justify-center">
                                    <span class="text-white font-bold">{{ substr($course->title, 0, 1) }}</span>
                                </div>
                            @endif
                            
                            <div class="flex-1">
                                <h4 class="font-medium text-gray-900">{{ $course->title }}</h4>
                                <p class="text-sm text-gray-600">{{ $course->mentor->name }}</p>
                                
                                @php
                                    $totalLessons = $course->publishedLessons->count();
                                    $completedLessons = $course->userProgress->where('is_completed', true)->count();
                                    $progressPercentage = $totalLessons > 0 ? ($completedLessons / $totalLessons) * 100 : 0;
                                @endphp
                                
                                <div class="mt-2">
                                    <div class="flex justify-between text-xs text-gray-600 mb-1">
                                        <span>{{ $completedLessons }}/{{ $totalLessons }} lessons</span>
                                        <span>{{ number_format($progressPercentage, 0) }}%</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-1">
                                        <div class="bg-blue-600 h-1 rounded-full" style="width: {{ $progressPercentage }}%"></div>
                                    </div>
                                </div>
                            </div>
                            
                            <a href="{{ route('courses.show', $course) }}" 
                               class="ml-4 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm font-medium transition duration-300">
                                Continue
                            </a>
                        </div>
                        @endforeach
                    </div>
                </div>
                @endif

                <!-- Recent Achievements -->
                @if($recentAchievements->count() > 0)
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Achievements</h3>
                    
                    <div class="space-y-3">
                        @foreach($recentAchievements as $achievement)
                        <div class="flex items-center p-3 bg-green-50 rounded-lg">
                            <div class="text-2xl mr-3">{{ $achievement['icon'] }}</div>
                            <div class="flex-1">
                                <h4 class="font-medium text-gray-900">{{ $achievement['title'] }}</h4>
                                <p class="text-sm text-gray-600">{{ $achievement['description'] }}</p>
                                <p class="text-xs text-gray-500 mt-1">{{ $achievement['date']->diffForHumans() }}</p>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
                @endif
            </div>
            
            <!-- Sidebar -->
            <div class="lg:col-span-1 space-y-6">
                <!-- Quick Actions -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                    
                    <div class="space-y-3">
                        <a href="{{ route('courses.index') }}" 
                           class="flex items-center p-3 bg-blue-50 hover:bg-blue-100 rounded-lg transition duration-300">
                            <svg class="w-5 h-5 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                            </svg>
                            <span class="font-medium text-gray-900">Browse Courses</span>
                        </a>
                        
                        <a href="{{ route('dashboard.stats') }}" 
                           class="flex items-center p-3 bg-green-50 hover:bg-green-100 rounded-lg transition duration-300">
                            <svg class="w-5 h-5 text-green-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                            <span class="font-medium text-gray-900">View Statistics</span>
                        </a>
                        
                        <a href="{{ route('subscriptions.index') }}" 
                           class="flex items-center p-3 bg-purple-50 hover:bg-purple-100 rounded-lg transition duration-300">
                            <svg class="w-5 h-5 text-purple-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                            </svg>
                            <span class="font-medium text-gray-900">Manage Subscription</span>
                        </a>
                    </div>
                </div>
                
                <!-- Recommended Courses -->
                @if($recommendedCourses->count() > 0)
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Recommended for You</h3>
                    
                    <div class="space-y-4">
                        @foreach($recommendedCourses->take(3) as $course)
                        <div class="border border-gray-200 rounded-lg p-3">
                            <div class="flex items-center mb-2">
                                @if($course->thumbnail)
                                    <img src="{{ $course->thumbnail }}" alt="{{ $course->title }}" class="w-10 h-10 object-cover rounded mr-3">
                                @else
                                    <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded mr-3 flex items-center justify-center">
                                        <span class="text-white text-sm font-bold">{{ substr($course->title, 0, 1) }}</span>
                                    </div>
                                @endif
                                <div class="flex-1">
                                    <h4 class="font-medium text-sm text-gray-900">{{ $course->title }}</h4>
                                    <p class="text-xs text-gray-600">{{ $course->mentor->name }}</p>
                                </div>
                            </div>
                            <a href="{{ route('courses.show', $course) }}" 
                               class="block w-full text-center bg-gray-100 hover:bg-gray-200 text-gray-800 py-2 rounded text-sm font-medium transition duration-300">
                                View Course
                            </a>
                        </div>
                        @endforeach
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
