@extends('layouts.app')

@section('title', $post->title . ' - Community')

@section('content')
<div class="post-container">
    <div class="container">
        <div class="row">
            <!-- Main Content -->
            <div class="col-lg-9">
                <!-- Breadcrumb -->
                <nav aria-label="breadcrumb" class="post-breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ route('home') }}">Home</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('community.index') }}">Community</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('community.index', ['category' => $post->category->slug]) }}">{{ $post->category->name }}</a></li>
                        <li class="breadcrumb-item active">{{ Str::limit($post->title, 50) }}</li>
                    </ol>
                </nav>

                <!-- Post Content -->
                <div class="post-main">
                    <div class="post-header">
                        <h1 class="post-title">{{ $post->title }}</h1>
                        <div class="post-meta">
                            <div class="post-author">
                                <img src="{{ $post->user->avatar ? asset('storage/' . $post->user->avatar) : asset('images/default-avatar.png') }}" 
                                     alt="{{ $post->user->name }}" class="author-avatar">
                                <div class="author-info">
                                    <span class="author-name">{{ $post->user->name }}</span>
                                    <div class="author-details">
                                        <span class="author-reputation">{{ $post->user->reputation }} reputation</span>
                                        <span class="post-time">{{ $post->created_at->format('M d, Y \a\t g:i A') }}</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="post-actions">
                                @auth
                                    @if(auth()->id() === $post->user_id)
                                    <button class="btn btn-sm btn-outline-secondary" onclick="editPost()">
                                        <i class="fas fa-edit"></i> Edit
                                    </button>
                                    @endif
                                    <button class="btn btn-sm btn-outline-secondary" onclick="sharePost()">
                                        <i class="fas fa-share-alt"></i> Share
                                    </button>
                                    <button class="btn btn-sm btn-outline-secondary" onclick="reportPost()">
                                        <i class="fas fa-flag"></i> Report
                                    </button>
                                @endauth
                            </div>
                        </div>
                        
                        <div class="post-tags">
                            @foreach($post->tags as $tag)
                            <a href="{{ route('community.index', ['tag' => $tag->slug]) }}" class="post-tag">
                                {{ $tag->name }}
                            </a>
                            @endforeach
                        </div>
                    </div>

                    <div class="post-body">
                        <div class="post-votes">
                            <button class="vote-btn vote-up {{ $post->user_vote === 'up' ? 'active' : '' }}" 
                                    onclick="votePost({{ $post->id }}, 'up')">
                                <i class="fas fa-chevron-up"></i>
                            </button>
                            <div class="vote-count">{{ $post->votes_count }}</div>
                            <button class="vote-btn vote-down {{ $post->user_vote === 'down' ? 'active' : '' }}" 
                                    onclick="votePost({{ $post->id }}, 'down')">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                            
                            <button class="bookmark-btn {{ $post->is_bookmarked ? 'active' : '' }}" 
                                    onclick="toggleBookmark({{ $post->id }})">
                                <i class="fas fa-bookmark"></i>
                            </button>
                        </div>

                        <div class="post-content">
                            <div class="content-text">
                                {!! $post->content !!}
                            </div>
                            
                            @if($post->attachments->count() > 0)
                            <div class="post-attachments">
                                <h5>Attachments</h5>
                                <div class="attachments-list">
                                    @foreach($post->attachments as $attachment)
                                    <div class="attachment-item">
                                        <div class="attachment-icon">
                                            <i class="fas fa-{{ $attachment->getIconClass() }}"></i>
                                        </div>
                                        <div class="attachment-info">
                                            <span class="attachment-name">{{ $attachment->original_name }}</span>
                                            <span class="attachment-size">{{ $attachment->getFormattedSize() }}</span>
                                        </div>
                                        <a href="{{ route('community.attachment', $attachment) }}" 
                                           class="btn btn-sm btn-outline-primary" target="_blank">
                                            <i class="fas fa-download"></i>
                                        </a>
                                    </div>
                                    @endforeach
                                </div>
                            </div>
                            @endif
                        </div>
                    </div>

                    <div class="post-footer">
                        <div class="post-stats">
                            <span class="stat-item">
                                <i class="fas fa-eye"></i>
                                {{ $post->views_count }} views
                            </span>
                            <span class="stat-item">
                                <i class="fas fa-thumbs-up"></i>
                                {{ $post->votes_count }} votes
                            </span>
                            <span class="stat-item">
                                <i class="fas fa-comments"></i>
                                {{ $post->replies_count }} replies
                            </span>
                            @if($post->is_solved)
                            <span class="stat-item solved">
                                <i class="fas fa-check-circle"></i>
                                Solved
                            </span>
                            @endif
                        </div>
                        
                        <div class="post-updated">
                            @if($post->updated_at > $post->created_at)
                            <small class="text-muted">
                                Last edited {{ $post->updated_at->diffForHumans() }}
                            </small>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Replies Section -->
                <div class="replies-section">
                    <div class="replies-header">
                        <h3>{{ $post->replies_count }} {{ Str::plural('Reply', $post->replies_count) }}</h3>
                        <div class="replies-sort">
                            <select class="form-control form-control-sm" onchange="sortReplies(this.value)">
                                <option value="oldest">Oldest first</option>
                                <option value="newest">Newest first</option>
                                <option value="votes">Most voted</option>
                            </select>
                        </div>
                    </div>

                    <div class="replies-list" id="repliesList">
                        @forelse($replies as $reply)
                        <div class="reply-item {{ $reply->is_solution ? 'solution' : '' }}" id="reply-{{ $reply->id }}">
                            <div class="reply-votes">
                                <button class="vote-btn vote-up {{ $reply->user_vote === 'up' ? 'active' : '' }}" 
                                        onclick="voteReply({{ $reply->id }}, 'up')">
                                    <i class="fas fa-chevron-up"></i>
                                </button>
                                <div class="vote-count">{{ $reply->votes_count }}</div>
                                <button class="vote-btn vote-down {{ $reply->user_vote === 'down' ? 'active' : '' }}" 
                                        onclick="voteReply({{ $reply->id }}, 'down')">
                                    <i class="fas fa-chevron-down"></i>
                                </button>
                                
                                @if(auth()->id() === $post->user_id && !$post->is_solved)
                                <button class="solution-btn" onclick="markAsSolution({{ $reply->id }})" 
                                        title="Mark as solution">
                                    <i class="fas fa-check"></i>
                                </button>
                                @endif
                            </div>

                            <div class="reply-content">
                                @if($reply->is_solution)
                                <div class="solution-badge">
                                    <i class="fas fa-check-circle"></i>
                                    Accepted Solution
                                </div>
                                @endif

                                <div class="reply-header">
                                    <div class="reply-author">
                                        <img src="{{ $reply->user->avatar ? asset('storage/' . $reply->user->avatar) : asset('images/default-avatar.png') }}" 
                                             alt="{{ $reply->user->name }}" class="author-avatar">
                                        <div class="author-info">
                                            <span class="author-name">{{ $reply->user->name }}</span>
                                            <span class="author-reputation">{{ $reply->user->reputation }} rep</span>
                                        </div>
                                    </div>
                                    
                                    <div class="reply-meta">
                                        <span class="reply-time">{{ $reply->created_at->diffForHumans() }}</span>
                                        @auth
                                            @if(auth()->id() === $reply->user_id)
                                            <button class="btn btn-sm btn-link" onclick="editReply({{ $reply->id }})">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            @endif
                                            <button class="btn btn-sm btn-link" onclick="replyToReply({{ $reply->id }})">
                                                <i class="fas fa-reply"></i>
                                            </button>
                                        @endauth
                                    </div>
                                </div>

                                <div class="reply-body">
                                    {!! $reply->content !!}
                                </div>

                                <!-- Nested replies -->
                                @if($reply->replies->count() > 0)
                                <div class="nested-replies">
                                    @foreach($reply->replies as $nestedReply)
                                    <div class="nested-reply">
                                        <div class="nested-reply-author">
                                            <img src="{{ $nestedReply->user->avatar ? asset('storage/' . $nestedReply->user->avatar) : asset('images/default-avatar.png') }}" 
                                                 alt="{{ $nestedReply->user->name }}" class="author-avatar-small">
                                            <span class="author-name">{{ $nestedReply->user->name }}</span>
                                            <span class="reply-time">{{ $nestedReply->created_at->diffForHumans() }}</span>
                                        </div>
                                        <div class="nested-reply-content">
                                            {!! $nestedReply->content !!}
                                        </div>
                                    </div>
                                    @endforeach
                                </div>
                                @endif
                            </div>
                        </div>
                        @empty
                        <div class="no-replies">
                            <div class="no-replies-icon">
                                <i class="fas fa-comments"></i>
                            </div>
                            <h4>No replies yet</h4>
                            <p>Be the first to reply to this post!</p>
                        </div>
                        @endforelse
                    </div>

                    <!-- Reply Form -->
                    @auth
                    <div class="reply-form-section">
                        <h4>Your Reply</h4>
                        <form id="replyForm" onsubmit="submitReply(event)">
                            <div class="form-group">
                                <textarea id="replyContent" class="form-control" rows="6" 
                                          placeholder="Write your reply..." required></textarea>
                            </div>
                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-paper-plane"></i> Post Reply
                                </button>
                                <button type="button" class="btn btn-outline-secondary" onclick="previewReply()">
                                    <i class="fas fa-eye"></i> Preview
                                </button>
                            </div>
                        </form>
                    </div>
                    @else
                    <div class="login-prompt">
                        <div class="login-prompt-content">
                            <h4>Join the discussion</h4>
                            <p>You need to be logged in to reply to this post.</p>
                            <a href="{{ route('login') }}" class="btn btn-primary">
                                <i class="fas fa-sign-in-alt"></i> Login to Reply
                            </a>
                        </div>
                    </div>
                    @endauth
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-3">
                <div class="post-sidebar">
                    <!-- Related Posts -->
                    <div class="sidebar-section">
                        <h5>Related Posts</h5>
                        <div class="related-posts">
                            @foreach($relatedPosts as $relatedPost)
                            <div class="related-post">
                                <h6><a href="{{ route('community.post', $relatedPost) }}">{{ $relatedPost->title }}</a></h6>
                                <div class="related-post-meta">
                                    <span class="votes">{{ $relatedPost->votes_count }} votes</span>
                                    <span class="replies">{{ $relatedPost->replies_count }} replies</span>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>

                    <!-- Category Info -->
                    <div class="sidebar-section">
                        <h5>{{ $post->category->name }}</h5>
                        <p>{{ $post->category->description }}</p>
                        <div class="category-stats">
                            <div class="stat">
                                <span class="stat-number">{{ $post->category->posts_count }}</span>
                                <span class="stat-label">Posts</span>
                            </div>
                            <div class="stat">
                                <span class="stat-number">{{ $post->category->members_count }}</span>
                                <span class="stat-label">Members</span>
                            </div>
                        </div>
                        <a href="{{ route('community.index', ['category' => $post->category->slug]) }}" 
                           class="btn btn-sm btn-outline-primary">
                            View All Posts
                        </a>
                    </div>

                    <!-- Community Guidelines -->
                    <div class="sidebar-section">
                        <h5>Community Guidelines</h5>
                        <ul class="guidelines-list">
                            <li>Be respectful and constructive</li>
                            <li>Stay on topic</li>
                            <li>Search before posting</li>
                            <li>Use clear, descriptive titles</li>
                            <li>Mark solutions when found</li>
                        </ul>
                        <a href="{{ route('community.guidelines') }}" class="btn btn-sm btn-outline-secondary">
                            Read Full Guidelines
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
