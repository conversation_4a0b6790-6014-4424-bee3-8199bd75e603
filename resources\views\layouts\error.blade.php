<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title') - {{ config('app.name', 'The Real World') }}</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <!-- Additional Styles -->
    @stack('styles')
</head>
<body class="font-sans antialiased bg-gradient-to-br from-blue-50 via-white to-purple-50">
    <div class="min-h-screen flex flex-col justify-center items-center px-4 sm:px-6 lg:px-8">
        <!-- Logo -->
        <div class="mb-8">
            <a href="{{ route('home') }}" class="flex items-center">
                <div class="w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-700 rounded-xl flex items-center justify-center mr-4">
                    <span class="text-white font-bold text-xl">TRW</span>
                </div>
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">The Real World</h1>
                    <p class="text-sm text-gray-600">Escape the Matrix</p>
                </div>
            </a>
        </div>

        <!-- Error Content -->
        <div class="max-w-md w-full text-center">
            <!-- Error Code -->
            <div class="mb-8">
                <h1 class="text-9xl font-bold text-gray-200 mb-4">@yield('code')</h1>
                <h2 class="text-3xl font-bold text-gray-900 mb-4">@yield('title')</h2>
                <p class="text-lg text-gray-600 mb-8">@yield('message')</p>
            </div>

            <!-- Error Illustration -->
            <div class="mb-8">
                <svg class="mx-auto h-32 w-32 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    @yield('illustration')
                </svg>
            </div>

            <!-- Action Buttons -->
            <div class="space-y-4">
                <a href="{{ route('home') }}" 
                   class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg transition duration-300 inline-block">
                    Go Home
                </a>
                
                <button onclick="history.back()" 
                        class="w-full bg-gray-100 hover:bg-gray-200 text-gray-800 font-medium py-3 px-6 rounded-lg transition duration-300">
                    Go Back
                </button>
            </div>

            <!-- Additional Help -->
            <div class="mt-8 pt-8 border-t border-gray-200">
                <p class="text-sm text-gray-600 mb-4">
                    Need help? Contact our support team.
                </p>
                <div class="flex justify-center space-x-4">
                    <a href="{{ route('contact') }}" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                        Contact Support
                    </a>
                    <a href="{{ route('home') }}" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                        Visit Homepage
                    </a>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="mt-16 text-center">
            <p class="text-sm text-gray-500">
                &copy; {{ date('Y') }} The Real World. All rights reserved.
            </p>
        </div>
    </div>

    <!-- Additional Scripts -->
    @stack('scripts')
</body>
</html>
