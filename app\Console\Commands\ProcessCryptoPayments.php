<?php

namespace App\Console\Commands;

use App\Models\Payment;
use App\Services\PaymentService;
use Illuminate\Console\Command;

class ProcessCryptoPayments extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'payments:process-crypto {--limit=50 : Maximum number of payments to process}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process pending cryptocurrency payments by checking blockchain confirmations';

    protected $paymentService;

    /**
     * Create a new command instance.
     */
    public function __construct(PaymentService $paymentService)
    {
        parent::__construct();
        $this->paymentService = $paymentService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting crypto payment processing...');

        $limit = $this->option('limit');

        // Get pending crypto payments
        $pendingPayments = Payment::where('payment_method', 'crypto')
            ->where('status', 'pending')
            ->where('created_at', '>=', now()->subDays(7)) // Only check payments from last 7 days
            ->limit($limit)
            ->get();

        if ($pendingPayments->isEmpty()) {
            $this->info('No pending crypto payments found.');
            return 0;
        }

        $this->info("Found {$pendingPayments->count()} pending crypto payments to process.");

        $processed = 0;
        $failed = 0;

        foreach ($pendingPayments as $payment) {
            $this->line("Processing payment {$payment->id} ({$payment->crypto_currency})...");

            try {
                if ($this->paymentService->verifyCryptoPayment($payment)) {
                    $this->info("✓ Payment {$payment->id} verified and processed successfully.");
                    $processed++;
                } else {
                    $this->warn("⚠ Payment {$payment->id} not yet confirmed on blockchain.");
                }
            } catch (\Exception $e) {
                $this->error("✗ Failed to process payment {$payment->id}: {$e->getMessage()}");
                $failed++;
            }
        }

        $this->newLine();
        $this->info("Processing complete!");
        $this->table(
            ['Status', 'Count'],
            [
                ['Processed', $processed],
                ['Failed', $failed],
                ['Still Pending', $pendingPayments->count() - $processed - $failed],
            ]
        );

        return 0;
    }
}
