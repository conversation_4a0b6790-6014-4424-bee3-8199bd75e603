@extends('layouts.app')

@section('content')
<!-- Hero Section -->
<div class="bg-gradient-to-r from-purple-600 to-blue-700 text-white py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl md:text-5xl font-bold mb-4">
                Live Calls with <PERSON>
            </h1>
            <p class="text-xl md:text-2xl text-purple-100 mb-8">
                Join exclusive live sessions and get direct access to success strategies
            </p>
        </div>
    </div>
</div>

<!-- Filters Section -->
<div class="bg-white border-b">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="flex flex-wrap items-center gap-4">
            <a href="{{ route('live-calls.index', ['filter' => 'upcoming']) }}" 
               class="px-4 py-2 rounded-lg text-sm font-medium transition duration-300 {{ $filter === 'upcoming' ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200' }}">
                Upcoming
            </a>
            <a href="{{ route('live-calls.index', ['filter' => 'live']) }}" 
               class="px-4 py-2 rounded-lg text-sm font-medium transition duration-300 {{ $filter === 'live' ? 'bg-red-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200' }}">
                Live Now
            </a>
            <a href="{{ route('live-calls.index', ['filter' => 'completed']) }}" 
               class="px-4 py-2 rounded-lg text-sm font-medium transition duration-300 {{ $filter === 'completed' ? 'bg-green-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200' }}">
                Completed
            </a>
            @auth
                <a href="{{ route('live-calls.index', ['filter' => 'my-registered']) }}" 
                   class="px-4 py-2 rounded-lg text-sm font-medium transition duration-300 {{ $filter === 'my-registered' ? 'bg-purple-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200' }}">
                    My Registered
                </a>
            @endauth
        </div>
    </div>
</div>

<!-- Live Calls Grid -->
<div class="py-12 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        @if($liveCalls->count() > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                @foreach($liveCalls as $liveCall)
                <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition duration-300">
                    <!-- Status Badge -->
                    <div class="relative">
                        <div class="bg-gradient-to-br from-purple-500 to-blue-600 h-32 flex items-center justify-center">
                            <div class="text-center text-white">
                                <svg class="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                </svg>
                                <p class="font-medium">Live Call</p>
                            </div>
                        </div>
                        
                        <!-- Status Badge -->
                        <div class="absolute top-2 right-2">
                            @if($liveCall->is_live)
                                <span class="bg-red-500 text-white px-2 py-1 rounded text-xs font-medium animate-pulse">
                                    🔴 LIVE
                                </span>
                            @elseif($liveCall->is_upcoming)
                                <span class="bg-blue-500 text-white px-2 py-1 rounded text-xs font-medium">
                                    Upcoming
                                </span>
                            @else
                                <span class="bg-gray-500 text-white px-2 py-1 rounded text-xs font-medium">
                                    Completed
                                </span>
                            @endif
                        </div>
                        
                        <!-- Registration Status -->
                        @auth
                            @if(in_array($liveCall->id, $userRegistrations))
                                <div class="absolute top-2 left-2">
                                    <span class="bg-green-500 text-white px-2 py-1 rounded text-xs font-medium">
                                        ✓ Registered
                                    </span>
                                </div>
                            @endif
                        @endauth
                    </div>
                    
                    <!-- Call Info -->
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-3">
                            <span class="text-sm text-gray-600">
                                {{ $liveCall->scheduled_at->format('M j, Y') }}
                            </span>
                            <span class="text-sm text-gray-600">
                                {{ $liveCall->formatted_duration }}
                            </span>
                        </div>
                        
                        <h3 class="font-semibold text-lg mb-2 line-clamp-2">{{ $liveCall->title }}</h3>
                        <p class="text-gray-600 text-sm mb-4 line-clamp-3">{{ $liveCall->description }}</p>
                        
                        <div class="flex items-center mb-4">
                            @if($liveCall->mentor->avatar)
                                <img src="{{ $liveCall->mentor->avatar }}" alt="{{ $liveCall->mentor->name }}" class="w-8 h-8 rounded-full mr-3">
                            @else
                                <div class="w-8 h-8 bg-gray-300 rounded-full mr-3 flex items-center justify-center">
                                    <span class="text-xs text-gray-600">{{ substr($liveCall->mentor->name, 0, 1) }}</span>
                                </div>
                            @endif
                            <div>
                                <p class="font-medium text-sm text-gray-900">{{ $liveCall->mentor->name }}</p>
                                <p class="text-xs text-gray-600">{{ $liveCall->scheduled_at->format('g:i A T') }}</p>
                            </div>
                        </div>
                        
                        <!-- Attendance Info -->
                        <div class="flex items-center justify-between mb-4 text-sm text-gray-600">
                            <span>{{ $liveCall->attendance_count }} registered</span>
                            @if($liveCall->max_attendees)
                                <span>Max: {{ $liveCall->max_attendees }}</span>
                            @endif
                        </div>
                        
                        <!-- Required Plans -->
                        @if($liveCall->required_plans)
                            <div class="mb-4">
                                <p class="text-xs text-gray-600 mb-1">Required plans:</p>
                                <div class="flex flex-wrap gap-1">
                                    @foreach($liveCall->required_plans as $plan)
                                        <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                                            {{ ucfirst($plan) }}
                                        </span>
                                    @endforeach
                                </div>
                            </div>
                        @endif
                        
                        <div class="flex items-center justify-between">
                            <a href="{{ route('live-calls.show', $liveCall) }}" 
                               class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm font-medium transition duration-300">
                                View Details
                            </a>
                            
                            @auth
                                @if($liveCall->is_live && in_array($liveCall->id, $userRegistrations))
                                    <a href="{{ route('live-calls.join', $liveCall) }}" 
                                       class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded text-sm font-medium transition duration-300">
                                        Join Live
                                    </a>
                                @endif
                            @endauth
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
            
            <!-- Pagination -->
            <div class="mt-8">
                {{ $liveCalls->appends(request()->query())->links() }}
            </div>
        @else
            <div class="text-center py-12">
                <div class="max-w-md mx-auto">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No live calls found</h3>
                    <p class="mt-1 text-sm text-gray-500">
                        @if($filter === 'upcoming')
                            No upcoming live calls scheduled at the moment.
                        @elseif($filter === 'live')
                            No live calls are currently active.
                        @elseif($filter === 'completed')
                            No completed live calls to show.
                        @else
                            You haven't registered for any live calls yet.
                        @endif
                    </p>
                </div>
            </div>
        @endif
    </div>
</div>

<!-- CTA Section -->
@guest
<div class="py-16 bg-blue-600">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl font-bold text-white mb-4">Ready to Join Live Calls?</h2>
        <p class="text-xl text-blue-100 mb-8">Get exclusive access to live sessions with Andrew Tate</p>
        <div class="space-x-4">
            <a href="{{ route('register') }}" class="bg-white text-blue-600 hover:bg-gray-100 px-8 py-3 rounded-lg font-medium transition duration-300">
                Sign Up Now
            </a>
            <a href="{{ route('pricing') }}" class="border border-white text-white hover:bg-white hover:text-blue-600 px-8 py-3 rounded-lg font-medium transition duration-300">
                View Plans
            </a>
        </div>
    </div>
</div>
@endguest
@endsection
