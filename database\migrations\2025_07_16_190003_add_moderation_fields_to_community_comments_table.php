<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('community_comments', function (Blueprint $table) {
            $table->enum('status', ['approved', 'pending', 'rejected'])->default('approved')->after('content');
            $table->boolean('is_reported')->default(false)->after('status');
            $table->text('report_reason')->nullable()->after('is_reported');
            $table->timestamp('moderated_at')->nullable()->after('report_reason');
            $table->foreignId('moderated_by')->nullable()->constrained('users')->onDelete('set null')->after('moderated_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('community_comments', function (Blueprint $table) {
            $table->dropForeign(['moderated_by']);
            $table->dropColumn([
                'status',
                'is_reported',
                'report_reason',
                'moderated_at',
                'moderated_by'
            ]);
        });
    }
};
