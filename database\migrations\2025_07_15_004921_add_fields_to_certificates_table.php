<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('certificates', function (Blueprint $table) {
            // Add certificate_id field if it doesn't exist
            if (!Schema::hasColumn('certificates', 'certificate_id')) {
                $table->string('certificate_id')->unique()->after('id');
            }
            
            // Add additional fields that might be missing
            if (!Schema::hasColumn('certificates', 'grade')) {
                $table->string('grade')->nullable()->after('issued_at');
            }
            
            if (!Schema::hasColumn('certificates', 'completion_time')) {
                $table->string('completion_time')->nullable()->after('grade');
            }
            
            if (!Schema::hasColumn('certificates', 'is_public')) {
                $table->boolean('is_public')->default(true)->after('is_valid');
            }
            
            if (!Schema::hasColumn('certificates', 'qr_code_url')) {
                $table->string('qr_code_url')->nullable()->after('verification_url');
            }
            
            if (!Schema::hasColumn('certificates', 'verification_hash')) {
                $table->string('verification_hash')->nullable()->after('qr_code_url');
            }
        });

        // Add indexes for the new fields
        Schema::table('certificates', function (Blueprint $table) {
            $table->index('certificate_id');
            $table->index('is_public');
            $table->index('verification_hash');
        });

        // Add soft deletes if not exists
        if (!Schema::hasColumn('certificates', 'deleted_at')) {
            Schema::table('certificates', function (Blueprint $table) {
                $table->softDeletes();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('certificates', function (Blueprint $table) {
            $table->dropIndex(['certificate_id']);
            $table->dropIndex(['is_public']);
            $table->dropIndex(['verification_hash']);
            
            $table->dropColumn([
                'certificate_id',
                'grade',
                'completion_time',
                'is_public',
                'qr_code_url',
                'verification_hash',
                'deleted_at'
            ]);
        });
    }
};
