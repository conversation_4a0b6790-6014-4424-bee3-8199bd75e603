@props([
    'value' => 0,
    'max' => 100,
    'size' => 'md',
    'color' => 'primary',
    'animated' => false,
    'striped' => false,
    'showLabel' => false,
    'label' => null
])

@php
$percentage = $max > 0 ? min(100, ($value / $max) * 100) : 0;

$sizeClasses = [
    'xs' => 'h-1',
    'sm' => 'h-2',
    'md' => 'h-3',
    'lg' => 'h-4',
    'xl' => 'h-6',
];

$colorClasses = [
    'primary' => 'bg-primary-gradient',
    'secondary' => 'bg-secondary-gradient',
    'success' => 'bg-success-gradient',
    'warning' => 'bg-warning-gradient',
    'danger' => 'bg-danger-gradient',
    'gray' => 'bg-gray-500',
];

$barClasses = 'progress-fill h-full rounded-full transition-all duration-500 ease-out ' . $colorClasses[$color];

if ($striped) {
    $barClasses .= ' bg-striped';
}

if ($animated) {
    $barClasses .= ' animate-pulse';
}
@endphp

<div {{ $attributes->merge(['class' => 'progress-bar']) }}>
    @if($showLabel || $label)
        <div class="flex justify-between items-center mb-2">
            <span class="text-sm font-medium text-gray-700">
                {{ $label ?? "Progress" }}
            </span>
            <span class="text-sm font-medium text-gray-700">
                {{ number_format($percentage, 1) }}%
            </span>
        </div>
    @endif
    
    <div class="w-full bg-gray-200 rounded-full {{ $sizeClasses[$size] }} overflow-hidden">
        <div 
            class="{{ $barClasses }}"
            style="width: {{ $percentage }}%"
            role="progressbar"
            aria-valuenow="{{ $value }}"
            aria-valuemin="0"
            aria-valuemax="{{ $max }}"
            aria-label="{{ $label ?? 'Progress' }}"
        ></div>
    </div>
</div>

<style>
.bg-primary-gradient {
    background: linear-gradient(135deg, #2563eb 0%, #7c3aed 100%);
}

.bg-secondary-gradient {
    background: linear-gradient(135deg, #059669 0%, #2563eb 100%);
}

.bg-success-gradient {
    background: linear-gradient(135deg, #059669 0%, #10b981 100%);
}

.bg-warning-gradient {
    background: linear-gradient(135deg, #d97706 0%, #eab308 100%);
}

.bg-danger-gradient {
    background: linear-gradient(135deg, #dc2626 0%, #ea580c 100%);
}

.bg-striped {
    background-image: linear-gradient(
        45deg,
        rgba(255, 255, 255, 0.15) 25%,
        transparent 25%,
        transparent 50%,
        rgba(255, 255, 255, 0.15) 50%,
        rgba(255, 255, 255, 0.15) 75%,
        transparent 75%,
        transparent
    );
    background-size: 1rem 1rem;
}

.bg-striped.animate-pulse {
    animation: progress-bar-stripes 1s linear infinite;
}

@keyframes progress-bar-stripes {
    0% {
        background-position: 1rem 0;
    }
    100% {
        background-position: 0 0;
    }
}
</style>
