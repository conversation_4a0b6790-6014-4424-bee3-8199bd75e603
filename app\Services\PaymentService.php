<?php

namespace App\Services;

use App\Models\Payment;
use App\Models\User;
use App\Models\SubscriptionPlan;
use App\Models\UserSubscription;
use App\Jobs\ProcessPaymentJob;
use Illuminate\Support\Facades\Log;
use Stripe\StripeClient;

class PaymentService
{
    protected $stripe;

    public function __construct()
    {
        $this->stripe = new StripeClient(config('cashier.secret'));
    }

    /**
     * Process Stripe payment.
     */
    public function processStripePayment(User $user, SubscriptionPlan $plan, string $billingCycle, string $paymentMethodId): array
    {
        try {
            $amount = $billingCycle === 'yearly' ? $plan->yearly_price : $plan->monthly_price;
            
            // Create payment intent
            $paymentIntent = $this->stripe->paymentIntents->create([
                'amount' => $amount * 100, // Convert to cents
                'currency' => 'usd',
                'customer' => $user->stripe_id ?? $this->createStripeCustomer($user),
                'payment_method' => $paymentMethodId,
                'confirmation_method' => 'manual',
                'confirm' => true,
                'return_url' => route('subscriptions.success'),
            ]);

            if ($paymentIntent->status === 'succeeded') {
                // Dispatch job to process payment
                ProcessPaymentJob::dispatch([
                    'subscription_plan_id' => $plan->id,
                    'amount' => $amount,
                    'currency' => 'USD',
                    'payment_method' => 'stripe',
                    'transaction_id' => $paymentIntent->id,
                    'billing_cycle' => $billingCycle,
                    'metadata' => [
                        'stripe_payment_intent_id' => $paymentIntent->id,
                        'stripe_customer_id' => $paymentIntent->customer,
                    ],
                ], $user);

                return [
                    'success' => true,
                    'payment_intent' => $paymentIntent,
                    'message' => 'Payment processed successfully',
                ];
            }

            return [
                'success' => false,
                'payment_intent' => $paymentIntent,
                'message' => 'Payment requires additional action',
            ];

        } catch (\Exception $e) {
            Log::error('Stripe payment failed', [
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Payment processing failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Process cryptocurrency payment.
     */
    public function processCryptoPayment(User $user, SubscriptionPlan $plan, string $billingCycle, string $cryptoCurrency, string $cryptoAddress): array
    {
        try {
            $amount = $billingCycle === 'yearly' ? $plan->yearly_price : $plan->monthly_price;
            
            // Get crypto conversion rate (you would integrate with a crypto API here)
            $cryptoAmount = $this->convertToCrypto($amount, $cryptoCurrency);
            
            // Generate unique transaction ID
            $transactionId = 'crypto_' . uniqid() . '_' . time();

            // Create pending payment record
            $payment = Payment::create([
                'user_id' => $user->id,
                'subscription_plan_id' => $plan->id,
                'amount' => $amount,
                'currency' => 'USD',
                'crypto_amount' => $cryptoAmount,
                'crypto_currency' => $cryptoCurrency,
                'crypto_address' => $cryptoAddress,
                'payment_method' => 'crypto',
                'transaction_id' => $transactionId,
                'status' => 'pending',
                'payment_date' => now(),
                'metadata' => json_encode([
                    'billing_cycle' => $billingCycle,
                    'crypto_conversion_rate' => $this->getCryptoRate($cryptoCurrency),
                ]),
            ]);

            return [
                'success' => true,
                'payment' => $payment,
                'crypto_amount' => $cryptoAmount,
                'crypto_currency' => $cryptoCurrency,
                'payment_address' => $this->getCryptoPaymentAddress($cryptoCurrency),
                'message' => 'Crypto payment initiated. Please send the exact amount to the provided address.',
            ];

        } catch (\Exception $e) {
            Log::error('Crypto payment failed', [
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Crypto payment processing failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Cancel subscription.
     */
    public function cancelSubscription(UserSubscription $subscription): bool
    {
        try {
            // Cancel Stripe subscription if exists
            if ($subscription->stripe_subscription_id) {
                $this->stripe->subscriptions->cancel($subscription->stripe_subscription_id);
            }

            // Update subscription status
            $subscription->update([
                'status' => 'cancelled',
                'cancelled_at' => now(),
                'ends_at' => $subscription->current_period_end,
            ]);

            Log::info('Subscription cancelled', [
                'subscription_id' => $subscription->id,
                'user_id' => $subscription->user_id,
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('Subscription cancellation failed', [
                'subscription_id' => $subscription->id,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Resume cancelled subscription.
     */
    public function resumeSubscription(UserSubscription $subscription): bool
    {
        try {
            // Only resume if within grace period
            if ($subscription->ends_at && $subscription->ends_at->isPast()) {
                return false;
            }

            $subscription->update([
                'status' => 'active',
                'cancelled_at' => null,
                'ends_at' => null,
            ]);

            Log::info('Subscription resumed', [
                'subscription_id' => $subscription->id,
                'user_id' => $subscription->user_id,
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('Subscription resume failed', [
                'subscription_id' => $subscription->id,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Create Stripe customer.
     */
    protected function createStripeCustomer(User $user): string
    {
        $customer = $this->stripe->customers->create([
            'email' => $user->email,
            'name' => $user->name,
            'metadata' => [
                'user_id' => $user->id,
            ],
        ]);

        $user->update(['stripe_id' => $customer->id]);

        return $customer->id;
    }

    /**
     * Convert USD amount to cryptocurrency.
     */
    protected function convertToCrypto(float $usdAmount, string $cryptoCurrency): float
    {
        // This is a simplified conversion - you would integrate with a real crypto API
        $rates = [
            'BTC' => 45000,
            'ETH' => 3000,
            'USDT' => 1,
            'BNB' => 300,
        ];

        return round($usdAmount / ($rates[$cryptoCurrency] ?? 1), 8);
    }

    /**
     * Get cryptocurrency conversion rate.
     */
    protected function getCryptoRate(string $cryptoCurrency): float
    {
        // This would fetch real-time rates from a crypto API
        $rates = [
            'BTC' => 45000,
            'ETH' => 3000,
            'USDT' => 1,
            'BNB' => 300,
        ];

        return $rates[$cryptoCurrency] ?? 1;
    }

    /**
     * Get crypto payment address.
     */
    protected function getCryptoPaymentAddress(string $cryptoCurrency): string
    {
        // This would return your actual crypto wallet addresses
        $addresses = [
            'BTC' => '**********************************',
            'ETH' => '******************************************',
            'USDT' => '******************************************',
            'BNB' => 'bnb1grpf0955h0ykzq3ar5nmum7y6gdfl6lxfn46h2',
        ];

        return $addresses[$cryptoCurrency] ?? '';
    }

    /**
     * Verify crypto payment.
     */
    public function verifyCryptoPayment(Payment $payment): bool
    {
        // This would integrate with blockchain APIs to verify the payment
        // For now, we'll simulate verification
        
        try {
            // Simulate blockchain verification
            $verified = $this->checkBlockchainTransaction($payment);
            
            if ($verified) {
                $payment->update(['status' => 'completed']);
                
                // Process the subscription
                ProcessPaymentJob::dispatch([
                    'subscription_plan_id' => $payment->subscription_plan_id,
                    'amount' => $payment->amount,
                    'currency' => $payment->currency,
                    'payment_method' => 'crypto',
                    'transaction_id' => $payment->transaction_id,
                    'billing_cycle' => json_decode($payment->metadata, true)['billing_cycle'] ?? 'monthly',
                ], $payment->user);
                
                return true;
            }
            
            return false;
            
        } catch (\Exception $e) {
            Log::error('Crypto payment verification failed', [
                'payment_id' => $payment->id,
                'error' => $e->getMessage(),
            ]);
            
            return false;
        }
    }

    /**
     * Check blockchain transaction (mock implementation).
     */
    protected function checkBlockchainTransaction(Payment $payment): bool
    {
        // This would integrate with actual blockchain APIs
        // For demo purposes, we'll return true after some time
        return $payment->created_at->diffInMinutes(now()) > 5;
    }
}
