<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Course;
use App\Models\LiveCall;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class NotificationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $users = User::whereHas('roles', function($q) {
            $q->where('name', 'user');
        })->get();

        $courses = Course::all();
        $liveCalls = LiveCall::all();

        foreach ($users as $user) {
            $this->createNotificationsForUser($user, $courses, $liveCalls);
        }

        $this->command->info('Notifications seeded successfully!');
    }

    private function createNotificationsForUser(User $user, $courses, $liveCalls)
    {
        $notifications = [];

        // Welcome notification
        $notifications[] = [
            'id' => \Str::uuid(),
            'type' => 'App\\Notifications\\WelcomeNotification',
            'notifiable_type' => 'App\\Models\\User',
            'notifiable_id' => $user->id,
            'data' => json_encode([
                'title' => 'Welcome to The Real World!',
                'message' => 'Welcome to The Real World! Start your journey to financial freedom today.',
                'action_url' => '/dashboard',
                'action_text' => 'Get Started',
                'type' => 'welcome'
            ]),
            'read_at' => null,
            'created_at' => $user->created_at,
            'updated_at' => $user->created_at,
        ];

        // Course enrollment notifications
        $userCourses = $courses->random(rand(2, 4));
        foreach ($userCourses as $course) {
            $notifications[] = [
                'id' => \Str::uuid(),
                'type' => 'App\\Notifications\\CourseEnrollmentNotification',
                'notifiable_type' => 'App\\Models\\User',
                'notifiable_id' => $user->id,
                'data' => json_encode([
                    'title' => 'Course Enrollment Confirmed',
                    'message' => "You've successfully enrolled in {$course->title}",
                    'action_url' => "/courses/{$course->slug}",
                    'action_text' => 'Start Learning',
                    'course_id' => $course->id,
                    'course_title' => $course->title,
                    'type' => 'course_enrollment'
                ]),
                'read_at' => rand(0, 1) ? now()->subDays(rand(1, 10)) : null,
                'created_at' => now()->subDays(rand(1, 20)),
                'updated_at' => now()->subDays(rand(1, 20)),
            ];
        }

        // Live call notifications
        if ($liveCalls->isNotEmpty()) {
            $userLiveCalls = $liveCalls->random(min(rand(1, 3), $liveCalls->count()));
            foreach ($userLiveCalls as $liveCall) {
            $notifications[] = [
                'id' => \Str::uuid(),
                'type' => 'App\\Notifications\\LiveCallNotification',
                'notifiable_type' => 'App\\Models\\User',
                'notifiable_id' => $user->id,
                'data' => json_encode([
                    'title' => 'Live Call Starting Soon',
                    'message' => "Join {$liveCall->title} starting in 30 minutes",
                    'action_url' => "/live-calls/{$liveCall->id}",
                    'action_text' => 'Join Call',
                    'live_call_id' => $liveCall->id,
                    'live_call_title' => $liveCall->title,
                    'scheduled_at' => $liveCall->scheduled_at,
                    'type' => 'live_call'
                ]),
                'read_at' => rand(0, 1) ? now()->subDays(rand(1, 5)) : null,
                'created_at' => $liveCall->scheduled_at->subMinutes(30),
                'updated_at' => $liveCall->scheduled_at->subMinutes(30),
            ];
            }
        }

        // Achievement notifications
        $achievements = [
            'First Course Completed',
            'Week Streak Achieved',
            'Top Performer',
            'Community Contributor',
            'Fast Learner',
        ];

        foreach ($achievements as $achievement) {
            if (rand(0, 1)) { // 50% chance
                $notifications[] = [
                    'id' => \Str::uuid(),
                    'type' => 'App\\Notifications\\AchievementNotification',
                    'notifiable_type' => 'App\\Models\\User',
                    'notifiable_id' => $user->id,
                    'data' => json_encode([
                        'title' => 'Achievement Unlocked!',
                        'message' => "Congratulations! You've earned the '{$achievement}' achievement.",
                        'action_url' => '/profile/achievements',
                        'action_text' => 'View Achievements',
                        'achievement' => $achievement,
                        'type' => 'achievement'
                    ]),
                    'read_at' => rand(0, 1) ? now()->subDays(rand(1, 15)) : null,
                    'created_at' => now()->subDays(rand(1, 30)),
                    'updated_at' => now()->subDays(rand(1, 30)),
                ];
            }
        }

        // Payment notifications
        $notifications[] = [
            'id' => \Str::uuid(),
            'type' => 'App\\Notifications\\PaymentNotification',
            'notifiable_type' => 'App\\Models\\User',
            'notifiable_id' => $user->id,
            'data' => json_encode([
                'title' => 'Payment Successful',
                'message' => 'Your subscription payment has been processed successfully.',
                'action_url' => '/billing',
                'action_text' => 'View Billing',
                'amount' => '$49.99',
                'type' => 'payment_success'
            ]),
            'read_at' => rand(0, 1) ? now()->subDays(rand(1, 7)) : null,
            'created_at' => now()->subDays(rand(1, 30)),
            'updated_at' => now()->subDays(rand(1, 30)),
        ];

        // System notifications
        $systemNotifications = [
            [
                'title' => 'New Course Available',
                'message' => 'A new course has been added to your learning path.',
                'type' => 'new_course'
            ],
            [
                'title' => 'Platform Update',
                'message' => 'We\'ve made improvements to enhance your learning experience.',
                'type' => 'system_update'
            ],
            [
                'title' => 'Community Event',
                'message' => 'Join our upcoming community event this weekend.',
                'type' => 'community_event'
            ],
        ];

        foreach ($systemNotifications as $sysNotif) {
            if (rand(0, 1)) { // 50% chance
                $notifications[] = [
                    'id' => \Str::uuid(),
                    'type' => 'App\\Notifications\\SystemNotification',
                    'notifiable_type' => 'App\\Models\\User',
                    'notifiable_id' => $user->id,
                    'data' => json_encode([
                        'title' => $sysNotif['title'],
                        'message' => $sysNotif['message'],
                        'action_url' => '/dashboard',
                        'action_text' => 'Learn More',
                        'type' => $sysNotif['type']
                    ]),
                    'read_at' => rand(0, 1) ? now()->subDays(rand(1, 10)) : null,
                    'created_at' => now()->subDays(rand(1, 20)),
                    'updated_at' => now()->subDays(rand(1, 20)),
                ];
            }
        }

        // Insert notifications in batches
        $chunks = array_chunk($notifications, 50);
        foreach ($chunks as $chunk) {
            DB::table('notifications')->insert($chunk);
        }
    }
}
