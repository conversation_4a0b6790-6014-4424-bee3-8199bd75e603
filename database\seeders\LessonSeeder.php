<?php

namespace Database\Seeders;

use App\Models\Course;
use App\Models\Lesson;
use Illuminate\Database\Seeder;

class LessonSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $courses = Course::all();

        foreach ($courses as $course) {
            $this->createLessonsForCourse($course);
        }

        $this->command->info('Lessons seeded successfully!');
    }

    private function createLessonsForCourse(Course $course)
    {
        $lessonTemplates = $this->getLessonTemplates($course->category);

        foreach ($lessonTemplates as $index => $lessonData) {
            $slug = \Str::slug($lessonData['title']);
            Lesson::firstOrCreate(
                [
                    'course_id' => $course->id,
                    'slug' => $slug,
                ],
                [
                    'title' => $lessonData['title'],
                    'description' => $lessonData['description'],
                    'content' => $lessonData['content'],
                    'video_url' => $lessonData['video_url'] ?? null,
                    'duration_minutes' => $lessonData['duration'],
                    'sort_order' => $index + 1,
                    'is_published' => true,
                    'is_free' => $index === 0, // First lesson is always free
                    'resources' => json_encode($lessonData['resources'] ?? []),
                ]
            );
        }
    }

    private function getLessonTemplates($category)
    {
        $templates = [
            'E-commerce' => [
                [
                    'title' => 'E-commerce Fundamentals',
                    'description' => 'Understanding the basics of online business',
                    'content' => 'Learn the fundamental principles of e-commerce, market research, and product selection.',
                    'video_url' => 'https://example.com/video1',
                    'duration' => 45,
                    'resources' => ['Market Research Template', 'Product Analysis Sheet']
                ],
                [
                    'title' => 'Finding Profitable Products',
                    'description' => 'How to identify winning products in any niche',
                    'content' => 'Master the art of product research using proven methods and tools.',
                    'video_url' => 'https://example.com/video2',
                    'duration' => 60,
                    'resources' => ['Product Research Tools List', 'Profit Calculator']
                ],
                [
                    'title' => 'Setting Up Your Store',
                    'description' => 'Building a professional e-commerce website',
                    'content' => 'Step-by-step guide to creating a high-converting online store.',
                    'video_url' => 'https://example.com/video3',
                    'duration' => 90,
                    'resources' => ['Store Setup Checklist', 'Design Templates']
                ],
                [
                    'title' => 'Marketing and Traffic Generation',
                    'description' => 'Driving targeted traffic to your store',
                    'content' => 'Learn proven marketing strategies to attract customers.',
                    'video_url' => 'https://example.com/video4',
                    'duration' => 75,
                    'resources' => ['Marketing Calendar', 'Ad Templates']
                ],
                [
                    'title' => 'Scaling Your Business',
                    'description' => 'Growing from 6 to 7 figures',
                    'content' => 'Advanced strategies for scaling your e-commerce empire.',
                    'video_url' => 'https://example.com/video5',
                    'duration' => 120,
                    'resources' => ['Scaling Checklist', 'Team Building Guide']
                ],
            ],
            'Copywriting' => [
                [
                    'title' => 'Psychology of Persuasion',
                    'description' => 'Understanding what makes people buy',
                    'content' => 'Master the psychological triggers that drive purchasing decisions.',
                    'video_url' => 'https://example.com/video6',
                    'duration' => 50,
                    'resources' => ['Psychology Cheat Sheet', 'Trigger Examples']
                ],
                [
                    'title' => 'Writing Compelling Headlines',
                    'description' => 'Crafting headlines that grab attention',
                    'content' => 'Learn to write headlines that stop scrolling and drive action.',
                    'video_url' => 'https://example.com/video7',
                    'duration' => 40,
                    'resources' => ['Headline Formulas', 'A/B Testing Guide']
                ],
                [
                    'title' => 'Sales Page Structure',
                    'description' => 'Building high-converting sales pages',
                    'content' => 'The proven framework for sales pages that convert.',
                    'video_url' => 'https://example.com/video8',
                    'duration' => 80,
                    'resources' => ['Sales Page Template', 'Conversion Checklist']
                ],
                [
                    'title' => 'Email Marketing Mastery',
                    'description' => 'Building and monetizing email lists',
                    'content' => 'Create email campaigns that build relationships and drive sales.',
                    'video_url' => 'https://example.com/video9',
                    'duration' => 65,
                    'resources' => ['Email Templates', 'Automation Sequences']
                ],
            ],
            'Cryptocurrency' => [
                [
                    'title' => 'Blockchain Basics',
                    'description' => 'Understanding blockchain technology',
                    'content' => 'Learn the fundamentals of blockchain and cryptocurrency.',
                    'video_url' => 'https://example.com/video10',
                    'duration' => 55,
                    'resources' => ['Blockchain Guide', 'Terminology List']
                ],
                [
                    'title' => 'Crypto Investment Strategies',
                    'description' => 'Building a profitable crypto portfolio',
                    'content' => 'Learn proven strategies for crypto investing and risk management.',
                    'video_url' => 'https://example.com/video11',
                    'duration' => 70,
                    'resources' => ['Portfolio Template', 'Risk Calculator']
                ],
                [
                    'title' => 'DeFi and Yield Farming',
                    'description' => 'Earning passive income with DeFi',
                    'content' => 'Master decentralized finance and yield farming strategies.',
                    'video_url' => 'https://example.com/video12',
                    'duration' => 85,
                    'resources' => ['DeFi Platform List', 'Yield Calculator']
                ],
            ],
            'Stocks & Trading' => [
                [
                    'title' => 'Market Analysis Fundamentals',
                    'description' => 'Reading charts and market trends',
                    'content' => 'Learn technical and fundamental analysis for stock trading.',
                    'video_url' => 'https://example.com/video13',
                    'duration' => 60,
                    'resources' => ['Chart Patterns Guide', 'Analysis Tools']
                ],
                [
                    'title' => 'Risk Management',
                    'description' => 'Protecting your trading capital',
                    'content' => 'Essential risk management strategies for successful trading.',
                    'video_url' => 'https://example.com/video14',
                    'duration' => 45,
                    'resources' => ['Risk Calculator', 'Position Sizing Guide']
                ],
                [
                    'title' => 'Advanced Trading Strategies',
                    'description' => 'Professional trading techniques',
                    'content' => 'Advanced strategies used by professional traders.',
                    'video_url' => 'https://example.com/video15',
                    'duration' => 90,
                    'resources' => ['Strategy Templates', 'Backtesting Tools']
                ],
            ],
        ];

        // Default lessons for categories not specifically defined
        $defaultLessons = [
            [
                'title' => 'Getting Started',
                'description' => 'Introduction to the fundamentals',
                'content' => 'Learn the basic concepts and principles.',
                'duration' => 30,
            ],
            [
                'title' => 'Intermediate Concepts',
                'description' => 'Building on the basics',
                'content' => 'Dive deeper into intermediate topics.',
                'duration' => 45,
            ],
            [
                'title' => 'Advanced Strategies',
                'description' => 'Professional-level techniques',
                'content' => 'Master advanced strategies and techniques.',
                'duration' => 60,
            ],
            [
                'title' => 'Implementation',
                'description' => 'Putting it all together',
                'content' => 'Apply what you\'ve learned in real-world scenarios.',
                'duration' => 75,
            ],
        ];

        return $templates[$category] ?? $defaultLessons;
    }

    private function getTagsForCategory($category)
    {
        $tags = [
            'E-commerce' => ['dropshipping', 'shopify', 'marketing', 'sales', 'business'],
            'Copywriting' => ['writing', 'sales', 'marketing', 'persuasion', 'conversion'],
            'Cryptocurrency' => ['bitcoin', 'blockchain', 'trading', 'investment', 'defi'],
            'Stocks & Trading' => ['stocks', 'trading', 'investment', 'analysis', 'finance'],
            'Amazon FBA' => ['amazon', 'fba', 'ecommerce', 'private-label', 'sourcing'],
            'Freelancing' => ['freelance', 'skills', 'clients', 'business', 'remote'],
            'Content Creation' => ['content', 'youtube', 'social-media', 'video', 'audience'],
            'Business Management' => ['management', 'leadership', 'operations', 'strategy', 'team'],
        ];

        return $tags[$category] ?? ['business', 'entrepreneurship', 'success', 'wealth', 'mindset'];
    }

    private function getLearningOutcomes($category)
    {
        $outcomes = [
            'E-commerce' => [
                'Build a profitable online store from scratch',
                'Master product research and selection',
                'Implement effective marketing strategies',
                'Scale your business to 6-7 figures',
                'Automate operations for passive income'
            ],
            'Copywriting' => [
                'Write compelling sales copy that converts',
                'Understand consumer psychology and triggers',
                'Create effective email marketing campaigns',
                'Master different copywriting formats',
                'Generate consistent income through writing'
            ],
            'Cryptocurrency' => [
                'Understand blockchain technology fundamentals',
                'Develop profitable trading strategies',
                'Navigate DeFi protocols safely',
                'Build a diversified crypto portfolio',
                'Manage risk in volatile markets'
            ],
        ];

        return $outcomes[$category] ?? [
            'Master the fundamentals of the subject',
            'Apply knowledge in real-world scenarios',
            'Develop professional-level skills',
            'Build a sustainable income stream',
            'Achieve financial independence'
        ];
    }

    private function getPrerequisites($difficulty)
    {
        $prerequisites = [
            'beginner' => [
                'Basic computer skills',
                'Internet access',
                'Willingness to learn'
            ],
            'intermediate' => [
                'Basic business knowledge',
                'Some experience in the field',
                'Completed beginner courses'
            ],
            'advanced' => [
                'Extensive experience in the field',
                'Completed intermediate courses',
                'Strong analytical skills',
                'Risk tolerance'
            ]
        ];

        return $prerequisites[$difficulty] ?? $prerequisites['beginner'];
    }
}
