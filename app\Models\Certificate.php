<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Certificate extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'certificate_id',
        'user_id',
        'course_id',
        'certificate_number', // Keep for backward compatibility
        'issued_at',
        'expires_at',
        'grade',
        'completion_time',
        'is_valid',
        'is_public',
        'verification_url',
        'qr_code_url',
        'verification_hash',
        'certificate_data',
    ];

    protected $casts = [
        'issued_at' => 'datetime',
        'expires_at' => 'datetime',
        'is_valid' => 'boolean',
        'is_public' => 'boolean',
        'certificate_data' => 'array',
    ];

    protected $dates = [
        'issued_at',
        'expires_at',
        'deleted_at',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function course()
    {
        return $this->belongsTo(Course::class);
    }

    public function getRouteKeyName()
    {
        return 'certificate_id';
    }

    public function getPublicUrlAttribute()
    {
        return route('certificates.public', $this->certificate_id ?? $this->id);
    }

    public function getVerificationUrlAttribute()
    {
        return route('certificates.verify', $this->certificate_id ?? $this->id);
    }

    public function getDownloadUrlAttribute()
    {
        return route('certificates.download', $this);
    }

    public function getShareUrlAttribute()
    {
        return $this->is_public ? $this->public_url : route('certificates.show', $this);
    }

    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeForCourse($query, $courseId)
    {
        return $query->where('course_id', $courseId);
    }

    public function scopeIssuedThisMonth($query)
    {
        return $query->whereMonth('issued_at', now()->month)
                    ->whereYear('issued_at', now()->year);
    }

    public function scopeIssuedThisYear($query)
    {
        return $query->whereYear('issued_at', now()->year);
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($certificate) {
            // Generate certificate_id if not provided
            if (!$certificate->certificate_id) {
                $certificate->certificate_id = 'TRW-' . strtoupper(uniqid());
            }

            // Generate verification hash
            $certificate->verification_hash = hash('sha256',
                $certificate->certificate_id .
                $certificate->user_id .
                $certificate->course_id .
                config('app.key')
            );
        });
    }

    public function isValidCertificate()
    {
        // Verify the certificate hasn't been tampered with
        $expectedHash = hash('sha256',
            $this->certificate_id .
            $this->user_id .
            $this->course_id .
            config('app.key')
        );

        return $this->verification_hash === $expectedHash && $this->is_valid;
    }
}
