<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Course;
use App\Models\LiveCall;
use App\Models\CommunityPost;
use App\Models\UserSubscription;
use App\Models\UserProgress;
use Illuminate\Http\Request;
use Carbon\Carbon;

class AdminDashboardController extends Controller
{
    /**
     * Show the admin dashboard.
     */
    public function index()
    {
        // User Statistics
        $totalUsers = User::count();
        $activeUsers = User::where('is_active', true)->count();
        $newUsersToday = User::whereDate('created_at', today())->count();
        $newUsersThisWeek = User::whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])->count();
        $newUsersThisMonth = User::whereMonth('created_at', now()->month)->count();

        // Subscription Statistics
        $activeSubscriptions = UserSubscription::where('status', 'active')->count();
        $totalRevenue = UserSubscription::where('status', 'active')->sum('amount');
        $monthlyRevenue = UserSubscription::where('status', 'active')
            ->whereMonth('created_at', now()->month)
            ->sum('amount');

        // Course Statistics
        $totalCourses = Course::count();
        $publishedCourses = Course::where('is_published', true)->count();
        $totalLessons = Course::withCount('lessons')->get()->sum('lessons_count');
        $completedLessons = UserProgress::where('is_completed', true)->count();

        // Community Statistics
        $totalPosts = CommunityPost::count();
        $postsToday = CommunityPost::whereDate('created_at', today())->count();
        $totalComments = CommunityPost::sum('comments_count');

        // Live Call Statistics
        $totalLiveCalls = LiveCall::count();
        $upcomingCalls = LiveCall::upcoming()->count();
        $completedCalls = LiveCall::completed()->count();

        // Recent Activity
        $recentUsers = User::latest()->take(5)->get();
        $recentPosts = CommunityPost::with('user')->latest()->take(5)->get();
        $recentSubscriptions = UserSubscription::with(['user', 'subscriptionPlan'])
            ->latest()->take(5)->get();

        // Growth Data (last 30 days)
        $userGrowthData = $this->getUserGrowthData();
        $revenueGrowthData = $this->getRevenueGrowthData();

        // System Health
        $systemHealth = $this->getSystemHealth();

        return view('admin.dashboard', compact(
            'totalUsers',
            'activeUsers',
            'newUsersToday',
            'newUsersThisWeek',
            'newUsersThisMonth',
            'activeSubscriptions',
            'totalRevenue',
            'monthlyRevenue',
            'totalCourses',
            'publishedCourses',
            'totalLessons',
            'completedLessons',
            'totalPosts',
            'postsToday',
            'totalComments',
            'totalLiveCalls',
            'upcomingCalls',
            'completedCalls',
            'recentUsers',
            'recentPosts',
            'recentSubscriptions',
            'userGrowthData',
            'revenueGrowthData',
            'systemHealth'
        ));
    }

    /**
     * Get user growth data for the last 30 days.
     */
    private function getUserGrowthData()
    {
        $data = [];
        for ($i = 29; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $count = User::whereDate('created_at', $date)->count();
            $data[] = [
                'date' => $date->format('M j'),
                'count' => $count
            ];
        }
        return $data;
    }

    /**
     * Get revenue growth data for the last 30 days.
     */
    private function getRevenueGrowthData()
    {
        $data = [];
        for ($i = 29; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $amount = UserSubscription::whereDate('created_at', $date)
                ->where('status', 'active')
                ->sum('amount');
            $data[] = [
                'date' => $date->format('M j'),
                'amount' => $amount
            ];
        }
        return $data;
    }

    /**
     * Get system health metrics.
     */
    private function getSystemHealth()
    {
        return [
            'database' => $this->checkDatabaseHealth(),
            'storage' => $this->checkStorageHealth(),
            'cache' => $this->checkCacheHealth(),
            'queue' => $this->checkQueueHealth(),
        ];
    }

    private function checkDatabaseHealth()
    {
        try {
            User::count();
            return ['status' => 'healthy', 'message' => 'Database connection successful'];
        } catch (\Exception $e) {
            return ['status' => 'error', 'message' => 'Database connection failed'];
        }
    }

    private function checkStorageHealth()
    {
        $diskSpace = disk_free_space(storage_path());
        $totalSpace = disk_total_space(storage_path());
        $usedPercentage = (($totalSpace - $diskSpace) / $totalSpace) * 100;

        if ($usedPercentage > 90) {
            return ['status' => 'warning', 'message' => 'Storage usage high: ' . round($usedPercentage, 1) . '%'];
        } elseif ($usedPercentage > 95) {
            return ['status' => 'error', 'message' => 'Storage usage critical: ' . round($usedPercentage, 1) . '%'];
        }

        return ['status' => 'healthy', 'message' => 'Storage usage: ' . round($usedPercentage, 1) . '%'];
    }

    private function checkCacheHealth()
    {
        try {
            cache()->put('health_check', 'test', 60);
            $value = cache()->get('health_check');
            cache()->forget('health_check');

            if ($value === 'test') {
                return ['status' => 'healthy', 'message' => 'Cache working properly'];
            }

            return ['status' => 'warning', 'message' => 'Cache not working properly'];
        } catch (\Exception $e) {
            return ['status' => 'error', 'message' => 'Cache error: ' . $e->getMessage()];
        }
    }

    private function checkQueueHealth()
    {
        // This is a basic check - in production you'd want more sophisticated queue monitoring
        return ['status' => 'healthy', 'message' => 'Queue system operational'];
    }
}
