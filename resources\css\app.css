@import "tailwindcss";

/* Custom CSS for The Real World LMS */

/* Custom Properties (CSS Variables) */
:root {
    --primary-gradient: linear-gradient(135deg, #2563eb 0%, #7c3aed 100%);
    --secondary-gradient: linear-gradient(135deg, #059669 0%, #2563eb 100%);
    --danger-gradient: linear-gradient(135deg, #dc2626 0%, #ea580c 100%);
    --warning-gradient: linear-gradient(135deg, #d97706 0%, #eab308 100%);
    --success-gradient: linear-gradient(135deg, #059669 0%, #10b981 100%);

    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    --border-radius-sm: 0.375rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;

    --transition-fast: 150ms ease-in-out;
    --transition-normal: 300ms ease-in-out;
    --transition-slow: 500ms ease-in-out;
}

/* Base Styles */
* {
    scroll-behavior: smooth;
}

body {
    font-family: 'Figtree', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Custom Gradient Backgrounds */
.bg-primary-gradient {
    background: var(--primary-gradient);
}

.bg-secondary-gradient {
    background: var(--secondary-gradient);
}

.bg-danger-gradient {
    background: var(--danger-gradient);
}

.bg-warning-gradient {
    background: var(--warning-gradient);
}

.bg-success-gradient {
    background: var(--success-gradient);
}

/* Custom Button Styles */
.btn {
    @apply inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2;
}

.btn-primary {
    @apply text-white shadow-md hover:shadow-lg transform hover:-translate-y-0.5;
    background: var(--primary-gradient);
}

.btn-primary:hover {
    filter: brightness(1.1);
}

.btn-primary:focus {
    @apply ring-blue-500;
}

.btn-secondary {
    @apply bg-gray-100 text-gray-900 hover:bg-gray-200 shadow-sm hover:shadow-md;
}

.btn-success {
    @apply text-white shadow-md hover:shadow-lg transform hover:-translate-y-0.5;
    background: var(--success-gradient);
}

.btn-success:hover {
    filter: brightness(1.1);
}

.btn-danger {
    @apply text-white shadow-md hover:shadow-lg transform hover:-translate-y-0.5;
    background: var(--danger-gradient);
}

.btn-danger:hover {
    filter: brightness(1.1);
}

.btn-warning {
    @apply text-white shadow-md hover:shadow-lg transform hover:-translate-y-0.5;
    background: var(--warning-gradient);
}

.btn-warning:hover {
    filter: brightness(1.1);
}

/* Custom Card Styles */
.card {
    @apply bg-white rounded-lg shadow-md overflow-hidden transition-all duration-300;
}

.card:hover {
    @apply shadow-lg transform -translate-y-1;
}

.card-header {
    @apply px-6 py-4 border-b border-gray-200 bg-gray-50;
}

.card-body {
    @apply p-6;
}

.card-footer {
    @apply px-6 py-4 border-t border-gray-200 bg-gray-50;
}

/* Custom Form Styles */
.form-input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200;
}

.form-input:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
}

.form-error {
    @apply text-red-600 text-sm mt-1;
}

.form-help {
    @apply text-gray-500 text-sm mt-1;
}

/* Custom Badge Styles */
.badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.badge-primary {
    @apply bg-blue-100 text-blue-800;
}

.badge-success {
    @apply bg-green-100 text-green-800;
}

.badge-warning {
    @apply bg-yellow-100 text-yellow-800;
}

.badge-danger {
    @apply bg-red-100 text-red-800;
}

.badge-gray {
    @apply bg-gray-100 text-gray-800;
}

/* Custom Alert Styles */
.alert {
    @apply p-4 rounded-lg border-l-4;
}

.alert-success {
    @apply bg-green-50 border-green-400 text-green-700;
}

.alert-warning {
    @apply bg-yellow-50 border-yellow-400 text-yellow-700;
}

.alert-danger {
    @apply bg-red-50 border-red-400 text-red-700;
}

.alert-info {
    @apply bg-blue-50 border-blue-400 text-blue-700;
}

/* Loading Animations */
.loading-spinner {
    @apply inline-block w-4 h-4 border-2 border-current border-t-transparent rounded-full;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.loading-dots {
    @apply inline-flex space-x-1;
}

.loading-dots span {
    @apply w-2 h-2 bg-current rounded-full;
    animation: bounce 1.4s ease-in-out infinite both;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes bounce {
    0%, 80%, 100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1);
    }
}

/* Pulse Animation */
.pulse-animation {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: .5;
    }
}

/* Fade In Animation */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Slide In Animation */
.slide-in-right {
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.slide-in-left {
    animation: slideInLeft 0.3s ease-out;
}

@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Scale Animation */
.scale-in {
    animation: scaleIn 0.2s ease-out;
}

@keyframes scaleIn {
    from {
        transform: scale(0.95);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

/* Hover Effects */
.hover-lift {
    @apply transition-transform duration-300;
}

.hover-lift:hover {
    @apply transform -translate-y-1;
}

.hover-glow {
    @apply transition-all duration-300;
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

/* Text Animations */
.text-gradient {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.text-shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* Progress Bars */
.progress-bar {
    @apply w-full bg-gray-200 rounded-full h-2 overflow-hidden;
}

.progress-fill {
    @apply h-full rounded-full transition-all duration-500 ease-out;
    background: var(--primary-gradient);
}

/* Custom Scrollbar */
.custom-scrollbar::-webkit-scrollbar {
    width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Line Clamp Utilities */
.line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Glass Morphism Effect */
.glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-dark {
    background: rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Floating Action Button */
.fab {
    @apply fixed bottom-6 right-6 w-14 h-14 rounded-full shadow-lg flex items-center justify-center text-white transition-all duration-300 z-50;
    background: var(--primary-gradient);
}

.fab:hover {
    @apply shadow-xl transform scale-110;
}

/* Notification Dot */
.notification-dot {
    @apply absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full border-2 border-white;
}

.notification-dot.pulse {
    animation: pulse-dot 2s infinite;
}

@keyframes pulse-dot {
    0% {
        transform: scale(0.95);
        box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
    }

    70% {
        transform: scale(1);
        box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
    }

    100% {
        transform: scale(0.95);
        box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
    }
}

/* Skeleton Loading */
.skeleton {
    @apply bg-gray-200 rounded animate-pulse;
}

.skeleton-text {
    @apply h-4 bg-gray-200 rounded animate-pulse;
}

.skeleton-avatar {
    @apply w-10 h-10 bg-gray-200 rounded-full animate-pulse;
}

/* Custom Focus Styles */
.focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
}

/* Responsive Utilities */
@media (max-width: 640px) {
    .mobile-hidden {
        display: none !important;
    }
}

@media (min-width: 641px) {
    .mobile-only {
        display: none !important;
    }
}

/* Print Styles */
@media print {
    .no-print {
        display: none !important;
    }

    .print-break {
        page-break-before: always;
    }

    .print-avoid-break {
        page-break-inside: avoid;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .dark-mode-auto {
        @apply bg-gray-900 text-white;
    }

    .dark-mode-auto .card {
        @apply bg-gray-800 border-gray-700;
    }

    .dark-mode-auto .form-input {
        @apply bg-gray-800 border-gray-600 text-white;
    }
}

/* Accessibility Improvements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus visible for better accessibility */
.focus-visible:focus {
    outline: 2px solid #2563eb;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .btn-primary {
        border: 2px solid #000;
    }

    .card {
        border: 1px solid #000;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}