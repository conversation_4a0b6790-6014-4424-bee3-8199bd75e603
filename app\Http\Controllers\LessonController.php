<?php

namespace App\Http\Controllers;

use App\Models\Course;
use App\Models\Lesson;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class LessonController extends Controller
{
    /**
     * Display the specified lesson.
     */
    public function show(Course $course, Lesson $lesson)
    {
        // Check if lesson belongs to course
        if ($lesson->course_id !== $course->id) {
            abort(404);
        }

        // Check if lesson is published
        if (!$lesson->is_published) {
            abort(404);
        }

        $user = Auth::user();

        // Check if user can access this lesson
        if (!$lesson->canBeAccessedBy($user)) {
            return redirect()->route('courses.show', $course)
                ->withErrors(['access' => 'You need an active subscription to access this lesson.']);
        }

        // Mark lesson as started
        if ($user) {
            $lesson->markAsStartedBy($user->id);
        }

        // Load course with lessons for navigation
        $course->load(['publishedLessons']);

        // Get user progress for all lessons in this course
        $lessonProgress = [];
        if ($user) {
            foreach ($course->publishedLessons as $courseLesson) {
                $lessonProgress[$courseLesson->id] = [
                    'completed' => $courseLesson->isCompletedBy($user->id),
                    'watch_time' => $courseLesson->getWatchTimeForUser($user->id),
                    'percentage' => $courseLesson->getCompletionPercentageForUser($user->id),
                ];
            }
        }

        // Get next and previous lessons
        $currentIndex = $course->publishedLessons->search(function ($item) use ($lesson) {
            return $item->id === $lesson->id;
        });

        $previousLesson = $currentIndex > 0 ? $course->publishedLessons[$currentIndex - 1] : null;
        $nextLesson = $currentIndex < $course->publishedLessons->count() - 1 ?
            $course->publishedLessons[$currentIndex + 1] : null;

        return view('lessons.show', compact(
            'course',
            'lesson',
            'lessonProgress',
            'previousLesson',
            'nextLesson'
        ));
    }

    /**
     * Mark lesson as completed.
     */
    public function complete(Course $course, Lesson $lesson)
    {
        $user = Auth::user();

        // Check if lesson belongs to course
        if ($lesson->course_id !== $course->id) {
            abort(404);
        }

        // Check if user can access this lesson
        if (!$lesson->canBeAccessedBy($user)) {
            return response()->json(['error' => 'Access denied'], 403);
        }

        // Mark lesson as completed
        $lesson->markAsCompletedBy($user->id);

        // Get updated course completion percentage
        $completionPercentage = $course->getCompletionPercentageForUser($user->id);

        return response()->json([
            'success' => true,
            'message' => 'Lesson marked as completed!',
            'completion_percentage' => $completionPercentage,
        ]);
    }

    /**
     * Update watch time for lesson.
     */
    public function updateWatchTime(Request $request, Course $course, Lesson $lesson)
    {
        $user = Auth::user();

        // Check if lesson belongs to course
        if ($lesson->course_id !== $course->id) {
            abort(404);
        }

        // Check if user can access this lesson
        if (!$lesson->canBeAccessedBy($user)) {
            return response()->json(['error' => 'Access denied'], 403);
        }

        $request->validate([
            'watch_time' => 'required|integer|min:0',
        ]);

        // Update watch time
        $lesson->updateWatchTimeForUser($user->id, $request->watch_time);

        // Calculate completion percentage based on watch time
        $completionPercentage = 0;
        if ($lesson->duration_minutes > 0) {
            $durationSeconds = $lesson->duration_minutes * 60;
            $completionPercentage = min(100, ($request->watch_time / $durationSeconds) * 100);
        }

        // Update completion percentage in progress
        $lesson->userProgress()
            ->where('user_id', $user->id)
            ->update(['completion_percentage' => $completionPercentage]);

        return response()->json([
            'success' => true,
            'completion_percentage' => $completionPercentage,
        ]);
    }

    /**
     * Download lesson resources.
     */
    public function downloadResource(Course $course, Lesson $lesson, $resourceIndex)
    {
        $user = Auth::user();

        // Check if lesson belongs to course
        if ($lesson->course_id !== $course->id) {
            abort(404);
        }

        // Check if user can access this lesson
        if (!$lesson->canBeAccessedBy($user)) {
            abort(403);
        }

        // Check if resource exists
        $resources = $lesson->resources ?? [];
        if (!isset($resources[$resourceIndex])) {
            abort(404);
        }

        $resource = $resources[$resourceIndex];

        // In a real implementation, you would serve the file from storage
        // For now, we'll just redirect to the resource URL
        if (isset($resource['url'])) {
            return redirect($resource['url']);
        }

        abort(404);
    }
}
