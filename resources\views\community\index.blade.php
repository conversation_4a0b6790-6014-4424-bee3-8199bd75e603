@extends('layouts.app')

@section('content')
<!-- Hero Section -->
<div class="bg-gradient-to-r from-green-600 to-blue-700 text-white py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl md:text-5xl font-bold mb-4">
                The Real World Community
            </h1>
            <p class="text-xl md:text-2xl text-green-100 mb-8">
                Connect, share, and grow with like-minded entrepreneurs
            </p>
            @auth
                <a href="{{ route('community.create') }}" 
                   class="bg-white text-green-600 hover:bg-gray-100 px-8 py-3 rounded-lg font-bold text-lg transition duration-300">
                    Create New Post
                </a>
            @endauth
        </div>
    </div>
</div>

<!-- Filters and Search -->
<div class="bg-white border-b">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <form method="GET" action="{{ route('community.index') }}" class="flex flex-wrap items-center gap-4">
            <!-- Search -->
            <div class="flex-1 min-w-64">
                <input type="text" name="search" placeholder="Search posts..." 
                       value="{{ request('search') }}"
                       class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            </div>
            
            <!-- Category Filter -->
            <div>
                <select name="category" class="border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">All Categories</option>
                    @foreach($categories as $category)
                        <option value="{{ $category }}" {{ request('category') == $category ? 'selected' : '' }}>
                            {{ $category }}
                        </option>
                    @endforeach
                </select>
            </div>
            
            <!-- Sort Filter -->
            <div>
                <select name="sort" class="border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="activity" {{ request('sort') == 'activity' ? 'selected' : '' }}>Latest Activity</option>
                    <option value="popular" {{ request('sort') == 'popular' ? 'selected' : '' }}>Most Popular</option>
                    <option value="newest" {{ request('sort') == 'newest' ? 'selected' : '' }}>Newest</option>
                </select>
            </div>
            
            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition duration-300">
                Filter
            </button>
            
            @if(request()->hasAny(['search', 'category', 'sort']))
                <a href="{{ route('community.index') }}" class="text-gray-600 hover:text-gray-800 px-4 py-2">
                    Clear
                </a>
            @endif
        </form>
    </div>
</div>

<!-- Community Posts -->
<div class="py-8 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        @if($posts->count() > 0)
            <div class="space-y-6">
                @foreach($posts as $post)
                <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition duration-300">
                    <div class="p-6">
                        <!-- Post Header -->
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex items-center">
                                @if($post->user->avatar)
                                    <img src="{{ $post->user->avatar }}" alt="{{ $post->user->name }}" class="w-10 h-10 rounded-full mr-3">
                                @else
                                    <div class="w-10 h-10 bg-gray-300 rounded-full mr-3 flex items-center justify-center">
                                        <span class="text-sm text-gray-600 font-medium">{{ substr($post->user->name, 0, 1) }}</span>
                                    </div>
                                @endif
                                <div>
                                    <h3 class="font-medium text-gray-900">{{ $post->user->name }}</h3>
                                    <p class="text-sm text-gray-600">{{ $post->created_at->diffForHumans() }}</p>
                                </div>
                            </div>
                            
                            <!-- Post Meta -->
                            <div class="flex items-center space-x-4 text-sm text-gray-500">
                                @if($post->category)
                                    <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">{{ $post->category }}</span>
                                @endif
                                @if($post->is_pinned)
                                    <span class="text-yellow-600">📌 Pinned</span>
                                @endif
                            </div>
                        </div>
                        
                        <!-- Post Title -->
                        <h2 class="text-xl font-semibold text-gray-900 mb-3">
                            <a href="{{ route('community.show', $post) }}" class="hover:text-blue-600 transition duration-300">
                                {{ $post->title }}
                            </a>
                        </h2>
                        
                        <!-- Post Excerpt -->
                        <div class="text-gray-700 mb-4 line-clamp-3">
                            {{ $post->excerpt }}
                        </div>
                        
                        <!-- Tags -->
                        @if($post->formatted_tags)
                            <div class="flex flex-wrap gap-2 mb-4">
                                @foreach($post->formatted_tags as $tag)
                                    <span class="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs">
                                        #{{ $tag }}
                                    </span>
                                @endforeach
                            </div>
                        @endif
                        
                        <!-- Post Stats -->
                        <div class="flex items-center justify-between pt-4 border-t border-gray-200">
                            <div class="flex items-center space-x-6 text-sm text-gray-600">
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                    </svg>
                                    {{ $post->likes_count }} likes
                                </div>
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                    </svg>
                                    {{ $post->comments_count }} comments
                                </div>
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                    </svg>
                                    {{ $post->views_count }} views
                                </div>
                            </div>
                            
                            <div class="flex items-center space-x-2">
                                @auth
                                    @if($post->user_id === Auth::id())
                                        <a href="{{ route('community.edit', $post) }}" 
                                           class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                            Edit
                                        </a>
                                    @endif
                                @endauth
                                
                                <a href="{{ route('community.show', $post) }}" 
                                   class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm font-medium transition duration-300">
                                    Read More
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
            
            <!-- Pagination -->
            <div class="mt-8">
                {{ $posts->appends(request()->query())->links() }}
            </div>
        @else
            <div class="text-center py-12">
                <div class="max-w-md mx-auto">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No posts found</h3>
                    <p class="mt-1 text-sm text-gray-500">
                        @if(request('search'))
                            No posts match your search criteria.
                        @else
                            Be the first to start a conversation!
                        @endif
                    </p>
                    @auth
                        <div class="mt-6">
                            <a href="{{ route('community.create') }}" 
                               class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition duration-300">
                                Create First Post
                            </a>
                        </div>
                    @endauth
                </div>
            </div>
        @endif
    </div>
</div>

<!-- Community Stats -->
<div class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Join Our Growing Community</h2>
            <p class="text-lg text-gray-600">Connect with entrepreneurs from around the world</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div class="text-center">
                <div class="text-3xl font-bold text-blue-600 mb-2">{{ number_format($posts->total()) }}</div>
                <div class="text-gray-600">Total Posts</div>
            </div>
            <div class="text-center">
                <div class="text-3xl font-bold text-green-600 mb-2">10K+</div>
                <div class="text-gray-600">Active Members</div>
            </div>
            <div class="text-center">
                <div class="text-3xl font-bold text-purple-600 mb-2">50K+</div>
                <div class="text-gray-600">Comments</div>
            </div>
            <div class="text-center">
                <div class="text-3xl font-bold text-yellow-600 mb-2">24/7</div>
                <div class="text-gray-600">Active Community</div>
            </div>
        </div>
    </div>
</div>

@guest
<!-- CTA Section -->
<div class="py-16 bg-green-600">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl font-bold text-white mb-4">Ready to Join the Community?</h2>
        <p class="text-xl text-green-100 mb-8">Connect with successful entrepreneurs and grow your network</p>
        <div class="space-x-4">
            <a href="{{ route('register') }}" class="bg-white text-green-600 hover:bg-gray-100 px-8 py-3 rounded-lg font-medium transition duration-300">
                Sign Up Now
            </a>
            <a href="{{ route('login') }}" class="border border-white text-white hover:bg-white hover:text-green-600 px-8 py-3 rounded-lg font-medium transition duration-300">
                Sign In
            </a>
        </div>
    </div>
</div>
@endguest
@endsection
