<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\LiveCall;
use App\Models\User;
use App\Models\Course;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class LiveCallManagementController extends Controller
{
    /**
     * Display a listing of live calls.
     */
    public function index(Request $request)
    {
        $query = LiveCall::with(['mentor', 'course', 'attendances.user']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhereHas('mentor', function ($mentorQuery) use ($search) {
                      $mentorQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by mentor
        if ($request->filled('mentor')) {
            $query->where('mentor_id', $request->mentor);
        }

        // Filter by course
        if ($request->filled('course')) {
            $query->where('course_id', $request->course);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('scheduled_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('scheduled_at', '<=', $request->date_to);
        }

        // Sort
        $sortBy = $request->get('sort', 'scheduled_at');
        $sortDirection = $request->get('direction', 'desc');
        $query->orderBy($sortBy, $sortDirection);

        $liveCalls = $query->paginate(20);

        // Get filter options
        $mentors = User::whereHas('roles', function ($q) {
            $q->where('name', 'mentor');
        })->get();

        $courses = Course::where('is_published', true)->get();

        // Statistics
        $stats = [
            'total_calls' => LiveCall::count(),
            'upcoming_calls' => LiveCall::where('status', 'scheduled')->where('scheduled_at', '>', now())->count(),
            'completed_calls' => LiveCall::where('status', 'completed')->count(),
            'total_attendees' => DB::table('live_call_attendances')->count(),
        ];

        return view('admin.live-calls.index', compact('liveCalls', 'mentors', 'courses', 'stats'));
    }

    /**
     * Show the form for creating a new live call.
     */
    public function create()
    {
        $mentors = User::whereHas('roles', function ($q) {
            $q->where('name', 'mentor');
        })->get();

        $courses = Course::where('is_published', true)->get();

        return view('admin.live-calls.create', compact('mentors', 'courses'));
    }

    /**
     * Store a newly created live call.
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'mentor_id' => 'required|exists:users,id',
            'course_id' => 'nullable|exists:courses,id',
            'scheduled_at' => 'required|date|after:now',
            'duration_minutes' => 'required|integer|min:15|max:480',
            'max_attendees' => 'nullable|integer|min:1',
            'meeting_url' => 'nullable|url',
            'meeting_password' => 'nullable|string|max:50',
            'is_recorded' => 'boolean',
            'required_subscription_plans' => 'array',
        ]);

        $liveCall = LiveCall::create([
            'title' => $request->title,
            'description' => $request->description,
            'mentor_id' => $request->mentor_id,
            'course_id' => $request->course_id,
            'scheduled_at' => $request->scheduled_at,
            'duration_minutes' => $request->duration_minutes,
            'max_attendees' => $request->max_attendees,
            'meeting_url' => $request->meeting_url,
            'meeting_password' => $request->meeting_password,
            'is_recorded' => $request->boolean('is_recorded'),
            'required_subscription_plans' => $request->required_subscription_plans ?? [],
            'status' => 'scheduled',
        ]);

        return redirect()->route('admin.live-calls.index')
                        ->with('success', 'Live call created successfully.');
    }

    /**
     * Display the specified live call.
     */
    public function show(LiveCall $liveCall)
    {
        $liveCall->load(['mentor', 'course', 'attendances.user']);

        return view('admin.live-calls.show', compact('liveCall'));
    }

    /**
     * Show the form for editing the live call.
     */
    public function edit(LiveCall $liveCall)
    {
        $mentors = User::whereHas('roles', function ($q) {
            $q->where('name', 'mentor');
        })->get();

        $courses = Course::where('is_published', true)->get();

        return view('admin.live-calls.edit', compact('liveCall', 'mentors', 'courses'));
    }

    /**
     * Update the specified live call.
     */
    public function update(Request $request, LiveCall $liveCall)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'mentor_id' => 'required|exists:users,id',
            'course_id' => 'nullable|exists:courses,id',
            'scheduled_at' => 'required|date',
            'duration_minutes' => 'required|integer|min:15|max:480',
            'max_attendees' => 'nullable|integer|min:1',
            'meeting_url' => 'nullable|url',
            'meeting_password' => 'nullable|string|max:50',
            'is_recorded' => 'boolean',
            'required_subscription_plans' => 'array',
        ]);

        $liveCall->update([
            'title' => $request->title,
            'description' => $request->description,
            'mentor_id' => $request->mentor_id,
            'course_id' => $request->course_id,
            'scheduled_at' => $request->scheduled_at,
            'duration_minutes' => $request->duration_minutes,
            'max_attendees' => $request->max_attendees,
            'meeting_url' => $request->meeting_url,
            'meeting_password' => $request->meeting_password,
            'is_recorded' => $request->boolean('is_recorded'),
            'required_subscription_plans' => $request->required_subscription_plans ?? [],
        ]);

        return redirect()->route('admin.live-calls.index')
                        ->with('success', 'Live call updated successfully.');
    }

    /**
     * Remove the specified live call.
     */
    public function destroy(LiveCall $liveCall)
    {
        // Check if call has attendees
        if ($liveCall->attendances()->count() > 0) {
            return redirect()->route('admin.live-calls.index')
                            ->with('error', 'Cannot delete live call with registered attendees.');
        }

        $liveCall->delete();

        return redirect()->route('admin.live-calls.index')
                        ->with('success', 'Live call deleted successfully.');
    }

    /**
     * Start the live call.
     */
    public function start(LiveCall $liveCall)
    {
        if ($liveCall->status !== 'scheduled') {
            return redirect()->back()->with('error', 'Only scheduled calls can be started.');
        }

        $liveCall->update([
            'status' => 'live',
            'started_at' => now(),
        ]);

        return redirect()->route('admin.live-calls.show', $liveCall)
                        ->with('success', 'Live call started successfully.');
    }

    /**
     * End the live call.
     */
    public function end(LiveCall $liveCall)
    {
        if ($liveCall->status !== 'live') {
            return redirect()->back()->with('error', 'Only live calls can be ended.');
        }

        $liveCall->update([
            'status' => 'completed',
            'ended_at' => now(),
        ]);

        return redirect()->route('admin.live-calls.show', $liveCall)
                        ->with('success', 'Live call ended successfully.');
    }

    /**
     * Cancel the live call.
     */
    public function cancel(LiveCall $liveCall)
    {
        if ($liveCall->status === 'completed') {
            return redirect()->back()->with('error', 'Cannot cancel completed calls.');
        }

        $liveCall->update([
            'status' => 'cancelled',
            'cancelled_at' => now(),
        ]);

        // TODO: Send notifications to registered attendees

        return redirect()->route('admin.live-calls.show', $liveCall)
                        ->with('success', 'Live call cancelled successfully.');
    }

    /**
     * View attendees for the live call.
     */
    public function attendees(LiveCall $liveCall)
    {
        $attendances = $liveCall->attendances()
                               ->with('user')
                               ->orderBy('registered_at', 'desc')
                               ->paginate(50);

        return view('admin.live-calls.attendees', compact('liveCall', 'attendances'));
    }

    /**
     * Export attendees list.
     */
    public function exportAttendees(LiveCall $liveCall)
    {
        $attendances = $liveCall->attendances()->with('user')->get();

        $csvData = [];
        $csvData[] = ['Name', 'Email', 'Registered At', 'Attended', 'Join Time', 'Leave Time'];

        foreach ($attendances as $attendance) {
            $csvData[] = [
                $attendance->user->name,
                $attendance->user->email,
                $attendance->registered_at->format('Y-m-d H:i:s'),
                $attendance->attended ? 'Yes' : 'No',
                $attendance->joined_at ? $attendance->joined_at->format('Y-m-d H:i:s') : '',
                $attendance->left_at ? $attendance->left_at->format('Y-m-d H:i:s') : '',
            ];
        }

        $filename = 'live-call-attendees-' . $liveCall->id . '-' . now()->format('Y-m-d') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($csvData) {
            $file = fopen('php://output', 'w');
            foreach ($csvData as $row) {
                fputcsv($file, $row);
            }
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Bulk actions for live calls.
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:cancel,delete,start',
            'live_call_ids' => 'required|array',
            'live_call_ids.*' => 'exists:live_calls,id',
        ]);

        $liveCalls = LiveCall::whereIn('id', $request->live_call_ids)->get();
        $action = $request->action;
        $count = 0;

        foreach ($liveCalls as $liveCall) {
            switch ($action) {
                case 'cancel':
                    if ($liveCall->status !== 'completed') {
                        $liveCall->update([
                            'status' => 'cancelled',
                            'cancelled_at' => now(),
                        ]);
                        $count++;
                    }
                    break;

                case 'delete':
                    if ($liveCall->attendances()->count() === 0) {
                        $liveCall->delete();
                        $count++;
                    }
                    break;

                case 'start':
                    if ($liveCall->status === 'scheduled') {
                        $liveCall->update([
                            'status' => 'live',
                            'started_at' => now(),
                        ]);
                        $count++;
                    }
                    break;
            }
        }

        $actionText = [
            'cancel' => 'cancelled',
            'delete' => 'deleted',
            'start' => 'started',
        ];

        return redirect()->route('admin.live-calls.index')
                        ->with('success', "{$count} live calls {$actionText[$action]} successfully.");
    }
}
