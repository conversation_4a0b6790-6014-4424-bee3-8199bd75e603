<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class RoleMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, ...$roles): Response
    {
        if (!auth()->check()) {
            return redirect()->route('login');
        }

        $user = auth()->user();

        // Check if user has any of the required roles
        foreach ($roles as $role) {
            // Check using <PERSON><PERSON>'s hasRole method if it exists
            if (method_exists($user, 'hasRole') && $user->hasRole($role)) {
                return $next($request);
            }

            // Fallback to simple role field comparison
            if (isset($user->role) && $user->role === $role) {
                return $next($request);
            }
        }

        // If user doesn't have required role, redirect based on their role
        return $this->redirectBasedOnRole($user);
    }

    /**
     * Redirect user based on their role
     */
    private function redirectBasedOnRole($user)
    {
        $userRole = $user->role ?? 'student';

        switch ($userRole) {
            case 'admin':
                return redirect()->route('admin.dashboard')
                    ->with('error', 'Access denied. You don\'t have permission to access this area.');

            case 'mentor':
                return redirect()->route('mentor.dashboard')
                    ->with('error', 'Access denied. You don\'t have permission to access this area.');

            case 'student':
            default:
                return redirect()->route('dashboard')
                    ->with('error', 'Access denied. You don\'t have permission to access this area.');
        }
    }
}
