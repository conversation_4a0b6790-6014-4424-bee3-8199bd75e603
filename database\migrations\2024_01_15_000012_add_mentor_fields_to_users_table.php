<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Add role field if it doesn't exist
            if (!Schema::hasColumn('users', 'role')) {
                $table->enum('role', ['student', 'mentor', 'admin'])->default('student')->after('timezone');
            }
            
            // Add mentor-specific fields
            if (!Schema::hasColumn('users', 'title')) {
                $table->string('title')->nullable()->after('role'); // Professional title for mentors
            }
            
            if (!Schema::hasColumn('users', 'specialties')) {
                $table->json('specialties')->nullable()->after('title'); // Areas of expertise
            }
            
            if (!Schema::hasColumn('users', 'social_links')) {
                $table->json('social_links')->nullable()->after('specialties'); // Social media links
            }
            
            if (!Schema::hasColumn('users', 'portfolio_url')) {
                $table->string('portfolio_url')->nullable()->after('social_links'); // Portfolio/website URL
            }
            
            if (!Schema::hasColumn('users', 'is_verified')) {
                $table->boolean('is_verified')->default(false)->after('is_active'); // Verified mentor status
            }
            
            if (!Schema::hasColumn('users', 'is_featured')) {
                $table->boolean('is_featured')->default(false)->after('is_verified'); // Featured mentor status
            }
            
            if (!Schema::hasColumn('users', 'average_rating')) {
                $table->decimal('average_rating', 3, 1)->default(0)->after('is_featured'); // Average mentor rating
            }
        });

        // Add indexes for the new fields
        Schema::table('users', function (Blueprint $table) {
            $table->index('role');
            $table->index('is_verified');
            $table->index('is_featured');
            $table->index('average_rating');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex(['role']);
            $table->dropIndex(['is_verified']);
            $table->dropIndex(['is_featured']);
            $table->dropIndex(['average_rating']);
            
            $table->dropColumn([
                'role',
                'title',
                'specialties',
                'social_links',
                'portfolio_url',
                'is_verified',
                'is_featured',
                'average_rating'
            ]);
        });
    }
};
