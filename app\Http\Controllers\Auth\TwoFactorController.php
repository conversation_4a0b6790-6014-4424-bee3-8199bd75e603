<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use PragmaRX\Google2FA\Google2FA;
use BaconQrCode\Renderer\ImageRenderer;
use BaconQrCode\Renderer\Image\ImagickImageBackEnd;
use BaconQrCode\Renderer\RendererStyle\RendererStyle;
use BaconQrCode\Writer;

class TwoFactorController extends Controller
{
    protected $google2fa;

    public function __construct()
    {
        $this->google2fa = new Google2FA();
        $this->middleware('auth');
    }

    /**
     * Show the two-factor authentication setup page.
     */
    public function show()
    {
        $user = Auth::user();

        if (!$user->two_factor_secret) {
            $user->two_factor_secret = $this->google2fa->generateSecretKey();
            $user->save();
        }

        $qrCodeUrl = $this->google2fa->getQRCodeUrl(
            config('app.name'),
            $user->email,
            $user->two_factor_secret
        );

        $qrCode = $this->generateQRCode($qrCodeUrl);

        return view('auth.two-factor.setup', [
            'qrCode' => $qrCode,
            'secret' => $user->two_factor_secret,
            'enabled' => $user->two_factor_enabled,
        ]);
    }

    /**
     * Enable two-factor authentication.
     */
    public function enable(Request $request)
    {
        $request->validate([
            'code' => 'required|string|size:6',
        ]);

        $user = Auth::user();

        if (!$user->two_factor_secret) {
            return back()->withErrors(['code' => 'Two-factor authentication is not set up.']);
        }

        $valid = $this->google2fa->verifyKey($user->two_factor_secret, $request->code);

        if (!$valid) {
            return back()->withErrors(['code' => 'The provided code is invalid.']);
        }

        $user->update([
            'two_factor_enabled' => true,
            'two_factor_recovery_codes' => $this->generateRecoveryCodes(),
        ]);

        return redirect()->route('two-factor.show')
            ->with('success', 'Two-factor authentication has been enabled.');
    }

    /**
     * Disable two-factor authentication.
     */
    public function disable(Request $request)
    {
        $request->validate([
            'password' => 'required|current_password',
        ]);

        $user = Auth::user();

        $user->update([
            'two_factor_enabled' => false,
            'two_factor_secret' => null,
            'two_factor_recovery_codes' => null,
        ]);

        return redirect()->route('two-factor.show')
            ->with('success', 'Two-factor authentication has been disabled.');
    }

    /**
     * Show the two-factor challenge page.
     */
    public function challenge()
    {
        if (!session('two_factor_user_id')) {
            return redirect()->route('login');
        }

        return view('auth.two-factor.challenge');
    }

    /**
     * Verify the two-factor authentication code.
     */
    public function verify(Request $request)
    {
        $request->validate([
            'code' => 'required|string',
        ]);

        $userId = session('two_factor_user_id');

        if (!$userId) {
            return redirect()->route('login');
        }

        $user = \App\Models\User::find($userId);

        if (!$user || !$user->two_factor_enabled) {
            return redirect()->route('login');
        }

        $code = str_replace(' ', '', $request->code);

        // Check if it's a recovery code
        if (strlen($code) > 6) {
            return $this->verifyRecoveryCode($user, $code);
        }

        // Verify TOTP code
        $valid = $this->google2fa->verifyKey($user->two_factor_secret, $code);

        if (!$valid) {
            return back()->withErrors(['code' => 'The provided code is invalid.']);
        }

        // Clear the two-factor session
        session()->forget('two_factor_user_id');

        // Log the user in
        Auth::login($user);
        $user->updateLastLogin();

        return redirect()->intended('/dashboard');
    }

    /**
     * Verify recovery code.
     */
    protected function verifyRecoveryCode($user, $code)
    {
        $recoveryCodes = $user->two_factor_recovery_codes ?? [];

        if (!in_array($code, $recoveryCodes)) {
            return back()->withErrors(['code' => 'The provided recovery code is invalid.']);
        }

        // Remove the used recovery code
        $recoveryCodes = array_diff($recoveryCodes, [$code]);
        $user->update(['two_factor_recovery_codes' => array_values($recoveryCodes)]);

        // Clear the two-factor session
        session()->forget('two_factor_user_id');

        // Log the user in
        Auth::login($user);
        $user->updateLastLogin();

        return redirect()->intended('/dashboard')
            ->with('warning', 'You have used a recovery code. Please generate new recovery codes.');
    }

    /**
     * Generate new recovery codes.
     */
    public function generateRecoveryCodes()
    {
        $user = Auth::user();

        $recoveryCodes = [];
        for ($i = 0; $i < 8; $i++) {
            $recoveryCodes[] = strtoupper(substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 10));
        }

        $user->update(['two_factor_recovery_codes' => $recoveryCodes]);

        return redirect()->route('two-factor.show')
            ->with('success', 'New recovery codes have been generated.')
            ->with('recovery_codes', $recoveryCodes);
    }

    /**
     * Generate QR code for the secret.
     */
    protected function generateQRCode($url)
    {
        $renderer = new ImageRenderer(
            new RendererStyle(200),
            new ImagickImageBackEnd()
        );

        $writer = new Writer($renderer);

        return base64_encode($writer->writeString($url));
    }


}
