<?php

namespace Database\Seeders;

use App\Models\Payment;
use App\Models\UserSubscription;
use Illuminate\Database\Seeder;

class PaymentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $subscriptions = UserSubscription::with(['user', 'subscriptionPlan'])->get();

        foreach ($subscriptions as $subscription) {
            $this->createPaymentsForSubscription($subscription);
        }

        $this->command->info('Payments seeded successfully!');
    }

    private function createPaymentsForSubscription(UserSubscription $subscription)
    {
        $plan = $subscription->subscriptionPlan;
        $startDate = $subscription->current_period_start;
        $currentDate = $startDate->copy();

        // Create initial payment
        $this->createPayment($subscription, $currentDate, 'initial');

        // Create recurring payments based on subscription status
        if ($subscription->status === 'active') {
            // Create monthly payments up to current date
            while ($currentDate->addMonth()->lte(now())) {
                $this->createPayment($subscription, $currentDate->copy(), 'recurring');
            }
        } elseif ($subscription->status === 'expired' || $subscription->status === 'cancelled') {
            // Create payments until subscription ended
            $endDate = $subscription->current_period_end;
            while ($currentDate->addMonth()->lte($endDate)) {
                $this->createPayment($subscription, $currentDate->copy(), 'recurring');
            }
        }
    }

    private function createPayment(UserSubscription $subscription, $date, $type)
    {
        $plan = $subscription->subscriptionPlan;
        $paymentMethods = ['stripe', 'crypto'];
        $cryptoCurrencies = ['BTC', 'ETH', 'USDT', 'BNB'];

        $paymentMethod = $paymentMethods[array_rand($paymentMethods)];
        $status = $this->getPaymentStatus($subscription->status, $date);

        $paymentData = [
            'user_id' => $subscription->user_id,
            'subscription_id' => $subscription->id,
            'amount' => $subscription->amount,
            'currency' => 'USD',
            'payment_method' => $paymentMethod,
            'status' => $status,
            'transaction_id' => $this->generateTransactionId($paymentMethod),
            'created_at' => $date,
            'updated_at' => $date,
        ];

        if ($paymentMethod === 'stripe') {
            $paymentData['stripe_payment_intent_id'] = 'pi_' . \Str::random(24);
            $paymentData['stripe_charge_id'] = 'ch_' . \Str::random(24);
        } else {
            $currency = $cryptoCurrencies[array_rand($cryptoCurrencies)];
            $paymentData['crypto_currency'] = $currency;
            $paymentData['crypto_amount'] = $this->convertToCrypto($subscription->amount, $currency);
            $paymentData['crypto_address'] = $this->generateCryptoAddress($currency);
            $paymentData['blockchain_tx_hash'] = $this->generateBlockchainHash();
        }

        if ($status === 'completed') {
            $paymentData['paid_at'] = $date->addMinutes(rand(1, 30));
        } elseif ($status === 'failed') {
            $paymentData['failed_at'] = $date->addMinutes(rand(1, 10));
            $paymentData['failure_reason'] = $this->getFailureReason($paymentMethod);
        }

        Payment::create($paymentData);
    }

    private function getPaymentStatus($subscriptionStatus, $date)
    {
        if ($subscriptionStatus === 'active') {
            // 95% success rate for active subscriptions
            return rand(1, 100) <= 95 ? 'completed' : 'failed';
        } elseif ($subscriptionStatus === 'cancelled') {
            // If payment date is before cancellation, it was successful
            return 'completed';
        } elseif ($subscriptionStatus === 'expired') {
            // Last payment might have failed
            return rand(1, 100) <= 80 ? 'completed' : 'failed';
        }

        return 'completed';
    }

    private function generateTransactionId($paymentMethod)
    {
        $prefix = $paymentMethod === 'stripe' ? 'txn_' : 'crypto_';
        return $prefix . \Str::random(16);
    }

    private function convertToCrypto($usdAmount, $currency)
    {
        // Simplified conversion rates (in reality, these would be fetched from an API)
        $rates = [
            'BTC' => 45000,  // $45,000 per BTC
            'ETH' => 3000,   // $3,000 per ETH
            'USDT' => 1,     // $1 per USDT
            'BNB' => 300,    // $300 per BNB
        ];

        return round($usdAmount / $rates[$currency], 8);
    }

    private function generateCryptoAddress($currency)
    {
        $prefixes = [
            'BTC' => ['1', '3', 'bc1'],
            'ETH' => ['0x'],
            'USDT' => ['0x'],
            'BNB' => ['bnb'],
        ];

        $prefix = $prefixes[$currency][array_rand($prefixes[$currency])];
        $length = $currency === 'BTC' ? 34 : 42;

        return $prefix . \Str::random($length - strlen($prefix));
    }

    private function generateBlockchainHash()
    {
        return '0x' . \Str::random(64);
    }

    private function getFailureReason($paymentMethod)
    {
        $stripeReasons = [
            'insufficient_funds',
            'card_declined',
            'expired_card',
            'processing_error',
        ];

        $cryptoReasons = [
            'insufficient_balance',
            'network_congestion',
            'invalid_address',
            'transaction_timeout',
        ];

        $reasons = $paymentMethod === 'stripe' ? $stripeReasons : $cryptoReasons;
        return $reasons[array_rand($reasons)];
    }
}
