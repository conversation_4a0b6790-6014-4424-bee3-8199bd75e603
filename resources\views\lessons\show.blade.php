@extends('layouts.app')

@section('content')
<div class="min-h-screen bg-gray-900">
    <div class="max-w-7xl mx-auto">
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-0">
            <!-- Video Player -->
            <div class="lg:col-span-3 bg-black">
                <div class="aspect-video">
                    @if($lesson->video_url)
                        @if($lesson->video_provider === 'youtube')
                            @php
                                $videoId = '';
                                if (preg_match('/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/', $lesson->video_url, $matches)) {
                                    $videoId = $matches[1];
                                }
                            @endphp
                            @if($videoId)
                                <iframe src="https://www.youtube.com/embed/{{ $videoId }}?rel=0&modestbranding=1" 
                                        class="w-full h-full" 
                                        frameborder="0" 
                                        allowfullscreen
                                        id="video-player"></iframe>
                            @endif
                        @elseif($lesson->video_provider === 'vimeo')
                            <iframe src="{{ $lesson->video_url }}" 
                                    class="w-full h-full" 
                                    frameborder="0" 
                                    allowfullscreen
                                    id="video-player"></iframe>
                        @else
                            <video controls class="w-full h-full" id="video-player">
                                <source src="{{ $lesson->video_url }}" type="video/mp4">
                                Your browser does not support the video tag.
                            </video>
                        @endif
                    @else
                        <div class="w-full h-full flex items-center justify-center bg-gray-800">
                            <div class="text-center text-white">
                                <svg class="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                </svg>
                                <p class="text-lg">Video not available</p>
                            </div>
                        </div>
                    @endif
                </div>
                
                <!-- Video Controls -->
                <div class="bg-gray-800 text-white p-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            @if($previousLesson)
                                <a href="{{ route('lessons.show', [$course, $previousLesson]) }}" 
                                   class="flex items-center text-gray-300 hover:text-white transition duration-300">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                                    </svg>
                                    Previous
                                </a>
                            @endif
                            
                            @if($nextLesson)
                                <a href="{{ route('lessons.show', [$course, $nextLesson]) }}" 
                                   class="flex items-center text-gray-300 hover:text-white transition duration-300">
                                    Next
                                    <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                    </svg>
                                </a>
                            @endif
                        </div>
                        
                        <div class="flex items-center space-x-4">
                            <button onclick="markAsCompleted()" 
                                    id="complete-btn"
                                    class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded text-sm font-medium transition duration-300">
                                Mark as Complete
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Sidebar -->
            <div class="lg:col-span-1 bg-white">
                <!-- Course Info -->
                <div class="p-4 border-b">
                    <h2 class="font-semibold text-gray-900 mb-1">{{ $course->title }}</h2>
                    <p class="text-sm text-gray-600">{{ $course->mentor->name }}</p>
                </div>
                
                <!-- Current Lesson -->
                <div class="p-4 border-b bg-blue-50">
                    <h3 class="font-medium text-gray-900 mb-2">{{ $lesson->title }}</h3>
                    <div class="flex items-center text-sm text-gray-600 mb-2">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        {{ $lesson->formatted_duration }}
                    </div>
                    @if($lesson->description)
                        <p class="text-sm text-gray-700">{{ $lesson->description }}</p>
                    @endif
                </div>
                
                <!-- Lesson Resources -->
                @if($lesson->resources && count($lesson->resources) > 0)
                <div class="p-4 border-b">
                    <h4 class="font-medium text-gray-900 mb-3">Resources</h4>
                    <div class="space-y-2">
                        @foreach($lesson->resources as $index => $resource)
                        <a href="{{ route('lessons.resource', [$course, $lesson, $index]) }}" 
                           class="flex items-center text-sm text-blue-600 hover:text-blue-800 transition duration-300">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            {{ $resource['name'] ?? 'Download Resource' }}
                        </a>
                        @endforeach
                    </div>
                </div>
                @endif
                
                <!-- Course Lessons -->
                <div class="p-4">
                    <h4 class="font-medium text-gray-900 mb-3">Course Content</h4>
                    <div class="space-y-2 max-h-96 overflow-y-auto">
                        @foreach($course->publishedLessons as $courseLesson)
                        <div class="flex items-center p-2 rounded {{ $courseLesson->id === $lesson->id ? 'bg-blue-100' : 'hover:bg-gray-50' }}">
                            <div class="flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center mr-3 {{ $courseLesson->id === $lesson->id ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-600' }}">
                                @if(isset($lessonProgress[$courseLesson->id]) && $lessonProgress[$courseLesson->id]['completed'])
                                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                @else
                                    <span class="text-xs">{{ $loop->iteration }}</span>
                                @endif
                            </div>
                            
                            <div class="flex-1 min-w-0">
                                <a href="{{ route('lessons.show', [$course, $courseLesson]) }}" 
                                   class="block text-sm font-medium text-gray-900 truncate hover:text-blue-600 transition duration-300">
                                    {{ $courseLesson->title }}
                                </a>
                                <p class="text-xs text-gray-500">{{ $courseLesson->formatted_duration }}</p>
                            </div>
                            
                            @if($courseLesson->is_free)
                                <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Free</span>
                            @endif
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Lesson Content (below video) -->
<div class="bg-white">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="prose prose-lg max-w-none">
            <h1>{{ $lesson->title }}</h1>
            @if($lesson->content)
                {!! nl2br(e($lesson->content)) !!}
            @endif
        </div>
    </div>
</div>

<script>
let watchTimeInterval;
let currentWatchTime = 0;

// Track video watch time
function startWatchTimeTracking() {
    watchTimeInterval = setInterval(() => {
        currentWatchTime += 1;
        updateWatchTime();
    }, 1000);
}

function stopWatchTimeTracking() {
    if (watchTimeInterval) {
        clearInterval(watchTimeInterval);
    }
}

function updateWatchTime() {
    fetch('{{ route("lessons.watch-time", [$course, $lesson]) }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        },
        body: JSON.stringify({
            watch_time: currentWatchTime
        })
    });
}

function markAsCompleted() {
    fetch('{{ route("lessons.complete", [$course, $lesson]) }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('complete-btn').innerHTML = '✓ Completed';
            document.getElementById('complete-btn').classList.remove('bg-green-600', 'hover:bg-green-700');
            document.getElementById('complete-btn').classList.add('bg-gray-600');
            document.getElementById('complete-btn').disabled = true;
            
            // Show success message
            showNotification('Lesson marked as completed!', 'success');
            
            // Auto-redirect to next lesson after 2 seconds
            @if($nextLesson)
                setTimeout(() => {
                    window.location.href = '{{ route("lessons.show", [$course, $nextLesson]) }}';
                }, 2000);
            @endif
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Failed to mark lesson as completed', 'error');
    });
}

function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 px-6 py-3 rounded-lg text-white z-50 ${type === 'success' ? 'bg-green-600' : 'bg-red-600'}`;
    notification.textContent = message;
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// Start tracking when page loads
document.addEventListener('DOMContentLoaded', function() {
    startWatchTimeTracking();
    
    // Stop tracking when page unloads
    window.addEventListener('beforeunload', function() {
        stopWatchTimeTracking();
    });
});

// Check if lesson is already completed
@if(isset($lessonProgress[$lesson->id]) && $lessonProgress[$lesson->id]['completed'])
    document.addEventListener('DOMContentLoaded', function() {
        const completeBtn = document.getElementById('complete-btn');
        completeBtn.innerHTML = '✓ Completed';
        completeBtn.classList.remove('bg-green-600', 'hover:bg-green-700');
        completeBtn.classList.add('bg-gray-600');
        completeBtn.disabled = true;
    });
@endif
</script>
@endsection
