@extends('layouts.guest')

@section('title', 'Forgot Password')

@section('content')
<div class="auth-container">
    <div class="auth-card">
        <div class="auth-header">
            <div class="logo">
                <img src="{{ asset('images/logo.png') }}" alt="The Real World" class="logo-img">
            </div>
            <h1 class="auth-title">Reset Password</h1>
            <p class="auth-subtitle">Enter your email address and we'll send you a link to reset your password.</p>
        </div>

        @if (session('status'))
            <div class="alert alert-success" role="alert">
                <i class="fas fa-check-circle"></i>
                {{ session('status') }}
            </div>
        @endif

        <form method="POST" action="{{ route('password.email') }}" class="auth-form">
            @csrf

            <div class="form-group">
                <label for="email" class="form-label">
                    <i class="fas fa-envelope"></i>
                    Email Address
                </label>
                <input id="email" type="email" 
                       class="form-control @error('email') is-invalid @enderror" 
                       name="email" 
                       value="{{ old('email') }}" 
                       required 
                       autocomplete="email" 
                       autofocus
                       placeholder="Enter your email address">
                @error('email')
                    <div class="invalid-feedback">
                        <i class="fas fa-exclamation-circle"></i>
                        {{ $message }}
                    </div>
                @enderror
            </div>

            <button type="submit" class="btn btn-primary btn-block btn-lg">
                <i class="fas fa-paper-plane"></i>
                Send Password Reset Link
            </button>
        </form>

        <div class="auth-footer">
            <div class="auth-links">
                <a href="{{ route('login') }}" class="auth-link">
                    <i class="fas fa-arrow-left"></i>
                    Back to Login
                </a>
                <span class="auth-divider">•</span>
                <a href="{{ route('register') }}" class="auth-link">
                    <i class="fas fa-user-plus"></i>
                    Create Account
                </a>
            </div>
        </div>
    </div>

    <div class="auth-background">
        <div class="auth-bg-overlay"></div>
        <div class="auth-bg-content">
            <h2>Forgot Your Password?</h2>
            <p>No problem! It happens to the best of us. Enter your email address and we'll send you a secure link to reset your password.</p>
            <div class="auth-features">
                <div class="feature">
                    <i class="fas fa-shield-alt"></i>
                    <span>Secure Reset Process</span>
                </div>
                <div class="feature">
                    <i class="fas fa-clock"></i>
                    <span>Quick & Easy</span>
                </div>
                <div class="feature">
                    <i class="fas fa-lock"></i>
                    <span>Protected Account</span>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 2rem 1rem;
    position: relative;
}

.auth-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    padding: 3rem;
    width: 100%;
    max-width: 450px;
    position: relative;
    z-index: 2;
}

.auth-header {
    text-align: center;
    margin-bottom: 2rem;
}

.logo-img {
    height: 60px;
    margin-bottom: 1rem;
}

.auth-title {
    font-size: 2rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.auth-subtitle {
    color: #718096;
    font-size: 1rem;
    line-height: 1.5;
}

.auth-form {
    margin-bottom: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: flex;
    align-items: center;
    font-weight: 600;
    color: #4a5568;
    margin-bottom: 0.5rem;
}

.form-label i {
    margin-right: 0.5rem;
    color: #667eea;
}

.form-control {
    height: 50px;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    padding: 0 1rem;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-control.is-invalid {
    border-color: #e53e3e;
}

.invalid-feedback {
    display: flex;
    align-items: center;
    color: #e53e3e;
    font-size: 0.875rem;
    margin-top: 0.5rem;
}

.invalid-feedback i {
    margin-right: 0.5rem;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    height: 50px;
    border-radius: 10px;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.auth-footer {
    text-align: center;
}

.auth-links {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.auth-link {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    display: flex;
    align-items: center;
    transition: color 0.3s ease;
}

.auth-link:hover {
    color: #764ba2;
    text-decoration: none;
}

.auth-link i {
    margin-right: 0.5rem;
}

.auth-divider {
    color: #cbd5e0;
}

.auth-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 2rem;
    z-index: 1;
}

.auth-bg-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
}

.auth-bg-content {
    max-width: 400px;
    color: white;
    text-align: center;
    position: relative;
    z-index: 2;
    margin-right: 2rem;
}

.auth-bg-content h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.auth-bg-content p {
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.auth-features {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.feature {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-weight: 500;
}

.feature i {
    font-size: 1.2rem;
    color: #ffd700;
}

.alert {
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
}

.alert-success {
    background-color: #f0fff4;
    border: 1px solid #9ae6b4;
    color: #276749;
}

.alert i {
    margin-right: 0.5rem;
}

@media (max-width: 768px) {
    .auth-container {
        padding: 1rem;
    }
    
    .auth-card {
        padding: 2rem;
    }
    
    .auth-background {
        display: none;
    }
    
    .auth-title {
        font-size: 1.75rem;
    }
    
    .auth-bg-content h2 {
        font-size: 2rem;
    }
}
</style>
@endpush
