# The Real World LMS - Complete Route Documentation

This document provides a comprehensive overview of all routes in The Real World LMS application.

## 🏠 Public Routes

### Homepage & Information
- `GET /` - Homepage
- `GET /pricing` - Pricing plans page
- `GET /about` - About page
- `GET /contact` - Contact page
- `POST /contact` - Submit contact form
- `GET /faq` - FAQ page
- `GET /testimonials` - Testimonials page
- `GET /mentors` - Mentors listing
- `GET /mentors/{mentor}` - Individual mentor profile

### Search & Discovery
- `GET /search` - Global search page
- `GET /search/courses` - Course search
- `GET /search/mentors` - Mentor search
- `GET /search/suggestions` - Search suggestions (AJAX)

### Courses (Public)
- `GET /courses` - Course listing
- `GET /courses/search` - Course search
- `GET /courses/category/{category}` - Courses by category
- `GET /courses/featured` - Featured courses
- `GET /courses/free` - Free courses
- `GET /courses/{course}` - Course details
- `GET /courses/{course}/preview` - Course preview

### Live Calls (Public)
- `GET /live-calls` - Live calls listing
- `GET /live-calls/upcoming` - Upcoming calls
- `GET /live-calls/past` - Past calls
- `GET /live-calls/{liveCall}` - Live call details

### Community (Public)
- `GET /community` - Community posts
- `GET /community/posts/{post}` - Post details
- `GET /community/categories` - Community categories
- `GET /community/category/{category}` - Posts by category

### Certificates (Public)
- `GET /certificates/verify/{certificate}` - Verify certificate
- `GET /certificates/{certificate}/download` - Download certificate

## 🔐 Authentication Routes

### Guest Routes
- `GET /register` - Registration form
- `POST /register` - Process registration
- `GET /login` - Login form
- `POST /login` - Process login
- `GET /forgot-password` - Forgot password form
- `POST /forgot-password` - Send reset link
- `GET /reset-password/{token}` - Reset password form
- `POST /reset-password` - Process password reset

### Authenticated Routes
- `POST /logout` - Logout user
- `GET /email/verify` - Email verification notice
- `GET /email/verify/{id}/{hash}` - Verify email
- `POST /email/verification-notification` - Resend verification

## 👤 User Dashboard Routes

### Dashboard
- `GET /dashboard` - Main dashboard
- `GET /dashboard/courses` - User courses
- `GET /dashboard/progress` - Learning progress
- `GET /dashboard/certificates` - User certificates
- `GET /dashboard/stats` - User statistics
- `GET /dashboard/notifications` - User notifications

### Profile Management
- `GET /profile` - Profile overview
- `GET /profile/edit` - Edit profile form
- `PUT /profile` - Update profile
- `DELETE /profile` - Delete account
- `GET /profile/certificates` - Profile certificates
- `GET /profile/certificates/{certificate}` - Download certificate
- `GET /profile/password` - Change password form
- `PUT /profile/password` - Update password

### Notifications
- `GET /notifications` - Notifications list
- `POST /notifications/{notification}/read` - Mark as read
- `POST /notifications/read-all` - Mark all as read
- `DELETE /notifications/{notification}` - Delete notification
- `DELETE /notifications` - Delete all notifications

## 💳 Subscription & Payment Routes

### Subscriptions
- `GET /subscriptions` - Subscription management
- `GET /subscriptions/plans` - Available plans
- `GET /subscriptions/{plan}/checkout` - Checkout page
- `POST /subscriptions/subscribe` - Create subscription
- `POST /subscriptions/cancel` - Cancel subscription
- `POST /subscriptions/resume` - Resume subscription
- `POST /subscriptions/upgrade` - Upgrade subscription
- `POST /subscriptions/downgrade` - Downgrade subscription
- `POST /subscriptions/payment-method` - Update payment method
- `GET /subscriptions/success` - Success page
- `GET /subscriptions/cancelled` - Cancelled page
- `GET /subscriptions/invoices` - Invoice history
- `GET /subscriptions/invoices/{invoice}` - Download invoice

### Payments
- `GET /payments` - Payment history
- `GET /payments/{payment}` - Payment details
- `POST /payments/crypto/verify` - Verify crypto payment
- `GET /payments/crypto/{payment}/status` - Crypto payment status

## 📚 Course & Learning Routes

### Course Interaction
- `POST /courses/{course}/enroll` - Enroll in course
- `DELETE /courses/{course}/unenroll` - Unenroll from course
- `POST /courses/{course}/favorite` - Toggle favorite
- `POST /courses/{course}/rate` - Rate course
- `PUT /courses/{course}/rating/{rating}` - Update rating
- `DELETE /courses/{course}/rating/{rating}` - Delete rating

### Lesson Interaction
- `GET /lessons/{lesson}` - View lesson
- `POST /lessons/{lesson}/complete` - Mark lesson complete
- `POST /lessons/{lesson}/progress` - Update progress
- `POST /lessons/{lesson}/watch-time` - Update watch time
- `GET /lessons/{lesson}/resources/{resource}` - Download resource
- `POST /lessons/{lesson}/bookmark` - Toggle bookmark
- `POST /lessons/{lesson}/note` - Save note

## 📞 Live Call Routes

### Live Call Participation
- `POST /live-calls/{liveCall}/register` - Register for call
- `DELETE /live-calls/{liveCall}/unregister` - Unregister from call
- `GET /live-calls/{liveCall}/join` - Join live call
- `POST /live-calls/{liveCall}/reminder` - Set reminder
- `GET /live-calls/{liveCall}/recording` - View recording
- `POST /live-calls/{liveCall}/feedback` - Submit feedback

## 👥 Community Routes

### Community Participation
- `GET /community/create` - Create post form
- `POST /community` - Create post
- `GET /community/posts/{post}/edit` - Edit post form
- `PUT /community/posts/{post}` - Update post
- `DELETE /community/posts/{post}` - Delete post
- `POST /community/posts/{post}/comments` - Add comment
- `PUT /community/comments/{comment}` - Update comment
- `DELETE /community/comments/{comment}` - Delete comment
- `POST /community/posts/{post}/like` - Toggle post like
- `POST /community/comments/{comment}/like` - Toggle comment like
- `POST /community/posts/{post}/report` - Report post
- `POST /community/comments/{comment}/report` - Report comment
- `POST /community/posts/{post}/bookmark` - Toggle bookmark
- `GET /community/my-posts` - User's posts
- `GET /community/bookmarks` - Bookmarked posts

### Certificate Management
- `GET /certificates` - User certificates
- `GET /certificates/{certificate}` - Certificate details
- `POST /certificates/{certificate}/share` - Share certificate

## 🔧 Admin Routes

All admin routes are prefixed with `/admin` and require admin role.

### Admin Dashboard
- `GET /admin` - Admin dashboard
- `GET /admin/stats` - Admin statistics
- `GET /admin/overview` - System overview

### User Management
- `GET /admin/users` - User list
- `GET /admin/users/create` - Create user form
- `POST /admin/users` - Create user
- `GET /admin/users/{user}` - User details
- `GET /admin/users/{user}/edit` - Edit user form
- `PUT /admin/users/{user}` - Update user
- `DELETE /admin/users/{user}` - Delete user
- `POST /admin/users/{user}/toggle-status` - Toggle user status
- `POST /admin/users/{user}/impersonate` - Impersonate user
- `POST /admin/users/stop-impersonating` - Stop impersonating
- `GET /admin/users-export` - Export users
- `POST /admin/users/bulk-action` - Bulk user actions
- `GET /admin/users/{user}/activity` - User activity
- `POST /admin/users/{user}/send-notification` - Send notification

### Course Management
- `GET /admin/courses` - Course list
- `GET /admin/courses/create` - Create course form
- `POST /admin/courses` - Create course
- `GET /admin/courses/{course}` - Course details
- `GET /admin/courses/{course}/edit` - Edit course form
- `PUT /admin/courses/{course}` - Update course
- `DELETE /admin/courses/{course}` - Delete course
- `POST /admin/courses/{course}/toggle-published` - Toggle published
- `POST /admin/courses/{course}/toggle-featured` - Toggle featured
- `GET /admin/courses/{course}/lessons` - Course lessons
- `GET /admin/courses/{course}/analytics` - Course analytics
- `POST /admin/courses/{course}/duplicate` - Duplicate course
- `GET /admin/courses/{course}/students` - Course students
- `GET /admin/courses/{course}/reviews` - Course reviews

### Lesson Management
- `GET /admin/lessons` - Lesson list
- `GET /admin/courses/{course}/lessons/create` - Create lesson form
- `POST /admin/courses/{course}/lessons` - Create lesson
- `GET /admin/courses/{course}/lessons/{lesson}` - Lesson details
- `GET /admin/courses/{course}/lessons/{lesson}/edit` - Edit lesson form
- `PUT /admin/courses/{course}/lessons/{lesson}` - Update lesson
- `DELETE /admin/courses/{course}/lessons/{lesson}` - Delete lesson
- `POST /admin/lessons/{lesson}/toggle-published` - Toggle published
- `POST /admin/lessons/reorder` - Reorder lessons

### Category Management
- `GET /admin/categories` - Category list
- `GET /admin/categories/create` - Create category form
- `POST /admin/categories` - Create category
- `GET /admin/categories/{category}` - Category details
- `GET /admin/categories/{category}/edit` - Edit category form
- `PUT /admin/categories/{category}` - Update category
- `DELETE /admin/categories/{category}` - Delete category
- `POST /admin/categories/{category}/toggle-active` - Toggle active
- `POST /admin/categories/reorder` - Reorder categories

### Subscription Management
- `GET /admin/subscriptions` - Subscription list
- `GET /admin/subscriptions/{subscription}` - Subscription details
- `GET /admin/subscriptions/{subscription}/edit` - Edit subscription form
- `PUT /admin/subscriptions/{subscription}` - Update subscription
- `DELETE /admin/subscriptions/{subscription}` - Delete subscription
- `GET /admin/subscription-plans` - Subscription plans
- `POST /admin/subscription-plans` - Create plan
- `PUT /admin/subscription-plans/{plan}` - Update plan
- `DELETE /admin/subscription-plans/{plan}` - Delete plan
- `POST /admin/subscriptions/{subscription}/cancel` - Cancel subscription
- `POST /admin/subscriptions/{subscription}/resume` - Resume subscription

### Payment Management
- `GET /admin/payments` - Payment list
- `GET /admin/payments/{payment}` - Payment details
- `POST /admin/payments/{payment}/refund` - Refund payment
- `GET /admin/payments/export` - Export payments
- `GET /admin/revenue` - Revenue analytics

### Live Call Management
- `GET /admin/live-calls` - Live call list
- `GET /admin/live-calls/create` - Create live call form
- `POST /admin/live-calls` - Create live call
- `GET /admin/live-calls/{liveCall}` - Live call details
- `GET /admin/live-calls/{liveCall}/edit` - Edit live call form
- `PUT /admin/live-calls/{liveCall}` - Update live call
- `DELETE /admin/live-calls/{liveCall}` - Delete live call
- `POST /admin/live-calls/{liveCall}/toggle-status` - Toggle status
- `GET /admin/live-calls/{liveCall}/attendees` - Call attendees
- `POST /admin/live-calls/{liveCall}/send-reminder` - Send reminder

### Community Management
- `GET /admin/community` - Community overview
- `GET /admin/community/posts` - Community posts
- `GET /admin/community/posts/{post}` - Post details
- `POST /admin/community/posts/{post}/approve` - Approve post
- `POST /admin/community/posts/{post}/reject` - Reject post
- `DELETE /admin/community/posts/{post}` - Delete post
- `GET /admin/community/reports` - Community reports
- `POST /admin/community/reports/{report}/resolve` - Resolve report

### Notification Management
- `GET /admin/notifications` - Notification management
- `POST /admin/notifications/send` - Send notification
- `POST /admin/notifications/broadcast` - Broadcast notification
- `GET /admin/notifications/templates` - Notification templates
- `POST /admin/notifications/templates` - Create template

### Analytics
- `GET /admin/analytics` - Analytics dashboard
- `GET /admin/analytics/users` - User analytics
- `GET /admin/analytics/courses` - Course analytics
- `GET /admin/analytics/revenue` - Revenue analytics
- `GET /admin/analytics/engagement` - Engagement analytics
- `GET /admin/analytics/export` - Export analytics

### Settings
- `GET /admin/settings` - Settings page
- `PUT /admin/settings/general` - Update general settings
- `PUT /admin/settings/email` - Update email settings
- `PUT /admin/settings/payment` - Update payment settings
- `PUT /admin/settings/security` - Update security settings
- `POST /admin/settings/test-email` - Test email configuration
- `POST /admin/settings/clear-cache` - Clear cache
- `GET /admin/settings/backup` - Backup management
- `POST /admin/settings/backup/create` - Create backup

### System Maintenance
- `POST /admin/maintenance/enable` - Enable maintenance mode
- `POST /admin/maintenance/disable` - Disable maintenance mode
- `GET /admin/logs` - System logs
- `GET /admin/logs/{file}` - View log file
- `DELETE /admin/logs/{file}` - Delete log file

## 👨‍🏫 Mentor Routes

All mentor routes are prefixed with `/mentor` and require mentor role.

### Mentor Dashboard
- `GET /mentor` - Mentor dashboard
- `GET /mentor/stats` - Mentor statistics
- `GET /mentor/overview` - Mentor overview

### Mentor Profile
- `GET /mentor/profile` - Mentor profile
- `GET /mentor/profile/edit` - Edit profile form
- `PUT /mentor/profile` - Update profile
- `POST /mentor/profile/avatar` - Update avatar
- `DELETE /mentor/profile/avatar` - Delete avatar

### Mentor Course Management
- `GET /mentor/courses` - Mentor courses
- `GET /mentor/courses/create` - Create course form
- `POST /mentor/courses` - Create course
- `GET /mentor/courses/{course}` - Course details
- `GET /mentor/courses/{course}/edit` - Edit course form
- `PUT /mentor/courses/{course}` - Update course
- `DELETE /mentor/courses/{course}` - Delete course
- `POST /mentor/courses/{course}/publish` - Publish course
- `POST /mentor/courses/{course}/unpublish` - Unpublish course
- `POST /mentor/courses/{course}/duplicate` - Duplicate course
- `GET /mentor/courses/{course}/analytics` - Course analytics
- `GET /mentor/courses/{course}/students` - Course students
- `GET /mentor/courses/{course}/reviews` - Course reviews

### Mentor Lesson Management
- `GET /mentor/lessons` - All lessons
- `GET /mentor/courses/{course}/lessons/create` - Create lesson form
- `POST /mentor/courses/{course}/lessons` - Create lesson
- `GET /mentor/courses/{course}/lessons/{lesson}` - Lesson details
- `GET /mentor/courses/{course}/lessons/{lesson}/edit` - Edit lesson form
- `PUT /mentor/courses/{course}/lessons/{lesson}` - Update lesson
- `DELETE /mentor/courses/{course}/lessons/{lesson}` - Delete lesson
- `POST /mentor/lessons/{lesson}/publish` - Publish lesson
- `POST /mentor/lessons/{lesson}/unpublish` - Unpublish lesson
- `POST /mentor/lessons/reorder` - Reorder lessons

### Mentor Student Management
- `GET /mentor/students` - Student list
- `GET /mentor/students/{user}` - Student details
- `GET /mentor/students/{user}/progress` - Student progress
- `POST /mentor/students/{user}/message` - Send message
- `POST /mentor/students/{user}/certificate` - Issue certificate

### Mentor Live Calls
- `GET /mentor/live-calls` - Live calls
- `GET /mentor/live-calls/create` - Create live call form
- `POST /mentor/live-calls` - Create live call
- `GET /mentor/live-calls/{liveCall}` - Live call details
- `GET /mentor/live-calls/{liveCall}/edit` - Edit live call form
- `PUT /mentor/live-calls/{liveCall}` - Update live call
- `DELETE /mentor/live-calls/{liveCall}` - Delete live call
- `POST /mentor/live-calls/{liveCall}/start` - Start live call
- `POST /mentor/live-calls/{liveCall}/end` - End live call

### Mentor Analytics
- `GET /mentor/analytics` - Analytics dashboard
- `GET /mentor/analytics/courses` - Course analytics
- `GET /mentor/analytics/students` - Student analytics
- `GET /mentor/analytics/engagement` - Engagement analytics
- `GET /mentor/analytics/revenue` - Revenue analytics
- `GET /mentor/analytics/export` - Export analytics

## 🔗 AJAX Routes

### Dynamic Content Loading
- `GET /ajax/courses/{course}/progress` - Course progress
- `GET /ajax/notifications/unread-count` - Unread notification count
- `GET /ajax/search/suggestions` - Search suggestions
- `POST /ajax/lessons/{lesson}/bookmark` - Toggle lesson bookmark
- `GET /ajax/dashboard/stats` - Dashboard statistics

## 🌐 API Routes

All API routes are prefixed with `/api` and return JSON responses.

### Authentication
- `POST /api/auth/register` - Register user
- `POST /api/auth/login` - Login user
- `POST /api/auth/logout` - Logout user
- `POST /api/auth/refresh` - Refresh token
- `POST /api/auth/forgot-password` - Forgot password
- `POST /api/auth/reset-password` - Reset password
- `POST /api/auth/verify-email` - Verify email
- `POST /api/auth/resend-verification` - Resend verification

### User Management
- `GET /api/user` - Current user
- `PUT /api/user` - Update user
- `POST /api/user/avatar` - Update avatar
- `DELETE /api/user/avatar` - Delete avatar

### Courses
- `GET /api/courses` - Course list
- `GET /api/courses/{course}` - Course details
- `POST /api/courses/{course}/enroll` - Enroll in course
- `DELETE /api/courses/{course}/unenroll` - Unenroll from course
- `POST /api/courses/{course}/favorite` - Toggle favorite
- `POST /api/courses/{course}/rate` - Rate course
- `GET /api/courses/my-courses` - User's courses
- `GET /api/courses/favorites` - Favorite courses

### Lessons
- `GET /api/lessons/{lesson}` - Lesson details
- `POST /api/lessons/{lesson}/progress` - Update progress
- `POST /api/lessons/{lesson}/complete` - Complete lesson
- `POST /api/lessons/{lesson}/bookmark` - Toggle bookmark
- `POST /api/lessons/{lesson}/note` - Save note
- `GET /api/lessons/{lesson}/notes` - Get notes

### Progress
- `GET /api/progress` - User progress
- `GET /api/progress/course/{course}` - Course progress
- `GET /api/progress/stats` - Progress statistics

### Subscriptions
- `GET /api/subscription` - Current subscription
- `POST /api/subscription/subscribe` - Subscribe
- `POST /api/subscription/cancel` - Cancel subscription
- `POST /api/subscription/resume` - Resume subscription
- `GET /api/subscription/invoices` - Invoice list

### Payments
- `GET /api/payments` - Payment list
- `GET /api/payments/{payment}` - Payment details
- `POST /api/payments/crypto/verify` - Verify crypto payment

### Certificates
- `GET /api/certificates` - Certificate list
- `GET /api/certificates/{certificate}` - Certificate details
- `GET /api/certificates/{certificate}/download` - Download certificate

### Notifications
- `GET /api/notifications` - Notification list
- `POST /api/notifications/{notification}/read` - Mark as read
- `POST /api/notifications/read-all` - Mark all as read
- `DELETE /api/notifications/{notification}` - Delete notification
- `GET /api/notifications/unread-count` - Unread count

### Live Calls
- `GET /api/live-calls` - Live call list
- `GET /api/live-calls/{liveCall}` - Live call details
- `POST /api/live-calls/{liveCall}/register` - Register for call
- `DELETE /api/live-calls/{liveCall}/unregister` - Unregister from call
- `POST /api/live-calls/{liveCall}/reminder` - Set reminder

### Community
- `GET /api/community/posts` - Community posts
- `POST /api/community/posts` - Create post
- `GET /api/community/posts/{post}` - Post details
- `PUT /api/community/posts/{post}` - Update post
- `DELETE /api/community/posts/{post}` - Delete post
- `POST /api/community/posts/{post}/like` - Toggle like
- `POST /api/community/posts/{post}/comments` - Add comment
- `PUT /api/community/comments/{comment}` - Update comment
- `DELETE /api/community/comments/{comment}` - Delete comment
- `POST /api/community/comments/{comment}/like` - Toggle comment like

### Search
- `GET /api/search` - Global search
- `GET /api/search/courses` - Course search
- `GET /api/search/suggestions` - Search suggestions

### Dashboard
- `GET /api/dashboard/stats` - Dashboard statistics
- `GET /api/dashboard/recent-activity` - Recent activity
- `GET /api/dashboard/recommendations` - Course recommendations

### Public API
- `GET /api/subscription-plans` - Subscription plans
- `GET /api/courses/public` - Public courses
- `GET /api/courses/{course}/public` - Public course details
- `GET /api/categories` - Categories
- `GET /api/mentors` - Mentors
- `GET /api/mentors/{mentor}` - Mentor details
- `GET /api/live-calls/public` - Public live calls
- `GET /api/testimonials` - Testimonials

## 🔄 Webhook Routes

### Payment Webhooks
- `POST /stripe/webhook` - Stripe webhook handler
- `POST /crypto/webhook` - Cryptocurrency webhook handler

## 📡 Broadcasting Channels

### User Channels
- `user.{userId}` - User-specific notifications
- `App.Models.User.{id}` - User model updates

### Course Channels
- `course.{courseId}` - Course updates
- `lesson.{lessonId}` - Lesson progress updates

### Live Call Channels
- `live-call.{liveCallId}` - Live call updates
- `live-call-chat.{liveCallId}` - Live call chat
- `live-call-presence.{liveCallId}` - Live call attendees

### Community Channels
- `community` - Community updates
- `community.post.{postId}` - Post updates

### System Channels
- `announcements` - Global announcements
- `system.status` - System status updates
- `emergency` - Emergency broadcasts

## 🎯 Route Middleware

### Authentication Middleware
- `auth` - Requires authentication
- `guest` - Requires guest (not authenticated)
- `verified` - Requires email verification

### Role-based Middleware
- `role:admin` - Requires admin role
- `role:mentor` - Requires mentor role
- `role:user` - Requires user role

### Subscription Middleware
- `subscription` - Requires active subscription
- `subscription:feature` - Requires specific feature access

### API Middleware
- `auth:sanctum` - API authentication
- `throttle:api` - API rate limiting

### Custom Middleware
- `impersonate` - Handle user impersonation
- `maintenance` - Maintenance mode check
- `subscription.check` - Subscription validation

---

## 📝 Route Naming Conventions

- **Resource routes**: `{resource}.{action}` (e.g., `courses.show`)
- **Admin routes**: `admin.{resource}.{action}` (e.g., `admin.users.index`)
- **Mentor routes**: `mentor.{resource}.{action}` (e.g., `mentor.courses.create`)
- **API routes**: `api.{resource}.{action}` (e.g., `api.courses.index`)
- **AJAX routes**: `ajax.{resource}.{action}` (e.g., `ajax.notifications.unread-count`)

## 🔒 Security Considerations

- All authenticated routes use CSRF protection
- API routes use Sanctum token authentication
- Admin routes require admin role verification
- Mentor routes require mentor role verification
- Subscription routes check active subscription status
- File upload routes validate file types and sizes
- Rate limiting applied to API and sensitive routes

---

*This documentation covers all routes in The Real World LMS application. For implementation details, refer to the respective controller files.*
