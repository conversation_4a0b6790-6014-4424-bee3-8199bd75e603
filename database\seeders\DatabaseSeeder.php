<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([
            RoleSeeder::class,
            UserSeeder::class,
            SubscriptionPlanSeeder::class,
            CategorySeeder::class,
            CourseSeeder::class,
            LessonSeeder::class,
            LiveCallSeeder::class,
            CommunitySeeder::class,
            SubscriptionSeeder::class,
            UserProgressSeeder::class,
            CourseRatingSeeder::class,
            CertificateSeeder::class,
            PaymentSeeder::class,
            NotificationSeeder::class,
        ]);
    }
}
