@extends('layouts.app')

@section('title', 'Subscription Plans')

@section('content')
<div class="pricing-hero">
    <div class="container">
        <div class="row justify-content-center text-center">
            <div class="col-lg-8">
                <h1 class="hero-title">Choose Your Path to Success</h1>
                <p class="hero-subtitle">Join thousands of students who are already transforming their lives with The Real World</p>
                
                <!-- Billing Toggle -->
                <div class="billing-toggle">
                    <span class="toggle-label">Monthly</span>
                    <label class="switch">
                        <input type="checkbox" id="billingToggle">
                        <span class="slider"></span>
                    </label>
                    <span class="toggle-label">
                        Annual 
                        <span class="discount-badge">Save 20%</span>
                    </span>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="pricing-section">
    <div class="container">
        <div class="row justify-content-center">
            @foreach($plans as $plan)
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="pricing-card {{ $plan->is_popular ? 'popular' : '' }}">
                    @if($plan->is_popular)
                    <div class="popular-badge">
                        <i class="fas fa-crown"></i> Most Popular
                    </div>
                    @endif
                    
                    <div class="plan-header">
                        <div class="plan-icon">
                            <i class="fas fa-{{ $plan->icon }}"></i>
                        </div>
                        <h3 class="plan-name">{{ $plan->name }}</h3>
                        <p class="plan-description">{{ $plan->description }}</p>
                    </div>
                    
                    <div class="plan-pricing">
                        <div class="price-display">
                            <span class="currency">$</span>
                            <span class="amount monthly-price" data-monthly="{{ $plan->monthly_price }}">{{ number_format($plan->monthly_price, 0) }}</span>
                            <span class="amount annual-price" data-annual="{{ $plan->annual_price }}" style="display: none;">{{ number_format($plan->annual_price / 12, 0) }}</span>
                            <span class="period">
                                <span class="monthly-period">/month</span>
                                <span class="annual-period" style="display: none;">/month</span>
                            </span>
                        </div>
                        <div class="annual-savings" style="display: none;">
                            <small class="text-success">
                                <i class="fas fa-tag"></i>
                                Save ${{ number_format(($plan->monthly_price * 12) - $plan->annual_price, 0) }} annually
                            </small>
                        </div>
                    </div>
                    
                    <div class="plan-features">
                        <ul class="features-list">
                            @foreach($plan->features as $feature)
                            <li class="feature-item">
                                <i class="fas fa-check text-success"></i>
                                <span>{{ $feature }}</span>
                            </li>
                            @endforeach
                        </ul>
                    </div>
                    
                    <div class="plan-action">
                        @auth
                            @if(auth()->user()->hasActiveSubscription() && auth()->user()->activeSubscription->subscription_plan_id === $plan->id)
                                <button class="btn btn-success btn-block" disabled>
                                    <i class="fas fa-check"></i> Current Plan
                                </button>
                            @elseif(auth()->user()->hasActiveSubscription())
                                @if($plan->price > auth()->user()->activeSubscription->subscriptionPlan->price)
                                    <a href="{{ route('subscriptions.checkout', $plan) }}" class="btn btn-primary btn-block">
                                        <i class="fas fa-arrow-up"></i> Upgrade to {{ $plan->name }}
                                    </a>
                                @else
                                    <a href="{{ route('subscriptions.checkout', $plan) }}" class="btn btn-outline-primary btn-block">
                                        <i class="fas fa-arrow-down"></i> Downgrade to {{ $plan->name }}
                                    </a>
                                @endif
                            @else
                                <a href="{{ route('subscriptions.checkout', $plan) }}" class="btn btn-primary btn-block">
                                    <i class="fas fa-rocket"></i> Get Started
                                </a>
                            @endif
                        @else
                            <a href="{{ route('register') }}" class="btn btn-primary btn-block">
                                <i class="fas fa-user-plus"></i> Sign Up Now
                            </a>
                        @endauth
                    </div>
                </div>
            </div>
            @endforeach
        </div>
        
        <!-- Money Back Guarantee -->
        <div class="guarantee-section text-center mt-5">
            <div class="guarantee-badge">
                <i class="fas fa-shield-alt"></i>
                <div class="guarantee-content">
                    <h4>30-Day Money Back Guarantee</h4>
                    <p>Not satisfied? Get a full refund within 30 days, no questions asked.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Features Comparison -->
<div class="comparison-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="section-header text-center mb-5">
                    <h2>Compare Plans</h2>
                    <p>Choose the plan that best fits your learning goals</p>
                </div>
                
                <div class="comparison-table-wrapper">
                    <table class="comparison-table">
                        <thead>
                            <tr>
                                <th class="feature-column">Features</th>
                                @foreach($plans as $plan)
                                <th class="plan-column {{ $plan->is_popular ? 'popular' : '' }}">
                                    <div class="plan-header-small">
                                        <h5>{{ $plan->name }}</h5>
                                        <div class="price-small">
                                            $<span class="monthly-price">{{ number_format($plan->monthly_price, 0) }}</span>
                                            <span class="annual-price" style="display: none;">{{ number_format($plan->annual_price / 12, 0) }}</span>
                                            /mo
                                        </div>
                                    </div>
                                </th>
                                @endforeach
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($comparisonFeatures as $feature)
                            <tr>
                                <td class="feature-name">
                                    <strong>{{ $feature['name'] }}</strong>
                                    @if($feature['description'])
                                    <small class="text-muted d-block">{{ $feature['description'] }}</small>
                                    @endif
                                </td>
                                @foreach($plans as $plan)
                                <td class="feature-value">
                                    @if(isset($feature['plans'][$plan->id]))
                                        @if($feature['plans'][$plan->id] === true)
                                            <i class="fas fa-check text-success"></i>
                                        @elseif($feature['plans'][$plan->id] === false)
                                            <i class="fas fa-times text-muted"></i>
                                        @else
                                            {{ $feature['plans'][$plan->id] }}
                                        @endif
                                    @else
                                        <i class="fas fa-times text-muted"></i>
                                    @endif
                                </td>
                                @endforeach
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- FAQ Section -->
<div class="faq-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="section-header text-center mb-5">
                    <h2>Frequently Asked Questions</h2>
                    <p>Got questions? We've got answers.</p>
                </div>
                
                <div class="accordion" id="pricingFAQ">
                    @foreach($faqs as $index => $faq)
                    <div class="card">
                        <div class="card-header" id="faq{{ $index }}">
                            <h5 class="mb-0">
                                <button class="btn btn-link" type="button" data-toggle="collapse" 
                                        data-target="#collapse{{ $index }}" aria-expanded="{{ $index === 0 ? 'true' : 'false' }}">
                                    {{ $faq['question'] }}
                                    <i class="fas fa-chevron-down"></i>
                                </button>
                            </h5>
                        </div>
                        <div id="collapse{{ $index }}" class="collapse {{ $index === 0 ? 'show' : '' }}" 
                             data-parent="#pricingFAQ">
                            <div class="card-body">
                                {{ $faq['answer'] }}
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</div>

<!-- CTA Section -->
<div class="cta-section">
    <div class="container">
        <div class="row justify-content-center text-center">
            <div class="col-lg-8">
                <h2>Ready to Transform Your Life?</h2>
                <p>Join thousands of students who are already building their path to financial freedom</p>
                <div class="cta-buttons">
                    @guest
                    <a href="{{ route('register') }}" class="btn btn-primary btn-lg">
                        <i class="fas fa-rocket"></i> Start Your Journey
                    </a>
                    <a href="{{ route('courses.index') }}" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-eye"></i> Preview Courses
                    </a>
                    @else
                    <a href="{{ route('courses.index') }}" class="btn btn-primary btn-lg">
                        <i class="fas fa-book"></i> Browse Courses
                    </a>
                    @endguest
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.pricing-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 5rem 0 3rem;
    text-align: center;
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.hero-subtitle {
    font-size: 1.25rem;
    opacity: 0.9;
    margin-bottom: 2rem;
}

.billing-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin-top: 2rem;
}

.toggle-label {
    font-weight: 600;
    font-size: 1.1rem;
}

.switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.3);
    transition: .4s;
    border-radius: 34px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: rgba(255, 255, 255, 0.5);
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.discount-badge {
    background: #ffd700;
    color: #333;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    margin-left: 0.5rem;
}

.pricing-section {
    padding: 4rem 0;
    background: #f8f9fc;
}

.pricing-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.pricing-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.pricing-card.popular {
    border: 3px solid #ffd700;
    transform: scale(1.05);
}

.popular-badge {
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    color: #333;
    padding: 0.5rem 1.5rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.875rem;
    box-shadow: 0 5px 15px rgba(255, 215, 0, 0.3);
}

.plan-header {
    text-align: center;
    margin-bottom: 2rem;
}

.plan-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
}

.plan-icon i {
    font-size: 2rem;
    color: white;
}

.plan-name {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.plan-description {
    color: #718096;
    font-size: 1rem;
}

.plan-pricing {
    text-align: center;
    margin-bottom: 2rem;
}

.price-display {
    display: flex;
    align-items: baseline;
    justify-content: center;
    margin-bottom: 0.5rem;
}

.currency {
    font-size: 1.5rem;
    font-weight: 600;
    color: #4a5568;
}

.amount {
    font-size: 3rem;
    font-weight: 700;
    color: #2d3748;
    margin: 0 0.25rem;
}

.period {
    font-size: 1rem;
    color: #718096;
    font-weight: 500;
}

.plan-features {
    flex: 1;
    margin-bottom: 2rem;
}

.features-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.feature-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #e2e8f0;
}

.feature-item:last-child {
    border-bottom: none;
}

.feature-item i {
    margin-right: 0.75rem;
    font-size: 1rem;
}

.guarantee-section {
    padding: 3rem 0;
}

.guarantee-badge {
    display: inline-flex;
    align-items: center;
    background: white;
    padding: 1.5rem 2rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 2px solid #1cc88a;
}

.guarantee-badge i {
    font-size: 2rem;
    color: #1cc88a;
    margin-right: 1rem;
}

.guarantee-content h4 {
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.guarantee-content p {
    color: #718096;
    margin: 0;
}

.comparison-section {
    padding: 4rem 0;
    background: white;
}

.comparison-table-wrapper {
    overflow-x: auto;
}

.comparison-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.comparison-table th,
.comparison-table td {
    padding: 1.5rem 1rem;
    text-align: center;
    border-bottom: 1px solid #e2e8f0;
}

.comparison-table th {
    background: #f8f9fc;
    font-weight: 600;
    color: #2d3748;
}

.comparison-table th.popular {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.feature-column {
    text-align: left !important;
    min-width: 200px;
}

.plan-column {
    min-width: 150px;
}

.feature-name {
    text-align: left !important;
    font-weight: 600;
    color: #2d3748;
}

.feature-value {
    font-size: 1.25rem;
}

.faq-section {
    padding: 4rem 0;
    background: #f8f9fc;
}

.accordion .card {
    border: none;
    border-radius: 10px;
    margin-bottom: 1rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.accordion .card-header {
    background: white;
    border: none;
    padding: 0;
}

.accordion .btn-link {
    width: 100%;
    text-align: left;
    padding: 1.5rem;
    color: #2d3748;
    text-decoration: none;
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.accordion .btn-link:hover {
    text-decoration: none;
    color: #667eea;
}

.accordion .btn-link i {
    transition: transform 0.3s ease;
}

.accordion .btn-link[aria-expanded="true"] i {
    transform: rotate(180deg);
}

.cta-section {
    background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
    color: white;
    padding: 4rem 0;
    text-align: center;
}

.cta-section h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.cta-section p {
    font-size: 1.25rem;
    opacity: 0.9;
    margin-bottom: 2rem;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

@media (max-width: 768px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .billing-toggle {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .pricing-card.popular {
        transform: none;
    }
    
    .amount {
        font-size: 2rem;
    }
    
    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .cta-buttons .btn {
        width: 100%;
        max-width: 300px;
    }
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const billingToggle = document.getElementById('billingToggle');
    const monthlyPrices = document.querySelectorAll('.monthly-price');
    const annualPrices = document.querySelectorAll('.annual-price');
    const monthlyPeriods = document.querySelectorAll('.monthly-period');
    const annualPeriods = document.querySelectorAll('.annual-period');
    const annualSavings = document.querySelectorAll('.annual-savings');
    
    billingToggle.addEventListener('change', function() {
        const isAnnual = this.checked;
        
        monthlyPrices.forEach(price => {
            price.style.display = isAnnual ? 'none' : 'inline';
        });
        
        annualPrices.forEach(price => {
            price.style.display = isAnnual ? 'inline' : 'none';
        });
        
        monthlyPeriods.forEach(period => {
            period.style.display = isAnnual ? 'none' : 'inline';
        });
        
        annualPeriods.forEach(period => {
            period.style.display = isAnnual ? 'inline' : 'none';
        });
        
        annualSavings.forEach(saving => {
            saving.style.display = isAnnual ? 'block' : 'none';
        });
    });
});
</script>
@endpush
