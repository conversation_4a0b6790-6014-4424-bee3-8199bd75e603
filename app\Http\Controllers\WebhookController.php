<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\UserSubscription;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Cashier\Http\Controllers\WebhookController as CashierWebhookController;

class WebhookController extends CashierWebhookController
{
    /**
     * Handle invoice payment succeeded.
     */
    public function handleInvoicePaymentSucceeded($payload)
    {
        $invoice = $payload['data']['object'];
        $subscriptionId = $invoice['subscription'];

        if ($subscriptionId) {
            $userSubscription = UserSubscription::where('stripe_subscription_id', $subscriptionId)->first();

            if ($userSubscription) {
                // Update subscription period
                $periodStart = \Carbon\Carbon::createFromTimestamp($invoice['period_start']);
                $periodEnd = \Carbon\Carbon::createFromTimestamp($invoice['period_end']);

                $userSubscription->update([
                    'current_period_start' => $periodStart,
                    'current_period_end' => $periodEnd,
                    'status' => 'active',
                ]);
            }
        }

        return $this->successMethod();
    }

    /**
     * Handle customer subscription updated.
     */
    public function handleCustomerSubscriptionUpdated($payload)
    {
        $subscription = $payload['data']['object'];
        $subscriptionId = $subscription['id'];

        $userSubscription = UserSubscription::where('stripe_subscription_id', $subscriptionId)->first();

        if ($userSubscription) {
            $status = $subscription['status'];

            // Map Stripe status to our status
            $ourStatus = match($status) {
                'active' => 'active',
                'canceled' => 'cancelled',
                'past_due' => 'past_due',
                'unpaid' => 'past_due',
                default => $status,
            };

            $userSubscription->update([
                'status' => $ourStatus,
                'current_period_start' => \Carbon\Carbon::createFromTimestamp($subscription['current_period_start']),
                'current_period_end' => \Carbon\Carbon::createFromTimestamp($subscription['current_period_end']),
            ]);
        }

        return $this->successMethod();
    }

    /**
     * Handle customer subscription deleted.
     */
    public function handleCustomerSubscriptionDeleted($payload)
    {
        $subscription = $payload['data']['object'];
        $subscriptionId = $subscription['id'];

        $userSubscription = UserSubscription::where('stripe_subscription_id', $subscriptionId)->first();

        if ($userSubscription) {
            $userSubscription->update([
                'status' => 'cancelled',
                'cancelled_at' => now(),
                'ends_at' => \Carbon\Carbon::createFromTimestamp($subscription['current_period_end']),
            ]);
        }

        return $this->successMethod();
    }

    /**
     * Handle invoice payment failed.
     */
    public function handleInvoicePaymentFailed($payload)
    {
        $invoice = $payload['data']['object'];
        $subscriptionId = $invoice['subscription'];

        if ($subscriptionId) {
            $userSubscription = UserSubscription::where('stripe_subscription_id', $subscriptionId)->first();

            if ($userSubscription) {
                $userSubscription->update([
                    'status' => 'past_due',
                ]);

                // You could send notification email here
                // Mail::to($userSubscription->user)->send(new PaymentFailedMail());
            }
        }

        return $this->successMethod();
    }
}
