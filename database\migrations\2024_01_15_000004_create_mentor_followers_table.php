<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('mentor_followers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade'); // The follower
            $table->foreignId('mentor_id')->constrained('users')->onDelete('cascade'); // The mentor being followed
            $table->timestamps();

            // Indexes
            $table->index('user_id');
            $table->index('mentor_id');
            $table->index('created_at');

            // Unique constraint to prevent duplicate follows
            $table->unique(['user_id', 'mentor_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('mentor_followers');
    }
};
