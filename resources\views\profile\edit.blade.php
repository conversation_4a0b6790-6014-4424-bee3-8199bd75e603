@extends('layouts.dashboard')

@section('title', 'Edit Profile')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('profile.index') }}">Profile</a></li>
                    <li class="breadcrumb-item active">Edit</li>
                </ol>
            </nav>
            <h1 class="h3 mb-0 text-gray-800">Edit Profile</h1>
        </div>
        <div>
            <a href="{{ route('profile.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Profile
            </a>
        </div>
    </div>

    <form action="{{ route('profile.update') }}" method="POST" enctype="multipart/form-data">
        @csrf
        @method('PUT')
        
        <div class="row">
            <!-- Basic Information -->
            <div class="col-xl-8">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Personal Information</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="first_name">First Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('first_name') is-invalid @enderror" 
                                           id="first_name" name="first_name" value="{{ old('first_name', auth()->user()->first_name) }}" required>
                                    @error('first_name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="last_name">Last Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('last_name') is-invalid @enderror" 
                                           id="last_name" name="last_name" value="{{ old('last_name', auth()->user()->last_name) }}" required>
                                    @error('last_name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="email">Email Address <span class="text-danger">*</span></label>
                            <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                   id="email" name="email" value="{{ old('email', auth()->user()->email) }}" required>
                            @error('email')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            @if(!auth()->user()->email_verified_at)
                                <small class="form-text text-warning">
                                    <i class="fas fa-exclamation-triangle"></i> Your email is not verified. 
                                    <a href="{{ route('verification.send') }}">Resend verification email</a>
                                </small>
                            @endif
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="phone">Phone Number</label>
                                    <input type="text" class="form-control @error('phone') is-invalid @enderror" 
                                           id="phone" name="phone" value="{{ old('phone', auth()->user()->phone) }}">
                                    @error('phone')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="country">Country</label>
                                    <select class="form-control @error('country') is-invalid @enderror" id="country" name="country">
                                        <option value="">Select Country</option>
                                        <option value="United States" {{ old('country', auth()->user()->country) == 'United States' ? 'selected' : '' }}>United States</option>
                                        <option value="Canada" {{ old('country', auth()->user()->country) == 'Canada' ? 'selected' : '' }}>Canada</option>
                                        <option value="United Kingdom" {{ old('country', auth()->user()->country) == 'United Kingdom' ? 'selected' : '' }}>United Kingdom</option>
                                        <option value="Australia" {{ old('country', auth()->user()->country) == 'Australia' ? 'selected' : '' }}>Australia</option>
                                        <option value="Germany" {{ old('country', auth()->user()->country) == 'Germany' ? 'selected' : '' }}>Germany</option>
                                        <option value="France" {{ old('country', auth()->user()->country) == 'France' ? 'selected' : '' }}>France</option>
                                        <option value="Italy" {{ old('country', auth()->user()->country) == 'Italy' ? 'selected' : '' }}>Italy</option>
                                        <option value="Spain" {{ old('country', auth()->user()->country) == 'Spain' ? 'selected' : '' }}>Spain</option>
                                        <option value="Romania" {{ old('country', auth()->user()->country) == 'Romania' ? 'selected' : '' }}>Romania</option>
                                    </select>
                                    @error('country')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="bio">Bio</label>
                            <textarea class="form-control @error('bio') is-invalid @enderror" 
                                      id="bio" name="bio" rows="4" placeholder="Tell us about yourself...">{{ old('bio', auth()->user()->bio) }}</textarea>
                            @error('bio')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="form-text text-muted">Maximum 500 characters</small>
                        </div>
                    </div>
                </div>

                <!-- Password Change -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Change Password</h6>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label for="current_password">Current Password</label>
                            <input type="password" class="form-control @error('current_password') is-invalid @enderror" 
                                   id="current_password" name="current_password">
                            @error('current_password')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="form-text text-muted">Required only if changing password</small>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="password">New Password</label>
                                    <input type="password" class="form-control @error('password') is-invalid @enderror" 
                                           id="password" name="password">
                                    @error('password')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">Minimum 8 characters</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="password_confirmation">Confirm New Password</label>
                                    <input type="password" class="form-control" 
                                           id="password_confirmation" name="password_confirmation">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Notification Preferences -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Notification Preferences</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" 
                                               id="email_notifications" name="email_notifications" value="1" 
                                               {{ old('email_notifications', auth()->user()->email_notifications ?? true) ? 'checked' : '' }}>
                                        <label class="custom-control-label" for="email_notifications">Email Notifications</label>
                                    </div>
                                    <small class="form-text text-muted">Receive course updates and announcements</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" 
                                               id="marketing_emails" name="marketing_emails" value="1" 
                                               {{ old('marketing_emails', auth()->user()->marketing_emails ?? false) ? 'checked' : '' }}>
                                        <label class="custom-control-label" for="marketing_emails">Marketing Emails</label>
                                    </div>
                                    <small class="form-text text-muted">Receive promotional offers and news</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Profile Picture & Settings -->
            <div class="col-xl-4">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Profile Picture</h6>
                    </div>
                    <div class="card-body text-center">
                        <img class="rounded-circle mb-3" id="avatar-preview"
                             src="{{ auth()->user()->avatar ? asset('storage/' . auth()->user()->avatar) : asset('images/default-avatar.png') }}" 
                             alt="{{ auth()->user()->name }}" width="120" height="120">
                        
                        <div class="form-group">
                            <input type="file" class="form-control-file @error('avatar') is-invalid @enderror" 
                                   id="avatar" name="avatar" accept="image/*" onchange="previewAvatar(this)">
                            @error('avatar')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="form-text text-muted">Max size: 2MB. Formats: JPG, PNG, GIF</small>
                        </div>

                        @if(auth()->user()->avatar)
                        <div class="form-group">
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" 
                                       id="remove_avatar" name="remove_avatar" value="1">
                                <label class="custom-control-label" for="remove_avatar">Remove current picture</label>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>

                <!-- Account Statistics -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Account Statistics</h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-12 mb-3">
                                <div class="font-weight-bold text-primary">{{ auth()->user()->userProgress->where('is_completed', true)->count() }}</div>
                                <div class="text-muted small">Lessons Completed</div>
                            </div>
                            <div class="col-12 mb-3">
                                <div class="font-weight-bold text-success">{{ auth()->user()->certificates->count() }}</div>
                                <div class="text-muted small">Certificates Earned</div>
                            </div>
                            <div class="col-12 mb-3">
                                <div class="font-weight-bold text-info">{{ auth()->user()->courseRatings->count() }}</div>
                                <div class="text-muted small">Reviews Given</div>
                            </div>
                            <div class="col-12">
                                <div class="font-weight-bold text-warning">{{ auth()->user()->created_at->diffInDays() }}</div>
                                <div class="text-muted small">Days as Member</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Save Changes -->
                <div class="card shadow mb-4">
                    <div class="card-body">
                        <button type="submit" class="btn btn-primary btn-block">
                            <i class="fas fa-save"></i> Save Changes
                        </button>
                        <a href="{{ route('profile.index') }}" class="btn btn-secondary btn-block">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                    </div>
                </div>

                <!-- Danger Zone -->
                <div class="card shadow border-left-danger mb-4">
                    <div class="card-header py-3 bg-danger text-white">
                        <h6 class="m-0 font-weight-bold">Danger Zone</h6>
                    </div>
                    <div class="card-body">
                        <p class="text-muted small mb-3">Once you delete your account, there is no going back. Please be certain.</p>
                        <button type="button" class="btn btn-outline-danger btn-sm" data-toggle="modal" data-target="#deleteAccountModal">
                            <i class="fas fa-trash"></i> Delete Account
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<!-- Delete Account Modal -->
<div class="modal fade" id="deleteAccountModal" tabindex="-1" role="dialog" aria-labelledby="deleteAccountModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteAccountModalLabel">Delete Account</h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p><strong>Are you sure you want to delete your account?</strong></p>
                <p>This action cannot be undone. All your progress, certificates, and data will be permanently deleted.</p>
                
                <form id="deleteAccountForm" action="{{ route('profile.destroy') }}" method="POST">
                    @csrf
                    @method('DELETE')
                    
                    <div class="form-group">
                        <label for="delete_password">Enter your password to confirm:</label>
                        <input type="password" class="form-control" id="delete_password" name="password" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="submit" form="deleteAccountForm" class="btn btn-danger">
                    <i class="fas fa-trash"></i> Delete My Account
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function previewAvatar(input) {
    if (input.files && input.files[0]) {
        var reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('avatar-preview').src = e.target.result;
        }
        reader.readAsDataURL(input.files[0]);
    }
}

// Character counter for bio
document.getElementById('bio').addEventListener('input', function() {
    const maxLength = 500;
    const currentLength = this.value.length;
    const remaining = maxLength - currentLength;
    
    // You could add a character counter here if needed
});
</script>
@endpush
