@extends('layouts.dashboard')

@section('title', 'Learning Progress')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Learning Progress</h1>
            <p class="mb-0 text-muted">Track your learning journey and achievements</p>
        </div>
        <div>
            <button class="btn btn-outline-primary" onclick="exportProgress()">
                <i class="fas fa-download"></i> Export Progress
            </button>
        </div>
    </div>

    <!-- Progress Overview -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Courses Enrolled</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['enrolled_courses'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-book fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Courses Completed</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['completed_courses'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Lessons Completed</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['completed_lessons'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-play-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Certificates Earned</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['certificates'] }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-certificate fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Overall Progress Chart -->
    <div class="row mb-4">
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Learning Progress Over Time</h6>
                    <div class="dropdown no-arrow">
                        <a class="dropdown-toggle" href="#" role="button" data-toggle="dropdown">
                            <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right shadow">
                            <a class="dropdown-item" href="#" onclick="changeChartPeriod('week')">Last Week</a>
                            <a class="dropdown-item" href="#" onclick="changeChartPeriod('month')">Last Month</a>
                            <a class="dropdown-item" href="#" onclick="changeChartPeriod('year')">Last Year</a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-area">
                        <canvas id="progressChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Learning Streak</h6>
                </div>
                <div class="card-body text-center">
                    <div class="streak-counter">
                        <div class="streak-number">{{ $stats['current_streak'] }}</div>
                        <div class="streak-label">Days</div>
                    </div>
                    <p class="text-muted mb-3">Current learning streak</p>
                    
                    <div class="streak-stats">
                        <div class="row">
                            <div class="col-6">
                                <div class="stat-item">
                                    <div class="stat-number">{{ $stats['longest_streak'] }}</div>
                                    <div class="stat-label">Longest Streak</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="stat-item">
                                    <div class="stat-number">{{ $stats['total_study_hours'] }}h</div>
                                    <div class="stat-label">Total Study Time</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="streak-calendar mt-3">
                        <h6 class="font-weight-bold mb-2">This Week</h6>
                        <div class="calendar-week">
                            @foreach($weeklyActivity as $day)
                            <div class="calendar-day {{ $day['active'] ? 'active' : '' }}" title="{{ $day['date'] }}">
                                <div class="day-label">{{ $day['label'] }}</div>
                                <div class="day-indicator"></div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Course Progress -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Course Progress</h6>
        </div>
        <div class="card-body">
            @forelse($courseProgress as $progress)
            <div class="course-progress-item mb-4">
                <div class="row align-items-center">
                    <div class="col-md-3">
                        <div class="course-info">
                            <img src="{{ $progress['course']->thumbnail ? asset('storage/' . $progress['course']->thumbnail) : asset('images/default-course-thumbnail.jpg') }}" 
                                 alt="{{ $progress['course']->title }}" class="course-thumbnail">
                            <div class="course-details">
                                <h6 class="course-title">{{ $progress['course']->title }}</h6>
                                <p class="course-mentor">{{ $progress['course']->mentor->name }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="progress-info">
                            <div class="d-flex justify-content-between mb-1">
                                <span class="progress-label">Progress</span>
                                <span class="progress-percentage">{{ number_format($progress['progress_percentage'], 1) }}%</span>
                            </div>
                            <div class="progress progress-custom">
                                <div class="progress-bar" role="progressbar" 
                                     style="width: {{ $progress['progress_percentage'] }}%"
                                     aria-valuenow="{{ $progress['progress_percentage'] }}" 
                                     aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                            <div class="progress-stats mt-2">
                                <small class="text-muted">
                                    {{ $progress['completed_lessons'] }} of {{ $progress['total_lessons'] }} lessons completed
                                </small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 text-right">
                        <div class="course-actions">
                            @if($progress['progress_percentage'] >= 100)
                                <span class="badge badge-success mb-2">
                                    <i class="fas fa-check"></i> Completed
                                </span>
                                @if($progress['certificate'])
                                <a href="{{ route('certificates.download', $progress['certificate']) }}" 
                                   class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-certificate"></i> Certificate
                                </a>
                                @endif
                            @else
                                <a href="{{ route('courses.show', $progress['course']) }}" 
                                   class="btn btn-sm btn-primary">
                                    <i class="fas fa-play"></i> Continue
                                </a>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
            @empty
            <div class="text-center py-4">
                <i class="fas fa-book fa-3x text-gray-300 mb-3"></i>
                <h5 class="text-gray-600">No courses enrolled yet</h5>
                <p class="text-muted">Start your learning journey by enrolling in a course</p>
                <a href="{{ route('courses.index') }}" class="btn btn-primary">
                    <i class="fas fa-search"></i> Browse Courses
                </a>
            </div>
            @endforelse
        </div>
    </div>

    <!-- Recent Achievements -->
    @if($recentAchievements->count() > 0)
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Recent Achievements</h6>
        </div>
        <div class="card-body">
            <div class="row">
                @foreach($recentAchievements as $achievement)
                <div class="col-md-6 col-lg-4 mb-3">
                    <div class="achievement-card">
                        <div class="achievement-icon">
                            <i class="fas fa-{{ $achievement['icon'] }}"></i>
                        </div>
                        <div class="achievement-content">
                            <h6 class="achievement-title">{{ $achievement['title'] }}</h6>
                            <p class="achievement-description">{{ $achievement['description'] }}</p>
                            <small class="achievement-date">{{ $achievement['date'] }}</small>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </div>
    @endif
</div>
@endsection

@push('styles')
<style>
.streak-counter {
    margin-bottom: 1rem;
}

.streak-number {
    font-size: 3rem;
    font-weight: bold;
    color: #f6c23e;
    line-height: 1;
}

.streak-label {
    font-size: 1rem;
    color: #858796;
    font-weight: 600;
}

.streak-stats .stat-item {
    text-align: center;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: bold;
    color: #5a5c69;
}

.stat-label {
    font-size: 0.8rem;
    color: #858796;
    text-transform: uppercase;
}

.calendar-week {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
}

.calendar-day {
    text-align: center;
    padding: 0.5rem;
    border-radius: 8px;
    background: #f8f9fc;
    min-width: 40px;
}

.calendar-day.active {
    background: #1cc88a;
    color: white;
}

.day-label {
    font-size: 0.7rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.day-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #d1d3e2;
    margin: 0 auto;
}

.calendar-day.active .day-indicator {
    background: white;
}

.course-progress-item {
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    padding: 1.5rem;
    background: #f8f9fc;
}

.course-info {
    display: flex;
    align-items: center;
}

.course-thumbnail {
    width: 60px;
    height: 40px;
    object-fit: cover;
    border-radius: 0.25rem;
    margin-right: 1rem;
}

.course-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
    color: #5a5c69;
}

.course-mentor {
    font-size: 0.875rem;
    color: #858796;
    margin: 0;
}

.progress-custom {
    height: 8px;
    border-radius: 4px;
    background-color: #e3e6f0;
}

.progress-custom .progress-bar {
    background: linear-gradient(90deg, #4e73df 0%, #224abe 100%);
    border-radius: 4px;
}

.progress-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: #5a5c69;
}

.progress-percentage {
    font-size: 0.875rem;
    font-weight: 600;
    color: #4e73df;
}

.achievement-card {
    display: flex;
    align-items: center;
    padding: 1rem;
    background: white;
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    transition: all 0.3s ease;
}

.achievement-card:hover {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    transform: translateY(-2px);
}

.achievement-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #f6c23e 0%, #f4b942 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    flex-shrink: 0;
}

.achievement-icon i {
    font-size: 1.5rem;
    color: white;
}

.achievement-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
    color: #5a5c69;
}

.achievement-description {
    font-size: 0.875rem;
    color: #858796;
    margin-bottom: 0.25rem;
}

.achievement-date {
    font-size: 0.75rem;
    color: #858796;
}

@media (max-width: 768px) {
    .course-info {
        flex-direction: column;
        text-align: center;
    }
    
    .course-thumbnail {
        margin-right: 0;
        margin-bottom: 0.5rem;
    }
    
    .course-actions {
        text-align: center !important;
        margin-top: 1rem;
    }
}
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Progress Chart
const ctx = document.getElementById('progressChart').getContext('2d');
const progressChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: {!! json_encode($chartData['labels']) !!},
        datasets: [{
            label: 'Lessons Completed',
            data: {!! json_encode($chartData['data']) !!},
            borderColor: '#4e73df',
            backgroundColor: 'rgba(78, 115, 223, 0.1)',
            borderWidth: 2,
            fill: true,
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                grid: {
                    color: '#e3e6f0'
                }
            },
            x: {
                grid: {
                    color: '#e3e6f0'
                }
            }
        }
    }
});

function changeChartPeriod(period) {
    // AJAX call to update chart data
    fetch(`/ajax/dashboard/progress-chart?period=${period}`)
        .then(response => response.json())
        .then(data => {
            progressChart.data.labels = data.labels;
            progressChart.data.datasets[0].data = data.data;
            progressChart.update();
        });
}

function exportProgress() {
    window.location.href = '/dashboard/progress/export';
}
</script>
@endpush
