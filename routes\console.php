<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;

/*
|--------------------------------------------------------------------------
| Console Routes
|--------------------------------------------------------------------------
|
| This file is where you may define all of your Closure based console
| commands. Each Closure is bound to a command instance allowing a
| simple approach to interacting with each command's IO methods.
|
*/

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

// LMS-specific console commands
Artisan::command('lms:install', function () {
    $this->info('Installing The Real World LMS...');

    // Run migrations
    $this->call('migrate');

    // Seed the database
    $this->call('db:seed');

    // Create storage link
    $this->call('storage:link');

    // Clear and cache config
    $this->call('config:cache');
    $this->call('route:cache');
    $this->call('view:cache');

    $this->info('The Real World LMS installed successfully!');
    $this->info('Default admin credentials:');
    $this->info('Email: <EMAIL>');
    $this->info('Password: password123');

})->purpose('Install The Real World LMS');

Artisan::command('lms:stats', function () {
    $this->info('The Real World LMS Statistics:');
    $this->info('================================');

    $userCount = \App\Models\User::count();
    $courseCount = \App\Models\Course::count();
    $lessonCount = \App\Models\Lesson::count();
    $subscriptionCount = \App\Models\UserSubscription::where('status', 'active')->count();
    $certificateCount = \App\Models\Certificate::count();

    $this->table(
        ['Metric', 'Count'],
        [
            ['Total Users', $userCount],
            ['Total Courses', $courseCount],
            ['Total Lessons', $lessonCount],
            ['Active Subscriptions', $subscriptionCount],
            ['Certificates Issued', $certificateCount],
        ]
    );

})->purpose('Display The Real World LMS statistics');

Artisan::command('lms:create-admin {email} {password}', function ($email, $password) {
    $this->info('Creating admin user...');

    $user = \App\Models\User::create([
        'first_name' => 'Admin',
        'last_name' => 'User',
        'name' => 'Admin User',
        'email' => $email,
        'password' => \Hash::make($password),
        'email_verified_at' => now(),
        'is_active' => true,
    ]);

    // Assign admin role
    $adminRole = \Spatie\Permission\Models\Role::firstOrCreate(['name' => 'admin']);
    $user->assignRole($adminRole);

    $this->info("Admin user created successfully!");
    $this->info("Email: {$email}");
    $this->info("Password: {$password}");

})->purpose('Create a new admin user');

Artisan::command('lms:optimize', function () {
    $this->info('Optimizing The Real World LMS...');

    // Clear all caches
    $this->call('cache:clear');
    $this->call('config:clear');
    $this->call('route:clear');
    $this->call('view:clear');

    // Optimize autoloader
    $this->call('optimize');

    // Cache everything
    $this->call('config:cache');
    $this->call('route:cache');
    $this->call('view:cache');

    $this->info('Optimization complete!');

})->purpose('Optimize The Real World LMS for production');

Artisan::command('lms:cleanup', function () {
    $this->info('Cleaning up The Real World LMS...');

    // Clean up old notifications
    $deletedNotifications = \DB::table('notifications')
        ->where('created_at', '<', now()->subDays(30))
        ->delete();

    // Clean up old sessions
    $this->call('session:gc');

    $this->info("Cleanup complete! Deleted {$deletedNotifications} old notifications.");

})->purpose('Clean up old data and temporary files');
