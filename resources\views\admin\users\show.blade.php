@extends('layouts.admin')

@section('title', 'User Details - ' . $user->name)

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.users.index') }}">Users</a></li>
                    <li class="breadcrumb-item active">{{ $user->name }}</li>
                </ol>
            </nav>
            <h1 class="h3 mb-0 text-gray-800">User Details</h1>
        </div>
        <div>
            <a href="{{ route('admin.users.edit', $user) }}" class="btn btn-primary">
                <i class="fas fa-edit"></i> Edit User
            </a>
            <a href="{{ route('admin.users.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Users
            </a>
        </div>
    </div>

    <div class="row">
        <!-- User Profile -->
        <div class="col-xl-4 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Profile Information</h6>
                </div>
                <div class="card-body text-center">
                    <img class="rounded-circle mb-3" 
                         src="{{ $user->avatar ? asset('storage/' . $user->avatar) : asset('images/default-avatar.png') }}" 
                         alt="{{ $user->name }}" width="120" height="120">
                    
                    <h4 class="mb-1">{{ $user->name }}</h4>
                    <p class="text-muted mb-3">{{ $user->email }}</p>
                    
                    <div class="mb-3">
                        @foreach($user->roles as $role)
                            <span class="badge badge-{{ $role->name == 'admin' ? 'danger' : ($role->name == 'mentor' ? 'info' : 'secondary') }} mr-1">
                                {{ ucfirst($role->name) }}
                            </span>
                        @endforeach
                    </div>

                    <div class="mb-3">
                        <span class="badge badge-{{ $user->is_active ? 'success' : 'danger' }}">
                            {{ $user->is_active ? 'Active' : 'Inactive' }}
                        </span>
                        @if($user->email_verified_at)
                            <span class="badge badge-success">Verified</span>
                        @else
                            <span class="badge badge-warning">Unverified</span>
                        @endif
                    </div>

                    @if($user->bio)
                    <div class="text-left">
                        <h6 class="font-weight-bold">Bio</h6>
                        <p class="text-muted">{{ $user->bio }}</p>
                    </div>
                    @endif

                    <div class="row text-center">
                        <div class="col-4">
                            <div class="font-weight-bold text-primary">{{ $user->userSubscriptions->count() }}</div>
                            <div class="text-muted small">Subscriptions</div>
                        </div>
                        <div class="col-4">
                            <div class="font-weight-bold text-success">{{ $user->userProgress->where('is_completed', true)->count() }}</div>
                            <div class="text-muted small">Completed</div>
                        </div>
                        <div class="col-4">
                            <div class="font-weight-bold text-info">{{ $user->courseRatings->count() }}</div>
                            <div class="text-muted small">Reviews</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Contact Information</h6>
                </div>
                <div class="card-body">
                    <div class="row mb-2">
                        <div class="col-4 font-weight-bold">Email:</div>
                        <div class="col-8">{{ $user->email }}</div>
                    </div>
                    @if($user->phone)
                    <div class="row mb-2">
                        <div class="col-4 font-weight-bold">Phone:</div>
                        <div class="col-8">{{ $user->phone }}</div>
                    </div>
                    @endif
                    @if($user->country)
                    <div class="row mb-2">
                        <div class="col-4 font-weight-bold">Country:</div>
                        <div class="col-8">{{ $user->country }}</div>
                    </div>
                    @endif
                    <div class="row mb-2">
                        <div class="col-4 font-weight-bold">Joined:</div>
                        <div class="col-8">{{ $user->created_at->format('M d, Y') }}</div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-4 font-weight-bold">Last Login:</div>
                        <div class="col-8">{{ $user->last_login_at ? $user->last_login_at->format('M d, Y H:i') : 'Never' }}</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- User Activity -->
        <div class="col-xl-8 col-lg-7">
            <!-- Subscription Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Subscription Information</h6>
                </div>
                <div class="card-body">
                    @if($user->activeSubscription)
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="font-weight-bold">Current Plan</h6>
                                <p class="mb-1">{{ $user->activeSubscription->subscriptionPlan->name }}</p>
                                <p class="text-muted small">{{ $user->activeSubscription->billing_cycle }}</p>
                            </div>
                            <div class="col-md-6">
                                <h6 class="font-weight-bold">Status</h6>
                                <span class="badge badge-success">{{ ucfirst($user->activeSubscription->status) }}</span>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <h6 class="font-weight-bold">Period</h6>
                                <p class="mb-0">{{ $user->activeSubscription->current_period_start->format('M d, Y') }} - {{ $user->activeSubscription->current_period_end->format('M d, Y') }}</p>
                            </div>
                            <div class="col-md-6">
                                <h6 class="font-weight-bold">Amount</h6>
                                <p class="mb-0">${{ number_format($user->activeSubscription->amount, 2) }} {{ $user->activeSubscription->currency }}</p>
                            </div>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No active subscription</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Learning Progress -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Learning Progress</h6>
                </div>
                <div class="card-body">
                    @if($user->userProgress->isNotEmpty())
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Course</th>
                                        <th>Progress</th>
                                        <th>Status</th>
                                        <th>Last Accessed</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($user->userProgress->groupBy('course_id') as $courseId => $progress)
                                        @php
                                            $course = $progress->first()->course;
                                            $totalLessons = $course->lessons->count();
                                            $completedLessons = $progress->where('is_completed', true)->count();
                                            $progressPercentage = $totalLessons > 0 ? ($completedLessons / $totalLessons) * 100 : 0;
                                        @endphp
                                        <tr>
                                            <td>{{ $course->title }}</td>
                                            <td>
                                                <div class="progress" style="height: 20px;">
                                                    <div class="progress-bar" role="progressbar" 
                                                         style="width: {{ $progressPercentage }}%"
                                                         aria-valuenow="{{ $progressPercentage }}" 
                                                         aria-valuemin="0" aria-valuemax="100">
                                                        {{ round($progressPercentage) }}%
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                @if($progressPercentage == 100)
                                                    <span class="badge badge-success">Completed</span>
                                                @elseif($progressPercentage > 0)
                                                    <span class="badge badge-primary">In Progress</span>
                                                @else
                                                    <span class="badge badge-secondary">Not Started</span>
                                                @endif
                                            </td>
                                            <td>{{ $progress->max('updated_at')->format('M d, Y') }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-book fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No learning progress yet</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Recent Activity</h6>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        @if($user->userProgress->isNotEmpty())
                            @foreach($user->userProgress->sortByDesc('updated_at')->take(10) as $progress)
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-{{ $progress->is_completed ? 'success' : 'primary' }}"></div>
                                    <div class="timeline-content">
                                        <h6 class="mb-1">
                                            {{ $progress->is_completed ? 'Completed' : 'Started' }} lesson
                                        </h6>
                                        <p class="mb-1">{{ $progress->lesson->title }} in {{ $progress->course->title }}</p>
                                        <small class="text-muted">{{ $progress->updated_at->diffForHumans() }}</small>
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <div class="text-center py-4">
                                <i class="fas fa-clock fa-3x text-muted mb-3"></i>
                                <p class="text-muted">No recent activity</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e3e6f0;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
}

.timeline-content {
    background: #f8f9fc;
    padding: 15px;
    border-radius: 5px;
    border-left: 3px solid #4e73df;
}
</style>
@endpush
