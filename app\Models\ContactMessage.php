<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ContactMessage extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'ticket_id',
        'first_name',
        'last_name',
        'email',
        'phone',
        'subject',
        'priority',
        'message',
        'attachment_path',
        'user_id',
        'status',
        'assigned_to',
        'resolved_at',
        'resolution_notes',
        'ip_address',
        'user_agent',
    ];

    protected $casts = [
        'resolved_at' => 'datetime',
    ];

    protected $dates = [
        'resolved_at',
        'deleted_at',
    ];

    // Status constants
    const STATUS_OPEN = 'open';
    const STATUS_IN_PROGRESS = 'in_progress';
    const STATUS_RESOLVED = 'resolved';
    const STATUS_CLOSED = 'closed';

    // Priority constants
    const PRIORITY_LOW = 'low';
    const PRIORITY_MEDIUM = 'medium';
    const PRIORITY_HIGH = 'high';
    const PRIORITY_CRITICAL = 'critical';

    // Subject constants
    const SUBJECT_GENERAL = 'general';
    const SUBJECT_TECHNICAL = 'technical';
    const SUBJECT_BILLING = 'billing';
    const SUBJECT_COURSE = 'course';
    const SUBJECT_ACCOUNT = 'account';
    const SUBJECT_PARTNERSHIP = 'partnership';
    const SUBJECT_FEEDBACK = 'feedback';
    const SUBJECT_OTHER = 'other';

    public static function getStatuses()
    {
        return [
            self::STATUS_OPEN => 'Open',
            self::STATUS_IN_PROGRESS => 'In Progress',
            self::STATUS_RESOLVED => 'Resolved',
            self::STATUS_CLOSED => 'Closed',
        ];
    }

    public static function getPriorities()
    {
        return [
            self::PRIORITY_LOW => 'Low',
            self::PRIORITY_MEDIUM => 'Medium',
            self::PRIORITY_HIGH => 'High',
            self::PRIORITY_CRITICAL => 'Critical',
        ];
    }

    public static function getSubjects()
    {
        return [
            self::SUBJECT_GENERAL => 'General Inquiry',
            self::SUBJECT_TECHNICAL => 'Technical Support',
            self::SUBJECT_BILLING => 'Billing & Payments',
            self::SUBJECT_COURSE => 'Course Related',
            self::SUBJECT_ACCOUNT => 'Account Issues',
            self::SUBJECT_PARTNERSHIP => 'Partnership Opportunities',
            self::SUBJECT_FEEDBACK => 'Feedback & Suggestions',
            self::SUBJECT_OTHER => 'Other',
        ];
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function assignedTo()
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    public function replies()
    {
        return $this->hasMany(ContactReply::class);
    }

    public function getFullNameAttribute()
    {
        return $this->first_name . ' ' . $this->last_name;
    }

    public function getStatusLabelAttribute()
    {
        return self::getStatuses()[$this->status] ?? 'Unknown';
    }

    public function getPriorityLabelAttribute()
    {
        return self::getPriorities()[$this->priority] ?? 'Unknown';
    }

    public function getSubjectLabelAttribute()
    {
        return self::getSubjects()[$this->subject] ?? 'Unknown';
    }

    public function getStatusColorAttribute()
    {
        $colors = [
            self::STATUS_OPEN => 'danger',
            self::STATUS_IN_PROGRESS => 'warning',
            self::STATUS_RESOLVED => 'success',
            self::STATUS_CLOSED => 'secondary',
        ];

        return $colors[$this->status] ?? 'secondary';
    }

    public function getPriorityColorAttribute()
    {
        $colors = [
            self::PRIORITY_LOW => 'success',
            self::PRIORITY_MEDIUM => 'warning',
            self::PRIORITY_HIGH => 'danger',
            self::PRIORITY_CRITICAL => 'dark',
        ];

        return $colors[$this->priority] ?? 'secondary';
    }

    public function getAttachmentUrlAttribute()
    {
        return $this->attachment_path ? asset('storage/' . $this->attachment_path) : null;
    }

    public function getAttachmentNameAttribute()
    {
        return $this->attachment_path ? basename($this->attachment_path) : null;
    }

    public function scopeOpen($query)
    {
        return $query->where('status', self::STATUS_OPEN);
    }

    public function scopeInProgress($query)
    {
        return $query->where('status', self::STATUS_IN_PROGRESS);
    }

    public function scopeResolved($query)
    {
        return $query->where('status', self::STATUS_RESOLVED);
    }

    public function scopeClosed($query)
    {
        return $query->where('status', self::STATUS_CLOSED);
    }

    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    public function scopeBySubject($query, $subject)
    {
        return $query->where('subject', $subject);
    }

    public function scopeAssignedTo($query, $userId)
    {
        return $query->where('assigned_to', $userId);
    }

    public function scopeUnassigned($query)
    {
        return $query->whereNull('assigned_to');
    }

    public function markAsInProgress(User $assignee = null)
    {
        $this->update([
            'status' => self::STATUS_IN_PROGRESS,
            'assigned_to' => $assignee ? $assignee->id : auth()->id(),
        ]);
    }

    public function markAsResolved($notes = null)
    {
        $this->update([
            'status' => self::STATUS_RESOLVED,
            'resolved_at' => now(),
            'resolution_notes' => $notes,
        ]);
    }

    public function markAsClosed()
    {
        $this->update([
            'status' => self::STATUS_CLOSED,
        ]);
    }

    public function getRouteKeyName()
    {
        return 'ticket_id';
    }
}
