APP_NAME="The Real World"
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

# LMS Configuration
LMS_NAME="The Real World"
LMS_TAGLINE="Escape The Matrix"
LMS_DESCRIPTION="The most exclusive online university in the world"

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=laravel
DB_USERNAME=root
DB_PASSWORD=

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

# Stripe Configuration
STRIPE_KEY=pk_test_
STRIPE_SECRET=sk_test_
STRIPE_WEBHOOK_SECRET=whsec_

# Cryptocurrency Settings
CRYPTO_BTC_ADDRESS=
CRYPTO_ETH_ADDRESS=
CRYPTO_USDT_ADDRESS=
CRYPTO_BNB_ADDRESS=

# Course Settings
COURSE_MAX_FILE_SIZE=100
AUTO_ENROLL_FREE_COURSES=true

# Subscription Settings
SUBSCRIPTION_TRIAL_DAYS=7
SUBSCRIPTION_GRACE_PERIOD_DAYS=3
ALLOW_SUBSCRIPTION_DOWNGRADES=true
PRORATE_SUBSCRIPTION_UPGRADES=true

# Payment Settings
DEFAULT_CURRENCY=USD
STRIPE_WEBHOOK_TOLERANCE=300

# Security Settings
TWO_FACTOR_ENABLED=true
PASSWORD_MIN_LENGTH=8
SESSION_TIMEOUT=120
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=60

# Analytics Settings
ANALYTICS_ENABLED=true
TRACK_USER_ACTIVITY=true
TRACK_COURSE_PROGRESS=true
TRACK_VIDEO_WATCH_TIME=true
ANALYTICS_RETENTION_DAYS=365

# Cache Settings
COURSE_CACHE_TTL=3600
USER_PROGRESS_CACHE_TTL=1800
SUBSCRIPTION_CACHE_TTL=7200

# API Settings
API_RATE_LIMIT=60
API_PAGINATION_LIMIT=50
API_VERSION=v1

# Social Media Links
SOCIAL_TWITTER=https://twitter.com/therealworld
SOCIAL_INSTAGRAM=https://instagram.com/therealworld
SOCIAL_YOUTUBE=https://youtube.com/therealworld
SOCIAL_TELEGRAM=https://t.me/therealworld
SOCIAL_DISCORD=https://discord.gg/therealworld

# Feature Flags
CERTIFICATES_ENABLED=true
GAMIFICATION_ENABLED=false
AFFILIATE_PROGRAM_ENABLED=false
MOBILE_APP_ENABLED=true
OFFLINE_MODE_ENABLED=false

# Email Settings
SUPPORT_EMAIL=<EMAIL>
EMAIL_LOGO_URL=https://therealworld.com/images/logo.png

# Maintenance Mode
MAINTENANCE_MODE_ENABLED=false
MAINTENANCE_MESSAGE="We are currently performing scheduled maintenance. Please check back soon."
MAINTENANCE_ALLOWED_IPS=

# Backup Settings
BACKUP_ENABLED=true
BACKUP_FREQUENCY=daily
BACKUP_RETENTION_DAYS=30
BACKUP_INCLUDE_UPLOADS=true

# Performance Settings
ENABLE_QUERY_CACHING=true
ENABLE_VIEW_CACHING=true
ENABLE_ROUTE_CACHING=true
ENABLE_CONFIG_CACHING=true
LAZY_LOAD_IMAGES=true
COMPRESS_RESPONSES=true

# Monitoring Settings
MONITORING_ENABLED=true
LOG_SLOW_QUERIES=true
SLOW_QUERY_THRESHOLD=1000
ERROR_REPORTING_ENABLED=true
UPTIME_MONITORING_ENABLED=true
