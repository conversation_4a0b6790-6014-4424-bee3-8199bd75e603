// Performance Optimization Module

class PerformanceManager {
    constructor() {
        this.observers = new Map();
        this.cache = new Map();
        this.init();
    }

    init() {
        this.setupLazyLoading();
        this.setupImageOptimization();
        this.setupResourcePreloading();
        this.setupCaching();
        this.setupDebouncing();
        this.setupVirtualScrolling();
        this.setupPerformanceMonitoring();
    }

    // Lazy Loading Implementation
    setupLazyLoading() {
        if (!('IntersectionObserver' in window)) {
            // Fallback for older browsers
            this.loadAllImages();
            return;
        }

        const lazyImageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    this.loadImage(img);
                    lazyImageObserver.unobserve(img);
                }
            });
        }, {
            rootMargin: '50px 0px',
            threshold: 0.01
        });

        // Observe all lazy images
        document.querySelectorAll('img[data-src]').forEach(img => {
            lazyImageObserver.observe(img);
        });

        // Lazy load other content
        const lazyContentObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const element = entry.target;
                    this.loadLazyContent(element);
                    lazyContentObserver.unobserve(element);
                }
            });
        }, {
            rootMargin: '100px 0px',
            threshold: 0.01
        });

        document.querySelectorAll('[data-lazy-load]').forEach(element => {
            lazyContentObserver.observe(element);
        });

        this.observers.set('lazyImage', lazyImageObserver);
        this.observers.set('lazyContent', lazyContentObserver);
    }

    loadImage(img) {
        return new Promise((resolve, reject) => {
            const imageLoader = new Image();
            
            imageLoader.onload = () => {
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                img.classList.add('loaded');
                
                // Remove data-src to prevent reprocessing
                delete img.dataset.src;
                
                resolve(img);
            };
            
            imageLoader.onerror = () => {
                img.classList.add('error');
                reject(new Error('Image failed to load'));
            };
            
            imageLoader.src = img.dataset.src;
        });
    }

    loadLazyContent(element) {
        const url = element.dataset.lazyLoad;
        
        if (this.cache.has(url)) {
            element.innerHTML = this.cache.get(url);
            element.classList.add('loaded');
            return Promise.resolve();
        }

        element.classList.add('loading');
        
        return fetch(url)
            .then(response => response.text())
            .then(html => {
                this.cache.set(url, html);
                element.innerHTML = html;
                element.classList.remove('loading');
                element.classList.add('loaded');
            })
            .catch(error => {
                element.classList.remove('loading');
                element.classList.add('error');
                console.error('Failed to load lazy content:', error);
            });
    }

    loadAllImages() {
        document.querySelectorAll('img[data-src]').forEach(img => {
            img.src = img.dataset.src;
            delete img.dataset.src;
        });
    }

    // Image Optimization
    setupImageOptimization() {
        // WebP support detection
        this.supportsWebP = this.checkWebPSupport();
        
        // Responsive images
        this.setupResponsiveImages();
        
        // Image compression
        this.setupImageCompression();
    }

    checkWebPSupport() {
        return new Promise(resolve => {
            const webP = new Image();
            webP.onload = webP.onerror = () => {
                resolve(webP.height === 2);
            };
            webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA';
        });
    }

    setupResponsiveImages() {
        const images = document.querySelectorAll('img[data-srcset]');
        
        images.forEach(img => {
            if (window.devicePixelRatio > 1) {
                // High DPI display
                const srcset = img.dataset.srcset;
                if (srcset) {
                    img.srcset = srcset;
                }
            }
        });
    }

    setupImageCompression() {
        // Implement client-side image compression for uploads
        const fileInputs = document.querySelectorAll('input[type="file"][accept*="image"]');
        
        fileInputs.forEach(input => {
            input.addEventListener('change', (e) => {
                const files = Array.from(e.target.files);
                Promise.all(files.map(file => this.compressImage(file)))
                    .then(compressedFiles => {
                        // Replace original files with compressed versions
                        const dt = new DataTransfer();
                        compressedFiles.forEach(file => dt.items.add(file));
                        input.files = dt.files;
                    });
            });
        });
    }

    compressImage(file, quality = 0.8, maxWidth = 1920, maxHeight = 1080) {
        return new Promise(resolve => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();
            
            img.onload = () => {
                // Calculate new dimensions
                let { width, height } = img;
                
                if (width > maxWidth) {
                    height = (height * maxWidth) / width;
                    width = maxWidth;
                }
                
                if (height > maxHeight) {
                    width = (width * maxHeight) / height;
                    height = maxHeight;
                }
                
                canvas.width = width;
                canvas.height = height;
                
                // Draw and compress
                ctx.drawImage(img, 0, 0, width, height);
                
                canvas.toBlob(resolve, 'image/jpeg', quality);
            };
            
            img.src = URL.createObjectURL(file);
        });
    }

    // Resource Preloading
    setupResourcePreloading() {
        // Preload critical resources
        this.preloadCriticalResources();
        
        // Prefetch likely next pages
        this.setupPrefetching();
        
        // DNS prefetch for external domains
        this.setupDNSPrefetch();
    }

    preloadCriticalResources() {
        const criticalResources = [
            { href: '/css/app.css', as: 'style' },
            { href: '/js/app.js', as: 'script' },
            // Add more critical resources
        ];

        criticalResources.forEach(resource => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.href = resource.href;
            link.as = resource.as;
            document.head.appendChild(link);
        });
    }

    setupPrefetching() {
        // Prefetch on hover
        document.addEventListener('mouseover', (e) => {
            const link = e.target.closest('a[href]');
            if (link && this.shouldPrefetch(link.href)) {
                this.prefetchPage(link.href);
            }
        });

        // Prefetch visible links
        if ('IntersectionObserver' in window) {
            const prefetchObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const link = entry.target;
                        if (this.shouldPrefetch(link.href)) {
                            this.prefetchPage(link.href);
                        }
                        prefetchObserver.unobserve(link);
                    }
                });
            });

            document.querySelectorAll('a[href]').forEach(link => {
                prefetchObserver.observe(link);
            });
        }
    }

    shouldPrefetch(url) {
        // Don't prefetch external links, files, or already prefetched URLs
        return url.startsWith('/') && 
               !url.includes('.') && 
               !this.cache.has(`prefetch:${url}`);
    }

    prefetchPage(url) {
        if (this.cache.has(`prefetch:${url}`)) return;
        
        const link = document.createElement('link');
        link.rel = 'prefetch';
        link.href = url;
        document.head.appendChild(link);
        
        this.cache.set(`prefetch:${url}`, true);
    }

    setupDNSPrefetch() {
        const externalDomains = [
            'fonts.googleapis.com',
            'fonts.gstatic.com',
            'cdn.jsdelivr.net',
            // Add more external domains
        ];

        externalDomains.forEach(domain => {
            const link = document.createElement('link');
            link.rel = 'dns-prefetch';
            link.href = `//${domain}`;
            document.head.appendChild(link);
        });
    }

    // Caching System
    setupCaching() {
        // Service Worker registration
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('/sw.js')
                .then(registration => {
                    console.log('SW registered:', registration);
                })
                .catch(error => {
                    console.log('SW registration failed:', error);
                });
        }

        // Memory cache for API responses
        this.setupAPICache();
    }

    setupAPICache() {
        const originalFetch = window.fetch;
        
        window.fetch = (url, options = {}) => {
            const cacheKey = `${url}:${JSON.stringify(options)}`;
            
            // Check cache for GET requests
            if (!options.method || options.method === 'GET') {
                if (this.cache.has(cacheKey)) {
                    return Promise.resolve(this.cache.get(cacheKey).clone());
                }
            }
            
            return originalFetch(url, options).then(response => {
                // Cache successful GET responses
                if (response.ok && (!options.method || options.method === 'GET')) {
                    this.cache.set(cacheKey, response.clone());
                }
                return response;
            });
        };
    }

    // Debouncing for Performance
    setupDebouncing() {
        // Debounce scroll events
        this.debounceScrollEvents();
        
        // Debounce resize events
        this.debounceResizeEvents();
        
        // Debounce input events
        this.debounceInputEvents();
    }

    debounceScrollEvents() {
        let scrollTimeout;
        const originalScrollHandler = window.onscroll;
        
        window.addEventListener('scroll', () => {
            clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(() => {
                if (originalScrollHandler) originalScrollHandler();
                this.handleScroll();
            }, 16); // ~60fps
        }, { passive: true });
    }

    debounceResizeEvents() {
        let resizeTimeout;
        
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
                this.handleResize();
            }, 250);
        });
    }

    debounceInputEvents() {
        document.addEventListener('input', this.debounce((e) => {
            if (e.target.matches('[data-search]')) {
                this.handleSearch(e.target);
            }
        }, 300));
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Virtual Scrolling for Large Lists
    setupVirtualScrolling() {
        const virtualLists = document.querySelectorAll('[data-virtual-scroll]');
        
        virtualLists.forEach(list => {
            this.initVirtualScroll(list);
        });
    }

    initVirtualScroll(container) {
        const itemHeight = parseInt(container.dataset.itemHeight) || 50;
        const items = JSON.parse(container.dataset.items || '[]');
        const visibleCount = Math.ceil(container.offsetHeight / itemHeight) + 2;
        
        let scrollTop = 0;
        let startIndex = 0;
        
        const updateVisibleItems = () => {
            startIndex = Math.floor(scrollTop / itemHeight);
            const endIndex = Math.min(startIndex + visibleCount, items.length);
            
            const visibleItems = items.slice(startIndex, endIndex);
            this.renderVirtualItems(container, visibleItems, startIndex, itemHeight);
        };
        
        container.addEventListener('scroll', () => {
            scrollTop = container.scrollTop;
            requestAnimationFrame(updateVisibleItems);
        });
        
        // Initial render
        updateVisibleItems();
    }

    renderVirtualItems(container, items, startIndex, itemHeight) {
        const totalHeight = container.dataset.items ? JSON.parse(container.dataset.items).length * itemHeight : 0;
        
        container.innerHTML = `
            <div style="height: ${totalHeight}px; position: relative;">
                ${items.map((item, index) => `
                    <div style="position: absolute; top: ${(startIndex + index) * itemHeight}px; height: ${itemHeight}px; width: 100%;">
                        ${this.renderVirtualItem(item)}
                    </div>
                `).join('')}
            </div>
        `;
    }

    renderVirtualItem(item) {
        // Override this method to customize item rendering
        return `<div class="virtual-item">${JSON.stringify(item)}</div>`;
    }

    // Performance Monitoring
    setupPerformanceMonitoring() {
        // Monitor Core Web Vitals
        this.monitorCoreWebVitals();
        
        // Monitor resource loading
        this.monitorResourceLoading();
        
        // Monitor JavaScript errors
        this.monitorErrors();
    }

    monitorCoreWebVitals() {
        // Largest Contentful Paint
        new PerformanceObserver((list) => {
            const entries = list.getEntries();
            const lastEntry = entries[entries.length - 1];
            console.log('LCP:', lastEntry.startTime);
        }).observe({ entryTypes: ['largest-contentful-paint'] });

        // First Input Delay
        new PerformanceObserver((list) => {
            const entries = list.getEntries();
            entries.forEach(entry => {
                console.log('FID:', entry.processingStart - entry.startTime);
            });
        }).observe({ entryTypes: ['first-input'] });

        // Cumulative Layout Shift
        new PerformanceObserver((list) => {
            let clsValue = 0;
            const entries = list.getEntries();
            entries.forEach(entry => {
                if (!entry.hadRecentInput) {
                    clsValue += entry.value;
                }
            });
            console.log('CLS:', clsValue);
        }).observe({ entryTypes: ['layout-shift'] });
    }

    monitorResourceLoading() {
        window.addEventListener('load', () => {
            const perfData = performance.getEntriesByType('navigation')[0];
            console.log('Page Load Time:', perfData.loadEventEnd - perfData.fetchStart);
        });
    }

    monitorErrors() {
        window.addEventListener('error', (e) => {
            console.error('JavaScript Error:', e.error);
        });

        window.addEventListener('unhandledrejection', (e) => {
            console.error('Unhandled Promise Rejection:', e.reason);
        });
    }

    // Utility methods
    handleScroll() {
        // Custom scroll handling
    }

    handleResize() {
        // Custom resize handling
    }

    handleSearch(input) {
        // Custom search handling
        const query = input.value;
        const target = input.dataset.search;
        // Implement search logic
    }

    // Cleanup
    destroy() {
        this.observers.forEach(observer => observer.disconnect());
        this.cache.clear();
    }
}

// Initialize performance manager
document.addEventListener('DOMContentLoaded', () => {
    window.performanceManager = new PerformanceManager();
});

export default PerformanceManager;
