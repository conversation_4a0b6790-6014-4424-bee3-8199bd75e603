@extends('layouts.app')

@section('title', '- Test Payment System')

@section('content')
<div class="min-h-screen bg-gray-50 py-12">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-900 mb-4">Payment System Test</h1>
            <p class="text-xl text-gray-600">
                Test the Stripe payment integration with our subscription plans
            </p>
        </div>

        <!-- Payment System Status -->
        <div class="bg-white shadow-lg rounded-lg p-6 mb-8">
            <h2 class="text-2xl font-semibold text-gray-900 mb-4">System Status</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="flex items-center p-4 bg-green-50 rounded-lg">
                    <div class="flex-shrink-0">
                        <svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-green-800">Laravel Cashier</p>
                        <p class="text-sm text-green-600">Installed & Configured</p>
                    </div>
                </div>

                <div class="flex items-center p-4 bg-blue-50 rounded-lg">
                    <div class="flex-shrink-0">
                        <svg class="h-8 w-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-blue-800">Stripe Integration</p>
                        <p class="text-sm text-blue-600">Ready for Testing</p>
                    </div>
                </div>

                <div class="flex items-center p-4 bg-purple-50 rounded-lg">
                    <div class="flex-shrink-0">
                        <svg class="h-8 w-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-purple-800">Subscription Plans</p>
                        <p class="text-sm text-purple-600">{{ $plans->count() }} Plans Available</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Available Plans -->
        <div class="bg-white shadow-lg rounded-lg p-6 mb-8">
            <h2 class="text-2xl font-semibold text-gray-900 mb-6">Available Subscription Plans</h2>
            
            @if($plans->count() > 0)
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    @foreach($plans as $plan)
                        <div class="border border-gray-200 rounded-lg p-6 {{ $plan->is_featured ? 'ring-2 ring-blue-500 relative' : '' }}">
                            @if($plan->is_featured)
                                <div class="absolute -top-3 left-1/2 transform -translate-x-1/2">
                                    <span class="bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-medium">Most Popular</span>
                                </div>
                            @endif
                            
                            <div class="text-center">
                                <h3 class="text-xl font-bold text-gray-900 mb-2">{{ $plan->name }}</h3>
                                <p class="text-gray-600 mb-4">{{ $plan->description }}</p>
                                
                                <div class="mb-6">
                                    <div class="text-3xl font-bold text-gray-900 mb-1">
                                        ${{ number_format($plan->monthly_price, 0) }}
                                    </div>
                                    <div class="text-sm text-gray-600">per month</div>
                                    
                                    @if($plan->yearly_price)
                                        <div class="mt-2 text-sm text-green-600">
                                            Save ${{ number_format(($plan->monthly_price * 12) - $plan->yearly_price, 0) }} yearly
                                        </div>
                                    @endif
                                </div>
                                
                                <ul class="text-sm text-gray-600 mb-6 space-y-2">
                                    @foreach($plan->features as $feature)
                                        <li class="flex items-center">
                                            <svg class="h-4 w-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                            {{ $feature }}
                                        </li>
                                    @endforeach
                                </ul>
                                
                                <div class="space-y-2">
                                    <a href="{{ route('subscriptions.checkout', ['plan' => $plan->id, 'billing' => 'monthly']) }}" 
                                        class="block w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-lg transition duration-300">
                                        Test Monthly Payment
                                    </a>
                                    
                                    @if($plan->yearly_price)
                                        <a href="{{ route('subscriptions.checkout', ['plan' => $plan->id, 'billing' => 'yearly']) }}" 
                                            class="block w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-2 px-4 rounded-lg transition duration-300">
                                            Test Yearly Payment
                                        </a>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-8">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No subscription plans found</h3>
                    <p class="mt-1 text-sm text-gray-500">Run the SubscriptionPlanSeeder to create test plans.</p>
                    <div class="mt-6">
                        <code class="bg-gray-100 px-3 py-1 rounded text-sm">php artisan db:seed --class=SubscriptionPlanSeeder</code>
                    </div>
                </div>
            @endif
        </div>

        <!-- Test Instructions -->
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-yellow-800">Testing Instructions</h3>
                    <div class="mt-2 text-sm text-yellow-700">
                        <ul class="list-disc list-inside space-y-1">
                            <li>Make sure you have Stripe test keys configured in your .env file</li>
                            <li>Use Stripe test card numbers (e.g., 4242 4242 4242 4242)</li>
                            <li>Any future expiry date and any 3-digit CVC will work</li>
                            <li>Check your Stripe dashboard to see test payments</li>
                            <li>Webhooks should be configured for production use</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Links -->
        <div class="mt-8 text-center">
            <div class="space-x-4">
                <a href="{{ route('subscriptions.index') }}" 
                    class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                    View All Plans
                </a>
                
                <a href="{{ route('dashboard') }}" 
                    class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
                    Go to Dashboard
                </a>
            </div>
        </div>
    </div>
</div>
@endsection
