@extends('layouts.app')

@section('content')
<!-- Hero Section -->
<div class="bg-gradient-to-r from-blue-600 to-purple-700 text-white py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 class="text-4xl md:text-5xl font-bold mb-4">
            Choose Your Plan
        </h1>
        <p class="text-xl md:text-2xl text-blue-100 mb-8">
            Start your journey to financial freedom with the plan that's right for you
        </p>
        <div class="flex justify-center">
            <div class="bg-white bg-opacity-20 rounded-lg p-1 flex">
                <button onclick="toggleBilling('monthly')" id="monthly-btn" class="px-4 py-2 rounded-md text-sm font-medium transition-all duration-300 bg-white text-blue-600">
                    Monthly
                </button>
                <button onclick="toggleBilling('yearly')" id="yearly-btn" class="px-4 py-2 rounded-md text-sm font-medium transition-all duration-300 text-white">
                    Yearly
                </button>
            </div>
        </div>
        <p class="text-sm text-blue-200 mt-2">
            <span id="savings-text" class="hidden">Save up to 20% with yearly billing</span>
        </p>
    </div>
</div>

<!-- Pricing Cards -->
<div class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            @foreach($subscriptionPlans as $plan)
            <div class="bg-white rounded-lg shadow-lg overflow-hidden {{ $plan->is_featured ? 'ring-2 ring-blue-500 transform scale-105' : '' }}">
                @if($plan->is_featured)
                    <div class="bg-blue-500 text-white text-center py-2 px-4 text-sm font-medium">
                        Most Popular
                    </div>
                @endif
                
                <div class="p-6">
                    <h3 class="text-2xl font-bold text-center mb-4">{{ $plan->name }}</h3>
                    
                    <!-- Monthly Pricing -->
                    <div class="text-center mb-6 monthly-pricing">
                        <span class="text-4xl font-bold">${{ number_format($plan->monthly_price, 0) }}</span>
                        <span class="text-gray-600">/month</span>
                        <div class="text-sm text-gray-500 mt-1">
                            Billed monthly
                        </div>
                    </div>
                    
                    <!-- Yearly Pricing -->
                    <div class="text-center mb-6 yearly-pricing hidden">
                        <span class="text-4xl font-bold">${{ number_format($plan->yearly_price / 12, 0) }}</span>
                        <span class="text-gray-600">/month</span>
                        <div class="text-sm text-gray-500 mt-1">
                            ${{ number_format($plan->yearly_price, 0) }} billed yearly
                        </div>
                        <div class="text-sm text-green-600 font-medium">
                            Save ${{ number_format(($plan->monthly_price * 12) - $plan->yearly_price, 0) }}
                        </div>
                    </div>
                    
                    <p class="text-gray-600 text-center mb-6">{{ $plan->description }}</p>
                    
                    <ul class="space-y-3 mb-8">
                        @foreach($plan->features as $feature)
                        <li class="flex items-start">
                            <svg class="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span class="text-sm text-gray-700">{{ $feature }}</span>
                        </li>
                        @endforeach
                    </ul>
                    
                    @auth
                        @if(Auth::user()->hasActiveSubscription() && Auth::user()->activeSubscription->subscriptionPlan->id === $plan->id)
                            <div class="text-center">
                                <span class="inline-block bg-green-100 text-green-800 px-4 py-2 rounded-lg font-medium">
                                    Current Plan
                                </span>
                            </div>
                        @else
                            <div class="space-y-3">
                                <a href="{{ route('subscriptions.checkout', ['plan' => $plan, 'billing' => 'monthly']) }}" 
                                   class="monthly-cta w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-4 rounded-lg text-center block transition duration-300">
                                    Get Started
                                </a>
                                <a href="{{ route('subscriptions.checkout', ['plan' => $plan, 'billing' => 'yearly']) }}" 
                                   class="yearly-cta hidden w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-4 rounded-lg text-center block transition duration-300">
                                    Get Started
                                </a>
                            </div>
                        @endif
                    @else
                        <div class="space-y-3">
                            <a href="{{ route('register') }}" 
                               class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-4 rounded-lg text-center block transition duration-300">
                                Get Started
                            </a>
                        </div>
                    @endauth
                </div>
            </div>
            @endforeach
        </div>
    </div>
</div>

<!-- FAQ Section -->
<div class="py-16 bg-white">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Frequently Asked Questions</h2>
            <p class="text-lg text-gray-600">Everything you need to know about our pricing</p>
        </div>
        
        <div class="space-y-8">
            <div class="border-b border-gray-200 pb-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Can I cancel my subscription anytime?</h3>
                <p class="text-gray-600">Yes, you can cancel your subscription at any time. You'll continue to have access until the end of your current billing period.</p>
            </div>
            
            <div class="border-b border-gray-200 pb-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-2">What payment methods do you accept?</h3>
                <p class="text-gray-600">We accept all major credit cards (Visa, MasterCard, American Express) and cryptocurrency payments (USDT, USDC).</p>
            </div>
            
            <div class="border-b border-gray-200 pb-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Can I upgrade or downgrade my plan?</h3>
                <p class="text-gray-600">Yes, you can change your plan at any time. Changes will be prorated and reflected in your next billing cycle.</p>
            </div>
            
            <div class="border-b border-gray-200 pb-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Is there a free trial?</h3>
                <p class="text-gray-600">We offer a 7-day free trial for all new subscribers. No credit card required to start your trial.</p>
            </div>
            
            <div class="border-b border-gray-200 pb-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Do you offer refunds?</h3>
                <p class="text-gray-600">We offer a 30-day money-back guarantee. If you're not satisfied, contact our support team for a full refund.</p>
            </div>
        </div>
    </div>
</div>

<!-- CTA Section -->
<div class="bg-blue-600 text-white py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl font-bold mb-4">Ready to Start Your Journey?</h2>
        <p class="text-xl mb-8">Join thousands of successful students today</p>
        <a href="{{ route('register') }}" class="bg-yellow-400 hover:bg-yellow-500 text-black font-bold py-3 px-8 rounded-lg text-lg transition duration-300">
            Get Started Now
        </a>
    </div>
</div>

<script>
function toggleBilling(type) {
    const monthlyBtn = document.getElementById('monthly-btn');
    const yearlyBtn = document.getElementById('yearly-btn');
    const savingsText = document.getElementById('savings-text');
    const monthlyPricing = document.querySelectorAll('.monthly-pricing');
    const yearlyPricing = document.querySelectorAll('.yearly-pricing');
    const monthlyCta = document.querySelectorAll('.monthly-cta');
    const yearlyCta = document.querySelectorAll('.yearly-cta');
    
    if (type === 'monthly') {
        monthlyBtn.classList.add('bg-white', 'text-blue-600');
        monthlyBtn.classList.remove('text-white');
        yearlyBtn.classList.remove('bg-white', 'text-blue-600');
        yearlyBtn.classList.add('text-white');
        savingsText.classList.add('hidden');
        
        monthlyPricing.forEach(el => el.classList.remove('hidden'));
        yearlyPricing.forEach(el => el.classList.add('hidden'));
        monthlyCta.forEach(el => el.classList.remove('hidden'));
        yearlyCta.forEach(el => el.classList.add('hidden'));
    } else {
        yearlyBtn.classList.add('bg-white', 'text-blue-600');
        yearlyBtn.classList.remove('text-white');
        monthlyBtn.classList.remove('bg-white', 'text-blue-600');
        monthlyBtn.classList.add('text-white');
        savingsText.classList.remove('hidden');
        
        monthlyPricing.forEach(el => el.classList.add('hidden'));
        yearlyPricing.forEach(el => el.classList.remove('hidden'));
        monthlyCta.forEach(el => el.classList.add('hidden'));
        yearlyCta.forEach(el => el.classList.remove('hidden'));
    }
}
</script>
@endsection
