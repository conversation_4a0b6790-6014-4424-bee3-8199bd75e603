#!/bin/bash

# The Real World LMS Backup Script
# Automated backup system for database, files, and configurations

set -e

# Configuration
APP_DIR="/public_html"
BACKUP_DIR="/home/<USER>"
S3_BUCKET="your-backup-bucket"
RETENTION_DAYS=30
NOTIFICATION_EMAIL="<EMAIL>"

# Database configuration
DB_HOST="localhost"
DB_NAME="realworld_lms"
DB_USER="lms_user"
DB_PASS="your_password"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
}

warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

# Send notification
send_notification() {
    local subject="$1"
    local message="$2"
    local status="$3"
    
    if [ -n "$NOTIFICATION_EMAIL" ]; then
        {
            echo "Backup Status: $status"
            echo "Timestamp: $(date)"
            echo "Server: $(hostname)"
            echo ""
            echo "$message"
        } | mail -s "$subject" "$NOTIFICATION_EMAIL"
    fi
}

# Create backup directory structure
setup_backup_directory() {
    local backup_date=$(date +%Y%m%d_%H%M%S)
    BACKUP_PATH="$BACKUP_DIR/$backup_date"
    
    mkdir -p "$BACKUP_PATH"/{database,files,config}
    
    log "Backup directory created: $BACKUP_PATH"
}

# Backup database
backup_database() {
    log "Starting database backup..."
    
    local db_backup_file="$BACKUP_PATH/database/database_$(date +%Y%m%d_%H%M%S).sql"
    
    # Create database dump
    if mysqldump -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" \
        --single-transaction \
        --routines \
        --triggers \
        --events \
        --add-drop-database \
        --databases "$DB_NAME" > "$db_backup_file"; then
        
        # Compress the backup
        gzip "$db_backup_file"
        
        local backup_size=$(du -h "${db_backup_file}.gz" | cut -f1)
        log "Database backup completed: ${backup_size}"
        
        # Verify backup integrity
        if gunzip -t "${db_backup_file}.gz"; then
            log "Database backup integrity verified"
        else
            error "Database backup integrity check failed"
            return 1
        fi
        
    else
        error "Database backup failed"
        return 1
    fi
}

# Backup application files
backup_files() {
    log "Starting file backup..."
    
    local files_backup="$BACKUP_PATH/files/files_$(date +%Y%m%d_%H%M%S).tar.gz"
    
    # Create list of directories to backup
    local backup_dirs=(
        "storage/app"
        "public/uploads"
        "public/storage"
    )
    
    # Create file backup
    cd "$APP_DIR"
    
    if tar -czf "$files_backup" \
        --exclude='storage/logs/*' \
        --exclude='storage/framework/cache/*' \
        --exclude='storage/framework/sessions/*' \
        --exclude='storage/framework/views/*' \
        "${backup_dirs[@]}" 2>/dev/null; then
        
        local backup_size=$(du -h "$files_backup" | cut -f1)
        log "File backup completed: ${backup_size}"
        
        # Verify backup integrity
        if tar -tzf "$files_backup" > /dev/null; then
            log "File backup integrity verified"
        else
            error "File backup integrity check failed"
            return 1
        fi
        
    else
        error "File backup failed"
        return 1
    fi
}

# Backup configuration files
backup_config() {
    log "Starting configuration backup..."
    
    local config_backup="$BACKUP_PATH/config/config_$(date +%Y%m%d_%H%M%S).tar.gz"
    
    # Configuration files to backup
    local config_files=(
        ".env"
        "config/"
        "composer.json"
        "composer.lock"
        "package.json"
        "package-lock.json"
        ".htaccess"
    )
    
    cd "$APP_DIR"
    
    if tar -czf "$config_backup" "${config_files[@]}" 2>/dev/null; then
        local backup_size=$(du -h "$config_backup" | cut -f1)
        log "Configuration backup completed: ${backup_size}"
    else
        error "Configuration backup failed"
        return 1
    fi
}

# Upload to S3 (if configured)
upload_to_s3() {
    if [ -z "$S3_BUCKET" ]; then
        info "S3 upload not configured, skipping..."
        return 0
    fi
    
    log "Uploading backup to S3..."
    
    local backup_name=$(basename "$BACKUP_PATH")
    local s3_path="s3://$S3_BUCKET/backups/$backup_name"
    
    # Create compressed archive of entire backup
    local archive_name="${backup_name}.tar.gz"
    local archive_path="$BACKUP_DIR/$archive_name"
    
    tar -czf "$archive_path" -C "$BACKUP_DIR" "$backup_name"
    
    # Upload to S3
    if aws s3 cp "$archive_path" "$s3_path.tar.gz"; then
        log "Backup uploaded to S3: $s3_path.tar.gz"
        
        # Remove local archive after successful upload
        rm -f "$archive_path"
        
    else
        error "S3 upload failed"
        return 1
    fi
}

# Clean old backups
cleanup_old_backups() {
    log "Cleaning up old backups..."
    
    # Clean local backups older than retention period
    find "$BACKUP_DIR" -type d -name "20*" -mtime +$RETENTION_DAYS -exec rm -rf {} + 2>/dev/null || true
    
    # Clean S3 backups if configured
    if [ -n "$S3_BUCKET" ]; then
        local cutoff_date=$(date -d "$RETENTION_DAYS days ago" +%Y%m%d)
        
        aws s3 ls "s3://$S3_BUCKET/backups/" | while read -r line; do
            local backup_date=$(echo "$line" | awk '{print $2}' | cut -d'_' -f1)
            if [[ "$backup_date" < "$cutoff_date" ]]; then
                local backup_file=$(echo "$line" | awk '{print $4}')
                aws s3 rm "s3://$S3_BUCKET/backups/$backup_file"
                log "Removed old S3 backup: $backup_file"
            fi
        done
    fi
    
    local remaining_backups=$(find "$BACKUP_DIR" -type d -name "20*" | wc -l)
    log "Cleanup completed. $remaining_backups local backups remaining"
}

# Verify backup completeness
verify_backup() {
    log "Verifying backup completeness..."
    
    local errors=0
    
    # Check database backup
    if [ ! -f "$BACKUP_PATH/database/"*.sql.gz ]; then
        error "Database backup file not found"
        ((errors++))
    fi
    
    # Check file backup
    if [ ! -f "$BACKUP_PATH/files/"*.tar.gz ]; then
        error "File backup not found"
        ((errors++))
    fi
    
    # Check config backup
    if [ ! -f "$BACKUP_PATH/config/"*.tar.gz ]; then
        error "Configuration backup not found"
        ((errors++))
    fi
    
    if [ $errors -eq 0 ]; then
        log "Backup verification passed"
        return 0
    else
        error "Backup verification failed with $errors errors"
        return 1
    fi
}

# Generate backup report
generate_backup_report() {
    local report_file="$BACKUP_PATH/backup_report.txt"
    local total_size=$(du -sh "$BACKUP_PATH" | cut -f1)
    
    {
        echo "The Real World LMS Backup Report"
        echo "================================"
        echo "Backup Date: $(date)"
        echo "Backup Path: $BACKUP_PATH"
        echo "Total Size: $total_size"
        echo ""
        
        echo "Database Backup:"
        ls -lh "$BACKUP_PATH/database/" | tail -n +2
        echo ""
        
        echo "File Backup:"
        ls -lh "$BACKUP_PATH/files/" | tail -n +2
        echo ""
        
        echo "Configuration Backup:"
        ls -lh "$BACKUP_PATH/config/" | tail -n +2
        echo ""
        
        echo "Backup Contents:"
        find "$BACKUP_PATH" -type f -exec ls -lh {} \; | awk '{print $5, $9}'
        
    } > "$report_file"
    
    log "Backup report generated: $report_file"
}

# Test backup restoration (dry run)
test_restoration() {
    log "Testing backup restoration (dry run)..."
    
    local test_dir="/tmp/backup_test_$(date +%s)"
    mkdir -p "$test_dir"
    
    # Test database restoration
    local db_backup=$(find "$BACKUP_PATH/database" -name "*.sql.gz" | head -1)
    if [ -n "$db_backup" ]; then
        if gunzip -c "$db_backup" | head -100 > /dev/null; then
            log "Database backup can be restored"
        else
            error "Database backup restoration test failed"
        fi
    fi
    
    # Test file restoration
    local file_backup=$(find "$BACKUP_PATH/files" -name "*.tar.gz" | head -1)
    if [ -n "$file_backup" ]; then
        if tar -tzf "$file_backup" > /dev/null; then
            log "File backup can be restored"
        else
            error "File backup restoration test failed"
        fi
    fi
    
    # Cleanup test directory
    rm -rf "$test_dir"
    
    log "Restoration test completed"
}

# Main backup function
main() {
    local start_time=$(date +%s)
    
    log "Starting backup process..."
    
    # Setup
    setup_backup_directory
    
    # Perform backups
    local backup_success=true
    
    if ! backup_database; then
        backup_success=false
    fi
    
    if ! backup_files; then
        backup_success=false
    fi
    
    if ! backup_config; then
        backup_success=false
    fi
    
    # Verify backup
    if ! verify_backup; then
        backup_success=false
    fi
    
    # Test restoration
    test_restoration
    
    # Upload to S3
    if [ "$backup_success" = true ]; then
        upload_to_s3
    fi
    
    # Generate report
    generate_backup_report
    
    # Cleanup old backups
    cleanup_old_backups
    
    # Calculate duration
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    local duration_formatted=$(printf '%02d:%02d:%02d' $((duration/3600)) $((duration%3600/60)) $((duration%60)))
    
    # Send notification
    if [ "$backup_success" = true ]; then
        log "Backup completed successfully in $duration_formatted"
        send_notification "Backup Successful" "Backup completed successfully in $duration_formatted. Location: $BACKUP_PATH" "SUCCESS"
    else
        error "Backup completed with errors in $duration_formatted"
        send_notification "Backup Failed" "Backup completed with errors in $duration_formatted. Please check logs." "FAILED"
        exit 1
    fi
}

# Restore function
restore_backup() {
    local backup_path="$1"
    
    if [ -z "$backup_path" ]; then
        error "Backup path not specified"
        exit 1
    fi
    
    if [ ! -d "$backup_path" ]; then
        error "Backup directory not found: $backup_path"
        exit 1
    fi
    
    log "Starting restoration from: $backup_path"
    
    # Confirm restoration
    read -p "This will overwrite current data. Are you sure? (yes/no): " confirm
    if [ "$confirm" != "yes" ]; then
        log "Restoration cancelled"
        exit 0
    fi
    
    # Restore database
    local db_backup=$(find "$backup_path/database" -name "*.sql.gz" | head -1)
    if [ -n "$db_backup" ]; then
        log "Restoring database..."
        gunzip -c "$db_backup" | mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS"
        log "Database restored"
    fi
    
    # Restore files
    local file_backup=$(find "$backup_path/files" -name "*.tar.gz" | head -1)
    if [ -n "$file_backup" ]; then
        log "Restoring files..."
        cd "$APP_DIR"
        tar -xzf "$file_backup"
        log "Files restored"
    fi
    
    # Restore configuration
    local config_backup=$(find "$backup_path/config" -name "*.tar.gz" | head -1)
    if [ -n "$config_backup" ]; then
        log "Restoring configuration..."
        cd "$APP_DIR"
        tar -xzf "$config_backup"
        log "Configuration restored"
    fi
    
    log "Restoration completed"
}

# Handle script arguments
case "${1:-backup}" in
    "backup")
        main
        ;;
    "restore")
        restore_backup "$2"
        ;;
    "cleanup")
        cleanup_old_backups
        ;;
    "verify")
        if [ -n "$2" ]; then
            BACKUP_PATH="$2"
            verify_backup
        else
            error "Backup path required for verification"
            exit 1
        fi
        ;;
    *)
        echo "Usage: $0 {backup|restore <path>|cleanup|verify <path>}"
        exit 1
        ;;
esac
