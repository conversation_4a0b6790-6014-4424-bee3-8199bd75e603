@extends('layouts.app')

@section('title', 'Search Courses')

@section('content')
<div class="search-hero">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <h1 class="search-title">Find Your Perfect Course</h1>
                <p class="search-subtitle">Discover courses that match your interests and career goals</p>
                
                <form action="{{ route('courses.search') }}" method="GET" class="search-form">
                    <div class="search-input-group">
                        <div class="search-input-wrapper">
                            <i class="fas fa-search search-icon"></i>
                            <input type="text" name="q" class="search-input" 
                                   placeholder="Search for courses, topics, or mentors..." 
                                   value="{{ request('q') }}" autocomplete="off">
                            <button type="submit" class="search-btn">
                                <i class="fas fa-arrow-right"></i>
                            </button>
                        </div>
                        <div class="search-suggestions" id="searchSuggestions"></div>
                    </div>
                </form>
                
                @if(request('q'))
                <div class="search-info">
                    <p>Showing results for "<strong>{{ request('q') }}</strong>"</p>
                    @if($courses->total() > 0)
                    <span class="results-count">{{ $courses->total() }} {{ Str::plural('course', $courses->total()) }} found</span>
                    @endif
                </div>
                @endif
            </div>
        </div>
    </div>
</div>

<div class="search-content">
    <div class="container">
        <div class="row">
            <!-- Filters Sidebar -->
            <div class="col-lg-3">
                <div class="filters-sidebar">
                    <div class="filters-header">
                        <h4>Filter Results</h4>
                        <button class="btn btn-sm btn-outline-secondary" onclick="clearFilters()">
                            <i class="fas fa-times"></i> Clear All
                        </button>
                    </div>
                    
                    <form id="filtersForm" method="GET">
                        <input type="hidden" name="q" value="{{ request('q') }}">
                        
                        <!-- Categories -->
                        <div class="filter-group">
                            <h5 class="filter-title">Categories</h5>
                            <div class="filter-options">
                                @foreach($categories as $category)
                                <label class="filter-option">
                                    <input type="checkbox" name="categories[]" value="{{ $category->id }}" 
                                           {{ in_array($category->id, request('categories', [])) ? 'checked' : '' }}>
                                    <span class="checkmark"></span>
                                    <span class="option-text">{{ $category->name }}</span>
                                    <span class="option-count">({{ $category->courses_count }})</span>
                                </label>
                                @endforeach
                            </div>
                        </div>
                        
                        <!-- Difficulty Level -->
                        <div class="filter-group">
                            <h5 class="filter-title">Difficulty Level</h5>
                            <div class="filter-options">
                                @foreach(['beginner', 'intermediate', 'advanced'] as $level)
                                <label class="filter-option">
                                    <input type="checkbox" name="levels[]" value="{{ $level }}" 
                                           {{ in_array($level, request('levels', [])) ? 'checked' : '' }}>
                                    <span class="checkmark"></span>
                                    <span class="option-text">{{ ucfirst($level) }}</span>
                                </label>
                                @endforeach
                            </div>
                        </div>
                        
                        <!-- Duration -->
                        <div class="filter-group">
                            <h5 class="filter-title">Duration</h5>
                            <div class="filter-options">
                                <label class="filter-option">
                                    <input type="checkbox" name="duration[]" value="0-2" 
                                           {{ in_array('0-2', request('duration', [])) ? 'checked' : '' }}>
                                    <span class="checkmark"></span>
                                    <span class="option-text">0-2 hours</span>
                                </label>
                                <label class="filter-option">
                                    <input type="checkbox" name="duration[]" value="2-5" 
                                           {{ in_array('2-5', request('duration', [])) ? 'checked' : '' }}>
                                    <span class="checkmark"></span>
                                    <span class="option-text">2-5 hours</span>
                                </label>
                                <label class="filter-option">
                                    <input type="checkbox" name="duration[]" value="5-10" 
                                           {{ in_array('5-10', request('duration', [])) ? 'checked' : '' }}>
                                    <span class="checkmark"></span>
                                    <span class="option-text">5-10 hours</span>
                                </label>
                                <label class="filter-option">
                                    <input type="checkbox" name="duration[]" value="10+" 
                                           {{ in_array('10+', request('duration', [])) ? 'checked' : '' }}>
                                    <span class="checkmark"></span>
                                    <span class="option-text">10+ hours</span>
                                </label>
                            </div>
                        </div>
                        
                        <!-- Price -->
                        <div class="filter-group">
                            <h5 class="filter-title">Price</h5>
                            <div class="filter-options">
                                <label class="filter-option">
                                    <input type="radio" name="price" value="free" 
                                           {{ request('price') === 'free' ? 'checked' : '' }}>
                                    <span class="checkmark"></span>
                                    <span class="option-text">Free</span>
                                </label>
                                <label class="filter-option">
                                    <input type="radio" name="price" value="premium" 
                                           {{ request('price') === 'premium' ? 'checked' : '' }}>
                                    <span class="checkmark"></span>
                                    <span class="option-text">Premium Only</span>
                                </label>
                                <label class="filter-option">
                                    <input type="radio" name="price" value="all" 
                                           {{ request('price', 'all') === 'all' ? 'checked' : '' }}>
                                    <span class="checkmark"></span>
                                    <span class="option-text">All Courses</span>
                                </label>
                            </div>
                        </div>
                        
                        <!-- Rating -->
                        <div class="filter-group">
                            <h5 class="filter-title">Rating</h5>
                            <div class="filter-options">
                                @for($i = 5; $i >= 3; $i--)
                                <label class="filter-option">
                                    <input type="radio" name="rating" value="{{ $i }}" 
                                           {{ request('rating') == $i ? 'checked' : '' }}>
                                    <span class="checkmark"></span>
                                    <span class="option-text">
                                        @for($j = 1; $j <= 5; $j++)
                                            <i class="fas fa-star {{ $j <= $i ? 'text-warning' : 'text-muted' }}"></i>
                                        @endfor
                                        & up
                                    </span>
                                </label>
                                @endfor
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary btn-block">
                            <i class="fas fa-filter"></i> Apply Filters
                        </button>
                    </form>
                </div>
            </div>
            
            <!-- Results -->
            <div class="col-lg-9">
                <div class="search-results">
                    <!-- Sort Options -->
                    <div class="results-header">
                        <div class="results-info">
                            @if($courses->total() > 0)
                            <span>{{ $courses->firstItem() }}-{{ $courses->lastItem() }} of {{ $courses->total() }} courses</span>
                            @endif
                        </div>
                        <div class="sort-options">
                            <select name="sort" class="form-control form-control-sm" onchange="updateSort(this.value)">
                                <option value="relevance" {{ request('sort', 'relevance') === 'relevance' ? 'selected' : '' }}>Most Relevant</option>
                                <option value="newest" {{ request('sort') === 'newest' ? 'selected' : '' }}>Newest First</option>
                                <option value="popular" {{ request('sort') === 'popular' ? 'selected' : '' }}>Most Popular</option>
                                <option value="rating" {{ request('sort') === 'rating' ? 'selected' : '' }}>Highest Rated</option>
                                <option value="title" {{ request('sort') === 'title' ? 'selected' : '' }}>A-Z</option>
                            </select>
                        </div>
                    </div>
                    
                    @if($courses->count() > 0)
                    <!-- Course Grid -->
                    <div class="courses-grid">
                        @foreach($courses as $course)
                        <div class="course-card">
                            <div class="course-image">
                                <img src="{{ $course->thumbnail ? asset('storage/' . $course->thumbnail) : asset('images/default-course-thumbnail.jpg') }}" 
                                     alt="{{ $course->title }}">
                                <div class="course-overlay">
                                    <a href="{{ route('courses.show', $course) }}" class="btn btn-primary btn-sm">
                                        <i class="fas fa-eye"></i> View Course
                                    </a>
                                </div>
                                @if($course->is_free)
                                <div class="course-badge free">Free</div>
                                @elseif($course->is_featured)
                                <div class="course-badge featured">Featured</div>
                                @endif
                            </div>
                            
                            <div class="course-content">
                                <div class="course-meta">
                                    <span class="course-category">{{ $course->category->name }}</span>
                                    <span class="course-level">{{ ucfirst($course->difficulty_level) }}</span>
                                </div>
                                
                                <h3 class="course-title">
                                    <a href="{{ route('courses.show', $course) }}">{{ $course->title }}</a>
                                </h3>
                                
                                <p class="course-description">{{ Str::limit($course->description, 100) }}</p>
                                
                                <div class="course-mentor">
                                    <img src="{{ $course->mentor->avatar ? asset('storage/' . $course->mentor->avatar) : asset('images/default-avatar.png') }}" 
                                         alt="{{ $course->mentor->name }}" class="mentor-avatar">
                                    <span class="mentor-name">{{ $course->mentor->name }}</span>
                                </div>
                                
                                <div class="course-stats">
                                    <div class="stat-item">
                                        <i class="fas fa-star text-warning"></i>
                                        <span>{{ number_format($course->average_rating, 1) }}</span>
                                        <small>({{ $course->ratings_count }})</small>
                                    </div>
                                    <div class="stat-item">
                                        <i class="fas fa-users"></i>
                                        <span>{{ $course->enrollments_count }} students</span>
                                    </div>
                                    <div class="stat-item">
                                        <i class="fas fa-clock"></i>
                                        <span>{{ $course->total_duration }} hours</span>
                                    </div>
                                </div>
                                
                                <div class="course-actions">
                                    @auth
                                        @if(auth()->user()->isEnrolledIn($course))
                                            <a href="{{ route('courses.show', $course) }}" class="btn btn-success btn-sm">
                                                <i class="fas fa-play"></i> Continue
                                            </a>
                                        @else
                                            <a href="{{ route('courses.show', $course) }}" class="btn btn-primary btn-sm">
                                                <i class="fas fa-plus"></i> Enroll Now
                                            </a>
                                        @endif
                                    @else
                                        <a href="{{ route('login') }}" class="btn btn-primary btn-sm">
                                            <i class="fas fa-sign-in-alt"></i> Login to Enroll
                                        </a>
                                    @endauth
                                    
                                    <button class="btn btn-outline-secondary btn-sm" onclick="toggleWishlist({{ $course->id }})">
                                        <i class="fas fa-heart"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                    
                    <!-- Pagination -->
                    <div class="pagination-wrapper">
                        {{ $courses->appends(request()->query())->links() }}
                    </div>
                    
                    @else
                    <!-- No Results -->
                    <div class="no-results">
                        <div class="no-results-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        <h3>No courses found</h3>
                        <p>We couldn't find any courses matching your search criteria.</p>
                        <div class="no-results-suggestions">
                            <h5>Try:</h5>
                            <ul>
                                <li>Using different keywords</li>
                                <li>Removing some filters</li>
                                <li>Checking your spelling</li>
                                <li>Browsing all courses</li>
                            </ul>
                        </div>
                        <a href="{{ route('courses.index') }}" class="btn btn-primary">
                            <i class="fas fa-book"></i> Browse All Courses
                        </a>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.search-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4rem 0 2rem;
}

.search-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-align: center;
}

.search-subtitle {
    font-size: 1.25rem;
    opacity: 0.9;
    margin-bottom: 2rem;
    text-align: center;
}

.search-form {
    margin-bottom: 2rem;
}

.search-input-group {
    position: relative;
    max-width: 600px;
    margin: 0 auto;
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background: white;
    border-radius: 50px;
    padding: 0.5rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.search-icon {
    position: absolute;
    left: 1.5rem;
    color: #718096;
    font-size: 1.25rem;
}

.search-input {
    flex: 1;
    border: none;
    outline: none;
    padding: 1rem 1rem 1rem 3rem;
    font-size: 1.1rem;
    background: transparent;
    color: #2d3748;
}

.search-input::placeholder {
    color: #a0aec0;
}

.search-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.search-btn:hover {
    transform: scale(1.05);
}

.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    display: none;
}

.search-info {
    text-align: center;
    color: rgba(255, 255, 255, 0.9);
}

.search-info strong {
    color: white;
}

.results-count {
    display: block;
    font-size: 0.875rem;
    margin-top: 0.5rem;
}

.search-content {
    padding: 2rem 0;
    background: #f8f9fc;
    min-height: 60vh;
}

.filters-sidebar {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    position: sticky;
    top: 2rem;
}

.filters-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e2e8f0;
}

.filters-header h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2d3748;
    margin: 0;
}

.filter-group {
    margin-bottom: 2rem;
}

.filter-title {
    font-size: 1rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 1rem;
}

.filter-options {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.filter-option {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 0.875rem;
    color: #4a5568;
    position: relative;
    padding-left: 2rem;
}

.filter-option input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
}

.checkmark {
    position: absolute;
    left: 0;
    height: 18px;
    width: 18px;
    background-color: #e2e8f0;
    border-radius: 3px;
    transition: all 0.3s ease;
}

.filter-option:hover input ~ .checkmark {
    background-color: #cbd5e0;
}

.filter-option input:checked ~ .checkmark {
    background-color: #667eea;
}

.checkmark:after {
    content: "";
    position: absolute;
    display: none;
}

.filter-option input:checked ~ .checkmark:after {
    display: block;
}

.filter-option .checkmark:after {
    left: 6px;
    top: 3px;
    width: 5px;
    height: 10px;
    border: solid white;
    border-width: 0 3px 3px 0;
    transform: rotate(45deg);
}

.option-count {
    margin-left: auto;
    color: #a0aec0;
    font-size: 0.75rem;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e2e8f0;
}

.results-info {
    color: #718096;
    font-size: 0.875rem;
}

.sort-options select {
    min-width: 150px;
}

.courses-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.course-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.course-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.course-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.course-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.course-card:hover .course-image img {
    transform: scale(1.05);
}

.course-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.course-card:hover .course-overlay {
    opacity: 1;
}

.course-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.course-badge.free {
    background: #48bb78;
    color: white;
}

.course-badge.featured {
    background: #ed8936;
    color: white;
}

.course-content {
    padding: 1.5rem;
}

.course-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.course-category {
    background: #edf2f7;
    color: #4a5568;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 500;
}

.course-level {
    color: #718096;
    font-size: 0.75rem;
    text-transform: uppercase;
    font-weight: 600;
}

.course-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    line-height: 1.4;
}

.course-title a {
    color: #2d3748;
    text-decoration: none;
}

.course-title a:hover {
    color: #667eea;
    text-decoration: none;
}

.course-description {
    color: #718096;
    font-size: 0.875rem;
    line-height: 1.5;
    margin-bottom: 1rem;
}

.course-mentor {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.mentor-avatar {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 0.5rem;
}

.mentor-name {
    color: #4a5568;
    font-size: 0.875rem;
    font-weight: 500;
}

.course-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
    font-size: 0.75rem;
    color: #718096;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.course-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.no-results {
    text-align: center;
    padding: 4rem 2rem;
}

.no-results-icon {
    font-size: 4rem;
    color: #cbd5e0;
    margin-bottom: 2rem;
}

.no-results h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 1rem;
}

.no-results p {
    color: #718096;
    font-size: 1.1rem;
    margin-bottom: 2rem;
}

.no-results-suggestions {
    background: #f7fafc;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    text-align: left;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

.no-results-suggestions h5 {
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 1rem;
}

.no-results-suggestions ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.no-results-suggestions li {
    color: #718096;
    margin-bottom: 0.5rem;
    position: relative;
    padding-left: 1.5rem;
}

.no-results-suggestions li:before {
    content: "•";
    color: #667eea;
    font-weight: bold;
    position: absolute;
    left: 0;
}

@media (max-width: 768px) {
    .search-title {
        font-size: 2rem;
    }
    
    .filters-sidebar {
        margin-bottom: 2rem;
    }
    
    .results-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .courses-grid {
        grid-template-columns: 1fr;
    }
    
    .course-stats {
        flex-direction: column;
        gap: 0.5rem;
        align-items: flex-start;
    }
}
</style>
@endpush

@push('scripts')
<script>
function updateSort(value) {
    const url = new URL(window.location);
    url.searchParams.set('sort', value);
    window.location.href = url.toString();
}

function clearFilters() {
    const url = new URL(window.location);
    const q = url.searchParams.get('q');
    url.search = '';
    if (q) {
        url.searchParams.set('q', q);
    }
    window.location.href = url.toString();
}

function toggleWishlist(courseId) {
    // Wishlist functionality
    fetch(`/courses/${courseId}/favorite`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update heart icon
            const button = event.target.closest('button');
            const icon = button.querySelector('i');
            if (data.favorited) {
                icon.style.color = '#e53e3e';
            } else {
                icon.style.color = '';
            }
        }
    });
}

// Auto-submit filters on change
document.getElementById('filtersForm').addEventListener('change', function() {
    this.submit();
});

// Search suggestions
let searchTimeout;
document.querySelector('.search-input').addEventListener('input', function() {
    clearTimeout(searchTimeout);
    const query = this.value;
    
    if (query.length >= 2) {
        searchTimeout = setTimeout(() => {
            fetch(`/ajax/search/suggestions?q=${encodeURIComponent(query)}`)
                .then(response => response.json())
                .then(data => {
                    const suggestions = document.getElementById('searchSuggestions');
                    if (data.length > 0) {
                        suggestions.innerHTML = data.map(item => 
                            `<div class="suggestion-item" onclick="selectSuggestion('${item}')">${item}</div>`
                        ).join('');
                        suggestions.style.display = 'block';
                    } else {
                        suggestions.style.display = 'none';
                    }
                });
        }, 300);
    } else {
        document.getElementById('searchSuggestions').style.display = 'none';
    }
});

function selectSuggestion(suggestion) {
    document.querySelector('.search-input').value = suggestion;
    document.getElementById('searchSuggestions').style.display = 'none';
    document.querySelector('.search-form').submit();
}

// Hide suggestions when clicking outside
document.addEventListener('click', function(e) {
    if (!e.target.closest('.search-input-group')) {
        document.getElementById('searchSuggestions').style.display = 'none';
    }
});
</script>
@endpush
