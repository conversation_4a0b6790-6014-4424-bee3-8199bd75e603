<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Course;
use App\Models\UserProgress;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ProgressController extends Controller
{
    /**
     * Get user's overall progress.
     */
    public function index(Request $request)
    {
        $user = Auth::user();

        // Get all user progress grouped by course
        $progressData = UserProgress::where('user_id', $user->id)
            ->with(['course', 'lesson'])
            ->get()
            ->groupBy('course_id');

        $courses = [];
        foreach ($progressData as $courseId => $progress) {
            $course = $progress->first()->course;
            $totalLessons = $course->lessons()->where('is_published', true)->count();
            $completedLessons = $progress->where('is_completed', true)->count();
            $progressPercentage = $totalLessons > 0 ? ($completedLessons / $totalLessons) * 100 : 0;

            $courses[] = [
                'course' => $course,
                'total_lessons' => $totalLessons,
                'completed_lessons' => $completedLessons,
                'progress_percentage' => round($progressPercentage, 2),
                'last_accessed' => $progress->max('last_accessed_at'),
                'lessons' => $progress->map(function ($p) {
                    return [
                        'lesson' => $p->lesson,
                        'is_completed' => $p->is_completed,
                        'completion_percentage' => $p->completion_percentage,
                        'watch_time_seconds' => $p->watch_time_seconds,
                        'last_accessed_at' => $p->last_accessed_at,
                    ];
                }),
            ];
        }

        // Overall statistics
        $totalCourses = $user->userProgress()->distinct('course_id')->count('course_id');
        $completedCourses = collect($courses)->where('progress_percentage', 100)->count();
        $totalLessonsCompleted = $user->userProgress()->where('is_completed', true)->count();

        return response()->json([
            'success' => true,
            'data' => [
                'courses' => $courses,
                'statistics' => [
                    'total_courses_enrolled' => $totalCourses,
                    'completed_courses' => $completedCourses,
                    'total_lessons_completed' => $totalLessonsCompleted,
                    'certificates_earned' => $user->certificates()->count(),
                ],
            ],
        ]);
    }

    /**
     * Get progress for a specific course.
     */
    public function courseProgress(Course $course)
    {
        $user = Auth::user();

        $progress = UserProgress::where('user_id', $user->id)
            ->where('course_id', $course->id)
            ->with('lesson')
            ->get();

        $totalLessons = $course->lessons()->where('is_published', true)->count();
        $completedLessons = $progress->where('is_completed', true)->count();
        $progressPercentage = $totalLessons > 0 ? ($completedLessons / $totalLessons) * 100 : 0;

        $lessons = $course->lessons()
            ->where('is_published', true)
            ->orderBy('sort_order')
            ->get()
            ->map(function ($lesson) use ($progress) {
                $lessonProgress = $progress->where('lesson_id', $lesson->id)->first();

                return [
                    'lesson' => $lesson,
                    'progress' => $lessonProgress ? [
                        'is_completed' => $lessonProgress->is_completed,
                        'completion_percentage' => $lessonProgress->completion_percentage,
                        'watch_time_seconds' => $lessonProgress->watch_time_seconds,
                        'last_accessed_at' => $lessonProgress->last_accessed_at,
                    ] : null,
                ];
            });

        return response()->json([
            'success' => true,
            'data' => [
                'course' => $course,
                'total_lessons' => $totalLessons,
                'completed_lessons' => $completedLessons,
                'progress_percentage' => round($progressPercentage, 2),
                'lessons' => $lessons,
                'started_at' => $progress->min('created_at'),
                'last_accessed_at' => $progress->max('last_accessed_at'),
            ],
        ]);
    }
}
