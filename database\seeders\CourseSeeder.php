<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Course;
use App\Models\Lesson;
use Spatie\Permission\Models\Role;

class CourseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get mentors created by UserSeeder
        $mentors = User::whereHas('roles', function($q) {
            $q->where('name', 'mentor');
        })->get();

        if ($mentors->isEmpty()) {
            $this->command->error('No mentors found. Please run UserSeeder first.');
            return;
        }

        // Create courses
        $courses = [
            [
                'title' => 'Cryptocurrency Mastery',
                'description' => 'Learn how to trade and invest in cryptocurrency like a professional. Master the fundamentals of blockchain technology, technical analysis, and risk management.',
                'syllabus' => 'Module 1: Blockchain Fundamentals\nModule 2: Trading Strategies\nModule 3: Risk Management\nModule 4: Advanced Trading\nModule 5: Portfolio Management',
                'category' => 'Crypto',
                'difficulty' => 'intermediate',
                'duration_hours' => 12,
                'price' => 0,
                'required_plans' => ['challenger', 'hero', 'champion'],
                'is_published' => true,
                'is_featured' => true,
                'sort_order' => 1,
            ],
            [
                'title' => 'Copywriting Secrets',
                'description' => 'Discover the secrets of persuasive copywriting that converts readers into customers. Learn from proven frameworks and real-world examples.',
                'syllabus' => 'Module 1: Copywriting Fundamentals\nModule 2: Psychology of Persuasion\nModule 3: Sales Letters\nModule 4: Email Marketing\nModule 5: Social Media Copy',
                'category' => 'Copywriting',
                'difficulty' => 'beginner',
                'duration_hours' => 8,
                'price' => 0,
                'required_plans' => ['cadet', 'challenger', 'hero', 'champion'],
                'is_published' => true,
                'is_featured' => true,
                'sort_order' => 2,
            ],
            [
                'title' => 'E-commerce Empire',
                'description' => 'Build a profitable e-commerce business from scratch. Learn product selection, marketing strategies, and scaling techniques.',
                'syllabus' => 'Module 1: Market Research\nModule 2: Product Selection\nModule 3: Store Setup\nModule 4: Marketing & Ads\nModule 5: Scaling & Automation',
                'category' => 'E-commerce',
                'difficulty' => 'intermediate',
                'duration_hours' => 15,
                'price' => 0,
                'required_plans' => ['challenger', 'hero', 'champion'],
                'is_published' => true,
                'is_featured' => false,
                'sort_order' => 3,
            ],
            [
                'title' => 'Social Media Domination',
                'description' => 'Master social media marketing and build a massive following. Learn content creation, engagement strategies, and monetization.',
                'syllabus' => 'Module 1: Platform Selection\nModule 2: Content Strategy\nModule 3: Growth Hacking\nModule 4: Monetization\nModule 5: Personal Branding',
                'category' => 'Social Media',
                'difficulty' => 'beginner',
                'duration_hours' => 10,
                'price' => 0,
                'required_plans' => ['cadet', 'challenger', 'hero', 'champion'],
                'is_published' => true,
                'is_featured' => false,
                'sort_order' => 4,
            ],
            [
                'title' => 'Freelancing Freedom',
                'description' => 'Start and scale a successful freelancing business. Learn client acquisition, pricing strategies, and service delivery.',
                'syllabus' => 'Module 1: Skill Assessment\nModule 2: Client Acquisition\nModule 3: Pricing & Proposals\nModule 4: Service Delivery\nModule 5: Scaling Your Business',
                'category' => 'Freelancing',
                'difficulty' => 'beginner',
                'duration_hours' => 6,
                'price' => 0,
                'required_plans' => ['cadet', 'challenger', 'hero', 'champion'],
                'is_published' => true,
                'is_featured' => false,
                'sort_order' => 5,
            ],
            [
                'title' => 'Real Estate Riches',
                'description' => 'Learn real estate investment strategies that generate passive income. From rental properties to flipping houses.',
                'syllabus' => 'Module 1: Market Analysis\nModule 2: Financing Options\nModule 3: Property Selection\nModule 4: Renovation & Flipping\nModule 5: Rental Income',
                'category' => 'Real Estate',
                'difficulty' => 'advanced',
                'duration_hours' => 20,
                'price' => 0,
                'required_plans' => ['hero', 'champion'],
                'is_published' => true,
                'is_featured' => false,
                'sort_order' => 6,
            ],
        ];

        foreach ($courses as $courseData) {
            // Assign mentor based on category
            $mentor = $this->getMentorForCategory($mentors, $courseData['category']);

            $slug = \Str::slug($courseData['title']);
            $course = Course::firstOrCreate(
                ['slug' => $slug],
                array_merge($courseData, [
                    'mentor_id' => $mentor->id,
                    'published_at' => now(),
                    'slug' => $slug,
                ])
            );

            // Create lessons for each course if it's newly created
            if ($course->wasRecentlyCreated) {
                $this->createLessonsForCourse($course);
            }
        }

        $this->command->info('Courses seeded successfully!');
    }

    private function getMentorForCategory($mentors, $category)
    {
        // Map categories to specific mentors based on their specialties
        $categoryMentorMap = [
            'Crypto' => '<EMAIL>',
            'Copywriting' => '<EMAIL>',
            'E-commerce' => '<EMAIL>',
            'Social Media' => '<EMAIL>',
            'Freelancing' => '<EMAIL>',
            'Real Estate' => '<EMAIL>', // Andrew Tate
        ];

        $mentorEmail = $categoryMentorMap[$category] ?? null;

        if ($mentorEmail) {
            $mentor = $mentors->firstWhere('email', $mentorEmail);
            if ($mentor) {
                return $mentor;
            }
        }

        // Fallback to random mentor
        return $mentors->random();
    }

    private function createLessonsForCourse(Course $course)
    {
        $lessonTemplates = [
            [
                'title' => 'Introduction to {category}',
                'description' => 'Get started with the fundamentals of {category} and understand what you\'ll learn in this course.',
                'content' => 'Welcome to the {category} course! In this lesson, we\'ll cover the basics and set you up for success.',
                'duration_minutes' => 15,
                'is_free' => true,
            ],
            [
                'title' => 'Core Concepts and Principles',
                'description' => 'Deep dive into the core concepts that form the foundation of {category}.',
                'content' => 'Understanding the fundamental principles is crucial for success in {category}.',
                'duration_minutes' => 25,
                'is_free' => false,
            ],
            [
                'title' => 'Practical Applications',
                'description' => 'Learn how to apply {category} concepts in real-world scenarios.',
                'content' => 'Now that you understand the theory, let\'s put it into practice.',
                'duration_minutes' => 30,
                'is_free' => false,
            ],
            [
                'title' => 'Advanced Strategies',
                'description' => 'Master advanced {category} strategies used by professionals.',
                'content' => 'These advanced strategies will set you apart from the competition.',
                'duration_minutes' => 35,
                'is_free' => false,
            ],
            [
                'title' => 'Case Studies and Examples',
                'description' => 'Analyze real-world case studies and successful {category} examples.',
                'content' => 'Learn from real examples and case studies of successful implementations.',
                'duration_minutes' => 20,
                'is_free' => false,
            ],
        ];

        foreach ($lessonTemplates as $index => $template) {
            Lesson::create([
                'course_id' => $course->id,
                'title' => str_replace('{category}', $course->category, $template['title']),
                'description' => str_replace('{category}', $course->category, $template['description']),
                'content' => str_replace('{category}', $course->category, $template['content']),
                'video_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', // Sample video URL
                'video_provider' => 'youtube',
                'duration_minutes' => $template['duration_minutes'],
                'resources' => [
                    [
                        'name' => 'Course Notes PDF',
                        'type' => 'pdf',
                        'url' => '#',
                    ],
                    [
                        'name' => 'Worksheet',
                        'type' => 'pdf',
                        'url' => '#',
                    ],
                ],
                'sort_order' => $index + 1,
                'is_published' => true,
                'is_free' => $template['is_free'],
            ]);
        }
    }
}
