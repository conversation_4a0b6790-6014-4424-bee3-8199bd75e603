<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Course;
use App\Models\CourseRating;
use App\Models\UserProgress;
use Illuminate\Database\Seeder;

class CourseRatingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get users who have completed courses (based on lesson completion)
        $completedProgress = UserProgress::where('is_completed', true)
            ->with(['user', 'course'])
            ->get()
            ->groupBy(['user_id', 'course_id'])
            ->map(function($userCourseProgress) {
                return $userCourseProgress->first(); // Take first completed lesson as representative
            })
            ->flatten();

        $reviews = [
            [
                'rating' => 5,
                'reviews' => [
                    'This course completely changed my life! The strategies are practical and actually work.',
                    'Amazing content and excellent delivery. Highly recommended!',
                    'Best investment I\'ve ever made. Already seeing results!',
                    'The Real World delivers on its promises. This course is gold.',
                    'Incredible value and life-changing information. 5 stars!',
                ]
            ],
            [
                'rating' => 4,
                'reviews' => [
                    'Great course with solid content. Could use more examples.',
                    'Very informative and well-structured. Worth the investment.',
                    'Good practical advice, though some parts could be more detailed.',
                    'Solid course that delivers what it promises. Recommended.',
                    'Quality content with actionable strategies. Very helpful.',
                ]
            ],
            [
                'rating' => 3,
                'reviews' => [
                    'Decent course but nothing groundbreaking. Average content.',
                    'Some good points but could be more comprehensive.',
                    'It\'s okay, but I expected more for the price.',
                    'Mixed feelings - some parts are great, others not so much.',
                ]
            ],
        ];

        foreach ($completedProgress as $progress) {
            // 70% chance of leaving a review
            if (rand(1, 100) <= 70) {
                // Weight ratings towards higher scores (realistic for The Real World)
                $ratingWeights = [5 => 60, 4 => 30, 3 => 10]; // 60% 5-star, 30% 4-star, 10% 3-star
                $rating = $this->getWeightedRating($ratingWeights);

                $reviewData = $reviews[$rating - 3]; // Adjust index (3=0, 4=1, 5=2)
                $reviewText = $reviewData['reviews'][array_rand($reviewData['reviews'])];

                CourseRating::firstOrCreate(
                    [
                        'user_id' => $progress->user_id,
                        'course_id' => $progress->course_id,
                    ],
                    [
                        'rating' => $rating,
                        'review' => $reviewText,
                        'is_verified' => true, // They completed the course
                        'created_at' => $progress->completed_at->addDays(rand(1, 7)),
                        'updated_at' => $progress->completed_at->addDays(rand(1, 7)),
                    ]
                );
            }
        }

        // Add some additional ratings from users who haven't completed courses (lower ratings)
        $activeUsers = User::whereHas('userSubscriptions', function($q) {
            $q->where('status', 'active');
        })->whereHas('roles', function($q) {
            $q->where('name', 'user');
        })->get();

        $courses = Course::all();

        foreach ($activeUsers->random(20) as $user) {
            $course = $courses->random();

            $rating = rand(3, 5); // Only positive ratings for active users
            $reviewData = $reviews[$rating - 3];
            $reviewText = $reviewData['reviews'][array_rand($reviewData['reviews'])];

            CourseRating::firstOrCreate(
                [
                    'user_id' => $user->id,
                    'course_id' => $course->id,
                ],
                [
                    'rating' => $rating,
                    'review' => $reviewText,
                    'is_verified' => false, // They haven't completed the course
                    'created_at' => now()->subDays(rand(1, 30)),
                    'updated_at' => now()->subDays(rand(1, 30)),
                ]
            );
        }

        $this->command->info('Course ratings seeded successfully!');
    }

    private function getWeightedRating($weights)
    {
        $rand = rand(1, 100);
        $cumulative = 0;

        foreach ($weights as $rating => $weight) {
            $cumulative += $weight;
            if ($rand <= $cumulative) {
                return $rating;
            }
        }

        return 5; // Default to 5 if something goes wrong
    }
}
