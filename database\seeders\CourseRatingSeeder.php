<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Course;
use App\Models\User;
use App\Models\Review;

class CourseRatingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $courses = Course::all();
        $students = User::where('role', 'student')->get();

        if ($courses->isEmpty() || $students->isEmpty()) {
            $this->command->info('No courses or students found. Skipping course ratings.');
            return;
        }

        $reviews = [
            [
                'rating' => 5,
                'comment' => 'Absolutely amazing course! Changed my perspective completely. The mentor is incredibly knowledgeable and explains everything clearly.',
            ],
            [
                'rating' => 5,
                'comment' => 'Best investment I\'ve made in my education. The practical examples and real-world applications are invaluable.',
            ],
            [
                'rating' => 4,
                'comment' => 'Great content and well-structured lessons. Would recommend to anyone serious about learning.',
            ],
            [
                'rating' => 5,
                'comment' => 'This course exceeded my expectations. The mentor\'s expertise really shows through every lesson.',
            ],
            [
                'rating' => 4,
                'comment' => 'Very informative and practical. The step-by-step approach makes complex topics easy to understand.',
            ],
            [
                'rating' => 5,
                'comment' => 'Outstanding course! The quality of content and delivery is top-notch. Highly recommended!',
            ],
            [
                'rating' => 4,
                'comment' => 'Solid course with good examples. The mentor knows what they\'re talking about.',
            ],
            [
                'rating' => 5,
                'comment' => 'Life-changing content! This course has given me the tools and mindset I needed to succeed.',
            ],
            [
                'rating' => 4,
                'comment' => 'Well worth the investment. The lessons are engaging and the mentor is very responsive.',
            ],
            [
                'rating' => 5,
                'comment' => 'Incredible value! This course has everything you need to get started and succeed.',
            ],
            [
                'rating' => 3,
                'comment' => 'Good course overall, but could use more advanced topics. Still learned a lot though.',
            ],
            [
                'rating' => 4,
                'comment' => 'The mentor\'s experience really shows. Practical advice that you can implement immediately.',
            ],
            [
                'rating' => 5,
                'comment' => 'Perfect course for beginners and intermediate learners. Clear explanations and great examples.',
            ],
            [
                'rating' => 4,
                'comment' => 'High-quality content with real-world applications. The mentor is very knowledgeable.',
            ],
            [
                'rating' => 5,
                'comment' => 'This course is a game-changer! The strategies and techniques taught here actually work.',
            ],
        ];

        foreach ($courses as $course) {
            // Create 3-8 reviews per course
            $reviewCount = rand(3, 8);
            $usedStudents = [];

            for ($i = 0; $i < $reviewCount; $i++) {
                // Get a random student who hasn't reviewed this course yet
                $availableStudents = $students->whereNotIn('id', $usedStudents);
                
                if ($availableStudents->isEmpty()) {
                    break; // No more students available
                }

                $student = $availableStudents->random();
                $usedStudents[] = $student->id;

                $reviewData = $reviews[array_rand($reviews)];

                Review::create([
                    'course_id' => $course->id,
                    'user_id' => $student->id,
                    'rating' => $reviewData['rating'],
                    'comment' => $reviewData['comment'],
                    'is_approved' => true,
                    'created_at' => now()->subDays(rand(1, 30)),
                    'updated_at' => now()->subDays(rand(1, 30)),
                ]);
            }

            // Update course average rating
            $averageRating = $course->reviews()->avg('rating');
            $course->update(['average_rating' => round($averageRating, 1)]);
        }

        $totalReviews = Review::count();
        $this->command->info("Course ratings seeded successfully! Created {$totalReviews} reviews.");
    }
}
