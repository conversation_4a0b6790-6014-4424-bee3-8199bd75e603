@extends('layouts.dashboard')

@section('title', 'My Profile')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">My Profile</h1>
            <p class="mb-0 text-muted">Manage your account settings and preferences</p>
        </div>
        <div>
            <a href="{{ route('profile.edit') }}" class="btn btn-primary">
                <i class="fas fa-edit"></i> Edit Profile
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Profile Information -->
        <div class="col-xl-4 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-body text-center">
                    <img class="rounded-circle mb-3" 
                         src="{{ auth()->user()->avatar ? asset('storage/' . auth()->user()->avatar) : asset('images/default-avatar.png') }}" 
                         alt="{{ auth()->user()->name }}" width="120" height="120">
                    
                    <h4 class="mb-1">{{ auth()->user()->name }}</h4>
                    <p class="text-muted mb-3">{{ auth()->user()->email }}</p>
                    
                    <div class="mb-3">
                        @foreach(auth()->user()->roles as $role)
                            <span class="badge badge-{{ $role->name == 'admin' ? 'danger' : ($role->name == 'mentor' ? 'info' : 'primary') }} mr-1">
                                {{ ucfirst($role->name) }}
                            </span>
                        @endforeach
                    </div>

                    @if(auth()->user()->bio)
                    <div class="text-left">
                        <h6 class="font-weight-bold">About Me</h6>
                        <p class="text-muted">{{ auth()->user()->bio }}</p>
                    </div>
                    @endif

                    <div class="row text-center mt-4">
                        <div class="col-4">
                            <div class="font-weight-bold text-primary">{{ auth()->user()->userProgress->where('is_completed', true)->count() }}</div>
                            <div class="text-muted small">Completed</div>
                        </div>
                        <div class="col-4">
                            <div class="font-weight-bold text-success">{{ auth()->user()->certificates->count() }}</div>
                            <div class="text-muted small">Certificates</div>
                        </div>
                        <div class="col-4">
                            <div class="font-weight-bold text-info">{{ auth()->user()->courseRatings->count() }}</div>
                            <div class="text-muted small">Reviews</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Contact Information</h6>
                </div>
                <div class="card-body">
                    <div class="row mb-2">
                        <div class="col-4 font-weight-bold">Email:</div>
                        <div class="col-8">
                            {{ auth()->user()->email }}
                            @if(auth()->user()->email_verified_at)
                                <i class="fas fa-check-circle text-success ml-1" title="Verified"></i>
                            @else
                                <i class="fas fa-exclamation-circle text-warning ml-1" title="Unverified"></i>
                            @endif
                        </div>
                    </div>
                    @if(auth()->user()->phone)
                    <div class="row mb-2">
                        <div class="col-4 font-weight-bold">Phone:</div>
                        <div class="col-8">{{ auth()->user()->phone }}</div>
                    </div>
                    @endif
                    @if(auth()->user()->country)
                    <div class="row mb-2">
                        <div class="col-4 font-weight-bold">Country:</div>
                        <div class="col-8">{{ auth()->user()->country }}</div>
                    </div>
                    @endif
                    <div class="row mb-2">
                        <div class="col-4 font-weight-bold">Joined:</div>
                        <div class="col-8">{{ auth()->user()->created_at->format('M d, Y') }}</div>
                    </div>
                </div>
            </div>

            <!-- Security Settings -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Security</h6>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <h6 class="mb-1">Two-Factor Authentication</h6>
                            <small class="text-muted">Add an extra layer of security</small>
                        </div>
                        <div>
                            @if(auth()->user()->two_factor_enabled)
                                <span class="badge badge-success">Enabled</span>
                            @else
                                <span class="badge badge-secondary">Disabled</span>
                            @endif
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">Password</h6>
                            <small class="text-muted">Last changed {{ auth()->user()->password_changed_at ? auth()->user()->password_changed_at->diffForHumans() : 'never' }}</small>
                        </div>
                        <div>
                            <a href="{{ route('profile.password') }}" class="btn btn-sm btn-outline-primary">Change</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-xl-8 col-lg-7">
            <!-- Subscription Information -->
            @if(auth()->user()->activeSubscription)
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Current Subscription</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5 class="font-weight-bold">{{ auth()->user()->activeSubscription->subscriptionPlan->name }}</h5>
                            <p class="text-muted">{{ auth()->user()->activeSubscription->billing_cycle }} billing</p>
                        </div>
                        <div class="col-md-6 text-md-right">
                            <h5 class="font-weight-bold text-success">${{ number_format(auth()->user()->activeSubscription->amount, 2) }}</h5>
                            <p class="text-muted">Next billing: {{ auth()->user()->activeSubscription->current_period_end->format('M d, Y') }}</p>
                        </div>
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-md-12">
                            <h6 class="font-weight-bold mb-2">Plan Features:</h6>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-{{ auth()->user()->activeSubscription->subscriptionPlan->live_calls_access ? 'check text-success' : 'times text-danger' }} mr-2"></i>
                                        <span>Live Calls</span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-{{ auth()->user()->activeSubscription->subscriptionPlan->community_access ? 'check text-success' : 'times text-danger' }} mr-2"></i>
                                        <span>Community Access</span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-{{ auth()->user()->activeSubscription->subscriptionPlan->mentor_access ? 'check text-success' : 'times text-danger' }} mr-2"></i>
                                        <span>Mentor Access</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-3">
                        <a href="{{ route('subscriptions.index') }}" class="btn btn-primary">
                            <i class="fas fa-cog"></i> Manage Subscription
                        </a>
                    </div>
                </div>
            </div>
            @else
            <div class="card shadow mb-4">
                <div class="card-body text-center py-5">
                    <i class="fas fa-crown fa-3x text-muted mb-3"></i>
                    <h5>No Active Subscription</h5>
                    <p class="text-muted">Subscribe to access all courses and features</p>
                    <a href="{{ route('pricing') }}" class="btn btn-primary">
                        <i class="fas fa-star"></i> View Plans
                    </a>
                </div>
            </div>
            @endif

            <!-- Learning Progress -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">Learning Progress</h6>
                    <a href="{{ route('dashboard.courses') }}" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                <div class="card-body">
                    @if(auth()->user()->userProgress->isNotEmpty())
                        @foreach(auth()->user()->userProgress->groupBy('course_id')->take(5) as $courseId => $progress)
                            @php
                                $course = $progress->first()->course;
                                $totalLessons = $course->lessons->count();
                                $completedLessons = $progress->where('is_completed', true)->count();
                                $progressPercentage = $totalLessons > 0 ? ($completedLessons / $totalLessons) * 100 : 0;
                            @endphp
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">{{ $course->title }}</h6>
                                    <div class="progress" style="height: 8px;">
                                        <div class="progress-bar" role="progressbar" 
                                             style="width: {{ $progressPercentage }}%"
                                             aria-valuenow="{{ $progressPercentage }}" 
                                             aria-valuemin="0" aria-valuemax="100">
                                        </div>
                                    </div>
                                    <small class="text-muted">{{ $completedLessons }}/{{ $totalLessons }} lessons completed</small>
                                </div>
                                <div class="ml-3">
                                    <span class="font-weight-bold">{{ round($progressPercentage) }}%</span>
                                </div>
                            </div>
                        @endforeach
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-book fa-3x text-muted mb-3"></i>
                            <p class="text-muted">Start learning to see your progress here</p>
                            <a href="{{ route('courses.index') }}" class="btn btn-primary">
                                <i class="fas fa-play"></i> Browse Courses
                            </a>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Certificates -->
            @if(auth()->user()->certificates->isNotEmpty())
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">My Certificates</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        @foreach(auth()->user()->certificates->take(6) as $certificate)
                        <div class="col-md-6 mb-3">
                            <div class="card border-left-success">
                                <div class="card-body py-3">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-certificate text-success fa-2x mr-3"></i>
                                        <div>
                                            <h6 class="mb-1">{{ $certificate->course->title }}</h6>
                                            <small class="text-muted">{{ $certificate->issued_at->format('M d, Y') }}</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                    @if(auth()->user()->certificates->count() > 6)
                    <div class="text-center">
                        <a href="{{ route('profile.certificates') }}" class="btn btn-outline-primary">
                            View All Certificates
                        </a>
                    </div>
                    @endif
                </div>
            </div>
            @endif
        </div>
    </div>
</div>
@endsection
