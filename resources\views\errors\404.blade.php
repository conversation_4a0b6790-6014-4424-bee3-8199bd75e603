@extends('layouts.app')

@section('title', 'Page Not Found')

@section('content')
<div class="error-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 text-center">
                <div class="error-content">
                    <!-- Error Animation -->
                    <div class="error-animation">
                        <div class="error-number">
                            <span class="four">4</span>
                            <span class="zero">0</span>
                            <span class="four">4</span>
                        </div>
                        <div class="error-icon">
                            <i class="fas fa-search"></i>
                        </div>
                    </div>
                    
                    <!-- Error Message -->
                    <div class="error-message">
                        <h1 class="error-title">Oops! Page Not Found</h1>
                        <p class="error-description">
                            The page you're looking for doesn't exist or has been moved. 
                            Don't worry, it happens to the best of us!
                        </p>
                    </div>
                    
                    <!-- Suggestions -->
                    <div class="error-suggestions">
                        <h3>What can you do?</h3>
                        <div class="suggestions-grid">
                            <div class="suggestion-item">
                                <div class="suggestion-icon">
                                    <i class="fas fa-home"></i>
                                </div>
                                <h4>Go Home</h4>
                                <p>Return to our homepage and start fresh</p>
                                <a href="{{ route('home') }}" class="btn btn-primary">
                                    <i class="fas fa-home"></i> Homepage
                                </a>
                            </div>
                            
                            <div class="suggestion-item">
                                <div class="suggestion-icon">
                                    <i class="fas fa-search"></i>
                                </div>
                                <h4>Search</h4>
                                <p>Try searching for what you were looking for</p>
                                <form action="{{ route('courses.search') }}" method="GET" class="search-form">
                                    <div class="input-group">
                                        <input type="text" name="q" class="form-control" placeholder="Search courses...">
                                        <div class="input-group-append">
                                            <button class="btn btn-primary" type="submit">
                                                <i class="fas fa-search"></i>
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            
                            <div class="suggestion-item">
                                <div class="suggestion-icon">
                                    <i class="fas fa-book"></i>
                                </div>
                                <h4>Browse Courses</h4>
                                <p>Explore our extensive course library</p>
                                <a href="{{ route('courses.index') }}" class="btn btn-primary">
                                    <i class="fas fa-book"></i> View Courses
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Popular Links -->
                    <div class="popular-links">
                        <h4>Popular Pages</h4>
                        <div class="links-grid">
                            <a href="{{ route('courses.index') }}" class="popular-link">
                                <i class="fas fa-graduation-cap"></i>
                                <span>All Courses</span>
                            </a>
                            <a href="{{ route('subscriptions.plans') }}" class="popular-link">
                                <i class="fas fa-crown"></i>
                                <span>Pricing</span>
                            </a>
                            <a href="{{ route('community.index') }}" class="popular-link">
                                <i class="fas fa-users"></i>
                                <span>Community</span>
                            </a>
                            <a href="{{ route('live-calls.index') }}" class="popular-link">
                                <i class="fas fa-video"></i>
                                <span>Live Calls</span>
                            </a>
                            <a href="{{ route('about') }}" class="popular-link">
                                <i class="fas fa-info-circle"></i>
                                <span>About Us</span>
                            </a>
                            <a href="{{ route('contact') }}" class="popular-link">
                                <i class="fas fa-envelope"></i>
                                <span>Contact</span>
                            </a>
                        </div>
                    </div>
                    
                    <!-- Help Section -->
                    <div class="help-section">
                        <div class="help-card">
                            <div class="help-icon">
                                <i class="fas fa-question-circle"></i>
                            </div>
                            <div class="help-content">
                                <h4>Still Need Help?</h4>
                                <p>If you believe this is an error or need assistance, our support team is here to help.</p>
                                <div class="help-buttons">
                                    <a href="{{ route('contact') }}" class="btn btn-outline-primary">
                                        <i class="fas fa-envelope"></i> Contact Support
                                    </a>
                                    <a href="{{ route('faq') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-question"></i> View FAQ
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.error-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    padding: 2rem 0;
    color: white;
}

.error-content {
    padding: 2rem;
}

.error-animation {
    margin-bottom: 3rem;
    position: relative;
}

.error-number {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 8rem;
    font-weight: 900;
    line-height: 1;
    margin-bottom: 1rem;
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
}

.error-number span {
    display: inline-block;
    animation: bounce 2s infinite;
}

.error-number .zero {
    animation-delay: 0.1s;
    color: #ffd700;
}

.error-number .four:first-child {
    animation-delay: 0s;
}

.error-number .four:last-child {
    animation-delay: 0.2s;
}

.error-icon {
    font-size: 3rem;
    opacity: 0.8;
    animation: float 3s ease-in-out infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-20px);
    }
    60% {
        transform: translateY(-10px);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

.error-message {
    margin-bottom: 3rem;
}

.error-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.error-description {
    font-size: 1.25rem;
    opacity: 0.9;
    line-height: 1.6;
    max-width: 600px;
    margin: 0 auto;
}

.error-suggestions {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 3rem;
    backdrop-filter: blur(10px);
}

.error-suggestions h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 2rem;
    text-align: center;
}

.suggestions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.suggestion-item {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
}

.suggestion-item:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-5px);
}

.suggestion-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 1.5rem;
}

.suggestion-item h4 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.suggestion-item p {
    opacity: 0.9;
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.search-form {
    max-width: 300px;
    margin: 0 auto;
}

.search-form .form-control {
    background: rgba(255, 255, 255, 0.9);
    border: none;
    color: #333;
}

.search-form .form-control::placeholder {
    color: #666;
}

.popular-links {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 3rem;
    backdrop-filter: blur(10px);
}

.popular-links h4 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    text-align: center;
}

.links-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.popular-link {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
}

.popular-link:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    text-decoration: none;
    transform: translateY(-3px);
}

.popular-link i {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.popular-link span {
    font-size: 0.875rem;
    font-weight: 500;
}

.help-section {
    margin-top: 2rem;
}

.help-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    display: flex;
    align-items: center;
    backdrop-filter: blur(10px);
}

.help-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 2rem;
    flex-shrink: 0;
    font-size: 2rem;
}

.help-content h4 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.help-content p {
    opacity: 0.9;
    margin-bottom: 1rem;
    line-height: 1.5;
}

.help-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn {
    border-radius: 25px;
    padding: 0.5rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
}

.btn-primary:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    color: white;
    transform: translateY(-2px);
}

.btn-outline-primary {
    background: transparent;
    border: 2px solid rgba(255, 255, 255, 0.5);
    color: white;
}

.btn-outline-primary:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.7);
    color: white;
    transform: translateY(-2px);
}

.btn-outline-secondary {
    background: transparent;
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: rgba(255, 255, 255, 0.8);
}

.btn-outline-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
    color: white;
    transform: translateY(-2px);
}

@media (max-width: 768px) {
    .error-number {
        font-size: 5rem;
    }
    
    .error-title {
        font-size: 2rem;
    }
    
    .error-description {
        font-size: 1rem;
    }
    
    .suggestions-grid {
        grid-template-columns: 1fr;
    }
    
    .links-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .help-card {
        flex-direction: column;
        text-align: center;
    }
    
    .help-icon {
        margin-right: 0;
        margin-bottom: 1rem;
    }
    
    .help-buttons {
        justify-content: center;
    }
}
</style>
@endpush

@push('scripts')
<script>
// Add some interactive elements
document.addEventListener('DOMContentLoaded', function() {
    // Add click animation to suggestion items
    const suggestionItems = document.querySelectorAll('.suggestion-item');
    suggestionItems.forEach(item => {
        item.addEventListener('click', function() {
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });
    });
    
    // Auto-focus search input
    const searchInput = document.querySelector('.search-form input');
    if (searchInput) {
        searchInput.focus();
    }
});

// Track 404 errors for analytics
if (typeof gtag !== 'undefined') {
    gtag('event', 'page_view', {
        'page_title': '404 Error',
        'page_location': window.location.href,
        'custom_map': {'custom_parameter_1': 'error_page'}
    });
}
</script>
@endpush
